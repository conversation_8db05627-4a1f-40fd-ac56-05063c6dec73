<svg width="388" height="172" viewBox="0 0 388 172" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_429_8950)">
<rect width="388" height="172" rx="16" fill="url(#paint0_linear_429_8950)"/>
</g>
<defs>
<filter id="filter0_ii_429_8950" x="0" y="0" width="388" height="176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_429_8950"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_429_8950" result="effect2_innerShadow_429_8950"/>
</filter>
<linearGradient id="paint0_linear_429_8950" x1="61.4333" y1="24.3667" x2="138.573" y2="240.31" gradientUnits="userSpaceOnUse">
<stop stop-color="#B6947E"/>
<stop offset="0.2" stop-color="#8F6959"/>
<stop offset="0.475" stop-color="#F8DAC8"/>
<stop offset="0.67" stop-color="#AC836E"/>
<stop offset="0.83" stop-color="#B6947E"/>
<stop offset="1" stop-color="#F8DCCB"/>
</linearGradient>
</defs>
</svg>
