<svg width="348" height="195" viewBox="0 0 348 195" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_437_7427)">
<rect width="348" height="195" rx="16" fill="url(#paint0_linear_437_7427)"/>
</g>
<defs>
<filter id="filter0_i_437_7427" x="0" y="0" width="348" height="199" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_437_7427"/>
</filter>
<linearGradient id="paint0_linear_437_7427" x1="8.70001" y1="4.0625" x2="154.052" y2="291.629" gradientUnits="userSpaceOnUse">
<stop stop-color="#7A96AC"/>
<stop offset="0.18" stop-color="#EAEFF3"/>
<stop offset="0.315" stop-color="#C2D4E1"/>
<stop offset="0.491919" stop-color="white"/>
<stop offset="0.615" stop-color="#D4DEE5"/>
<stop offset="0.785" stop-color="#ABBDC8"/>
<stop offset="0.955" stop-color="#BCCAD7"/>
</linearGradient>
</defs>
</svg>
