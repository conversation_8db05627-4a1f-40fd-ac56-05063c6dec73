<svg width="388" height="173" viewBox="0 0 388 173" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_458_4612)">
<rect y="0.5" width="388" height="172" rx="20" fill="url(#paint0_linear_458_4612)"/>
</g>
<defs>
<filter id="filter0_i_458_4612" x="0" y="0.5" width="388" height="176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_458_4612"/>
</filter>
<linearGradient id="paint0_linear_458_4612" x1="51.7333" y1="30.6" x2="140.775" y2="233.839" gradientUnits="userSpaceOnUse">
<stop stop-color="#F2A545"/>
<stop offset="0.325272" stop-color="#FBE67B"/>
<stop offset="0.535488" stop-color="#FCFBE7"/>
<stop offset="0.769917" stop-color="#F7D14E"/>
<stop offset="1" stop-color="#D4A041"/>
</linearGradient>
</defs>
</svg>
