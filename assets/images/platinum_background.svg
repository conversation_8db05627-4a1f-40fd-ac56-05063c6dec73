<svg width="388" height="172" viewBox="0 0 388 172" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_458_4638)">
<rect width="388" height="172" rx="16" fill="url(#paint0_linear_458_4638)"/>
</g>
<defs>
<filter id="filter0_i_458_4638" x="0" y="0" width="388" height="176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_458_4638"/>
</filter>
<linearGradient id="paint0_linear_458_4638" x1="287.767" y1="159.1" x2="212.555" y2="-42.7769" gradientUnits="userSpaceOnUse">
<stop stop-color="#A8A8A6"/>
<stop offset="0.451645" stop-color="#979797"/>
<stop offset="0.665" stop-color="#F9F8F6"/>
<stop offset="0.825" stop-color="#D4D4D4"/>
<stop offset="1" stop-color="#7F7F7F"/>
</linearGradient>
</defs>
</svg>
