<svg width="388" height="172" viewBox="0 0 388 172" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_429_9779)">
<rect width="388" height="172" rx="16" fill="url(#paint0_linear_429_9779)"/>
</g>
<defs>
<filter id="filter0_i_429_9779" x="0" y="0" width="388" height="176" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_429_9779"/>
</filter>
<linearGradient id="paint0_linear_429_9779" x1="312.017" y1="7.16666" x2="227.577" y2="246.567" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDFFFE"/>
<stop offset="0.23" stop-color="#7ABBAC"/>
<stop offset="0.39" stop-color="#B1FFEF"/>
<stop offset="0.535" stop-color="#8AD2C3"/>
<stop offset="0.745" stop-color="#CFFEF4"/>
<stop offset="0.915" stop-color="#6CA196"/>
<stop offset="1" stop-color="#35544E"/>
</linearGradient>
</defs>
</svg>
