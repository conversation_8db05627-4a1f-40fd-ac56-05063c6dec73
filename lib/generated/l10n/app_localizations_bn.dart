// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Bengali Bangla (`bn`).
class AppLocalizationsBn extends AppLocalizations {
  AppLocalizationsBn([String locale = 'bn']) : super(locale);

  @override
  String get appTitle => 'ভেজ মুভ';

  @override
  String get home => 'হোম';

  @override
  String get categories => 'ক্যাটেগরিস';

  @override
  String get search => 'সার্চ';

  @override
  String get cart => 'কার্ট';

  @override
  String get profile => 'প্রোফাইল';
}
