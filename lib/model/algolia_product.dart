import 'dart:convert';

AlgoliaProduct algoliaProductFromJson(String str) =>
    AlgoliaProduct.fromJson(json.decode(str));

String algoliaProductToJson(AlgoliaProduct data) => json.encode(data.toJson());

class AlgoliaProduct {
  final int createdAt;
  final int updatedAt;
  final String objectID;
  final int productId;
  final String name;
  final String description;
  final String slug;
  final String barcode;
  final int categoryId;
  final String categoryName;
  final String categorySlug;
  final bool active;
  final String? thumbnailUrl;
  final int weight;
  final String weightUnit;
  final String searchableText;

  AlgoliaProduct({
    required this.createdAt,
    required this.updatedAt,
    required this.objectID,
    required this.productId,
    required this.name,
    required this.description,
    required this.slug,
    required this.barcode,
    required this.categoryId,
    required this.categoryName,
    required this.categorySlug,
    required this.active,
    this.thumbnailUrl,
    required this.weight,
    required this.weightUnit,
    required this.searchableText,
  });

  AlgoliaProduct copyWith({
    int? createdAt,
    int? updatedAt,
    String? objectID,
    int? productId,
    String? name,
    String? description,
    String? slug,
    String? barcode,
    int? categoryId,
    String? categoryName,
    String? categorySlug,
    bool? active,
    String? thumbnailUrl,
    int? weight,
    String? weightUnit,
    String? searchableText,
  }) =>
      AlgoliaProduct(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        objectID: objectID ?? this.objectID,
        productId: productId ?? this.productId,
        name: name ?? this.name,
        description: description ?? this.description,
        slug: slug ?? this.slug,
        barcode: barcode ?? this.barcode,
        categoryId: categoryId ?? this.categoryId,
        categoryName: categoryName ?? this.categoryName,
        categorySlug: categorySlug ?? this.categorySlug,
        active: active ?? this.active,
        thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
        weight: weight ?? this.weight,
        weightUnit: weightUnit ?? this.weightUnit,
        searchableText: searchableText ?? this.searchableText,
      );

  factory AlgoliaProduct.fromJson(Map<String, dynamic> json) => AlgoliaProduct(
        createdAt: json["createdAt"],
        updatedAt: json["updatedAt"],
        objectID: json["objectID"],
        productId: json["productId"],
        name: json["name"],
        description: json["description"],
        slug: json["slug"],
        barcode: json["barcode"],
        categoryId: json["categoryId"],
        categoryName: json["categoryName"],
        categorySlug: json["categorySlug"],
        active: json["active"],
        thumbnailUrl: json["thumbnailUrl"],
        weight: json["weight"],
        weightUnit: json["weightUnit"],
        searchableText: json["searchableText"],
      );

  Map<String, dynamic> toJson() => {
        "createdAt": createdAt,
        "updatedAt": updatedAt,
        "objectID": objectID,
        "productId": productId,
        "name": name,
        "description": description,
        "slug": slug,
        "barcode": barcode,
        "categoryId": categoryId,
        "categoryName": categoryName,
        "categorySlug": categorySlug,
        "active": active,
        "thumbnailUrl": thumbnailUrl,
        "weight": weight,
        "weightUnit": weightUnit,
        "searchableText": searchableText,
      };
}
