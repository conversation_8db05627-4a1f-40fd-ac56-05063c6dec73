// To parse this JSON data, do
//
//     final product = productFromJson(jsonString);

import 'dart:convert';
import 'package:flutter/foundation.dart' as flutter;
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/model/media.dart';
import 'package:vegmove_ecommerce/model/product_attribute.dart';
import 'package:vegmove_ecommerce/model/product_policy.dart';
import 'package:vegmove_ecommerce/model/product_translation.dart';
import 'package:vegmove_ecommerce/model/product_variation.dart';

Product productFromJson(String str) => Product.fromJson(json.decode(str));

String productToJson(Product data) => json.encode(data.toJson());

class Product {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String barcode;
  final String name;
  final String description;
  final Map<String, dynamic>? highlights;
  final Map<String, dynamic>? information;
  final int categoryId;
  final int weight;
  final WeightUnit weightUnit;
  final int length;
  final int width;
  final int height;
  final int gstPercentage;
  final int? supplierId;
  final String? slug;
  final double? discountValue;
  final AmountType? discountType;
  final bool hasVariations;
  final List<Media> media;
  final int? thumbnailId;
  final Media? thumbnail;
  final Category category;
  final List<ProductTranslation> productTranslation;
  final List<ProductPolicy> productPolicies;
  final List<ProductAttribute>? attributes;
  final List<ProductVariation>? variations;
  final ProductVariation? selectedVariation;
  final int quantity;
  final String originalPrice;
  final String price;
  final bool inStock;
  final int? maxCartQuantity;
  final List<Product>? relatedProducts;

  Product({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.barcode,
    required this.name,
    required this.description,
    this.highlights,
    this.information,
    required this.categoryId,
    required this.weight,
    required this.weightUnit,
    required this.length,
    required this.width,
    required this.height,
    required this.gstPercentage,
    this.supplierId,
    this.slug,
    this.discountValue,
    this.discountType,
    this.hasVariations = false,
    required this.media,
    this.thumbnail,
    this.thumbnailId,
    required this.category,
    required this.productTranslation,
    this.productPolicies = const [],
    this.attributes,
    this.variations,
    this.selectedVariation,
    required this.quantity,
    required this.originalPrice,
    required this.price,
    required this.inStock,
    this.relatedProducts,
    this.maxCartQuantity,
  });

  Product copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? barcode,
    String? name,
    String? description,
    Map<String, dynamic>? highlights,
    Map<String, dynamic>? information,
    int? categoryId,
    int? weight,
    WeightUnit? weightUnit,
    int? length,
    int? width,
    int? height,
    int? gstPercentage,
    int? supplierId,
    String? slug,
    double? discountValue,
    AmountType? discountType,
    bool? hasVariations,
    List<Media>? media,
    int? thumbnailId,
    Media? thumbnail,
    Category? category,
    List<ProductTranslation>? productTranslation,
    List<ProductPolicy>? productPolicies,
    List<ProductAttribute>? attributes,
    List<ProductVariation>? variations,
    ProductVariation? selectedVariation,
    int? quantity,
    String? originalPrice,
    String? price,
    bool? inStock,
    List<Product>? relatedProducts,
    int? maxCartQuantity,
  }) =>
      Product(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        barcode: barcode ?? this.barcode,
        name: name ?? this.name,
        description: description ?? this.description,
        highlights: highlights ?? this.highlights,
        information: information ?? this.information,
        categoryId: categoryId ?? this.categoryId,
        weight: weight ?? this.weight,
        weightUnit: weightUnit ?? this.weightUnit,
        length: length ?? this.length,
        width: width ?? this.width,
        height: height ?? this.height,
        gstPercentage: gstPercentage ?? this.gstPercentage,
        supplierId: supplierId ?? this.supplierId,
        slug: slug ?? this.slug,
        discountValue: discountValue ?? this.discountValue,
        discountType: discountType ?? this.discountType,
        hasVariations: hasVariations ?? this.hasVariations,
        thumbnailId: thumbnailId ?? this.thumbnailId,
        media: media ?? this.media,
        thumbnail: thumbnail ?? this.thumbnail,
        category: category ?? this.category,
        productTranslation: productTranslation ?? this.productTranslation,
        productPolicies: productPolicies ?? this.productPolicies,
        attributes: attributes ?? this.attributes,
        variations: variations ?? this.variations,
        selectedVariation: selectedVariation ?? this.selectedVariation,
        quantity: quantity ?? this.quantity,
        originalPrice: originalPrice ?? this.originalPrice,
        price: price ?? this.price,
        inStock: inStock ?? this.inStock,
        relatedProducts: relatedProducts ?? this.relatedProducts,
        maxCartQuantity: maxCartQuantity ?? this.maxCartQuantity,
      );

  factory Product.fromJson(Map<String, dynamic> json) {
    try {
      flutter.debugPrint('Product.fromJson: ${json.keys}');

      return Product(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        barcode: json['barcode'],
        name: json['name'],
        description: json['description'],
        highlights: json['highlights'] != null
            ? Map<String, dynamic>.from(json['highlights'])
            : null,
        information: json['information'] != null
            ? Map<String, dynamic>.from(json['information'])
            : null,
        categoryId: json['categoryId'],
        weight: json['weight'],
        weightUnit: WeightUnit.values.byName(json['weightUnit']),
        length: json['length'],
        width: json['width'],
        height: json['height'],
        gstPercentage: json['gstPercentage'],
        supplierId: json['supplierId'],
        slug: json['slug'],
        discountValue: json['discountValue'] != null
            ? json['discountValue'] is int
                ? (json['discountValue'] as int).toDouble()
                : json['discountValue']
            : null,
        discountType: json['discountType'] != null
            ? AmountType.values.byName(json['discountType'])
            : null,
        hasVariations: json['hasVariations'] ?? false,
        media: json['media'] != null
            ? List<Media>.from(json['media'].map((x) => Media.fromJson(x)))
            : [],
        thumbnailId: json['thumbnailId'],
        thumbnail: json['thumbnail'] != null
            ? Media.fromJson(json['thumbnail'])
            : null,
        category: Category.fromJson(json['category']),
        productTranslation: json['productTranslation'] != null
            ? List<ProductTranslation>.from(json['productTranslation']
                .map((x) => ProductTranslation.fromJson(x)))
            : [],
        productPolicies: json['productPolicies'] != null
            ? List<ProductPolicy>.from(
                json['productPolicies'].map((x) => ProductPolicy.fromJson(x)))
            : [],
        attributes: json['attributes'] != null
            ? List<ProductAttribute>.from(
                json['attributes'].map((x) => ProductAttribute.fromJson(x)))
            : null,
        variations: json['variations'] != null
            ? List<ProductVariation>.from(
                json['variations'].map((x) => ProductVariation.fromJson(x)))
            : null,
        selectedVariation: json['selectedVariation'] != null
            ? ProductVariation.fromJson(json['selectedVariation'])
            : null,
        quantity: json['quantity'] ?? 0,
        originalPrice: json['originalPrice'] ?? '0.00',
        price: json['price'] ?? '0.00',
        inStock: json['inStock'] ?? false,
        relatedProducts: json['relatedProducts'] != null
            ? List<Product>.from(
                json['relatedProducts'].map((x) => Product.fromJson(x)))
            : null,
        maxCartQuantity: json['maxCartQuantity'],
      );
    } catch (e, stackTrace) {
      flutter.debugPrint('Error parsing Product: $e');
      flutter.debugPrint('Stack trace: $stackTrace');
      flutter.debugPrint('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'barcode': barcode,
        'name': name,
        'description': description,
        'highlights': highlights,
        'information': information,
        'categoryId': categoryId,
        'weight': weight,
        'weightUnit': weightUnit.name,
        'length': length,
        'width': width,
        'height': height,
        'gstPercentage': gstPercentage,
        'supplierId': supplierId,
        'slug': slug,
        'discountValue': discountValue,
        'discountType': discountType?.name,
        'hasVariations': hasVariations,
        'media': List<dynamic>.from(media.map((x) => x.toJson())),
        'thumbnailId': thumbnailId,
        'thumbnail': thumbnail?.toJson(),
        'category': category.toJson(),
        'productTranslation':
            List<dynamic>.from(productTranslation.map((x) => x.toJson())),
        'productPolicies':
            List<dynamic>.from(productPolicies.map((x) => x.toJson())),
        'attributes': attributes != null
            ? List<dynamic>.from(attributes!.map((x) => x.toJson()))
            : null,
        'variations': variations != null
            ? List<dynamic>.from(variations!.map((x) => x.toJson()))
            : null,
        'selectedVariation': selectedVariation?.toJson(),
        'quantity': quantity,
        'originalPrice': originalPrice,
        'price': price,
        'inStock': inStock,
        'relatedProducts': relatedProducts != null
            ? List<dynamic>.from(relatedProducts!.map((x) => x.toJson()))
            : null,
        'maxCartQuantity': maxCartQuantity,
      };
}
