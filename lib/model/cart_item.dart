// To parse this JSON data, do
//
//     final cartItem = cartItemFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/product.dart';
import 'package:vegmove_ecommerce/model/product_variation.dart';

CartItem cartItemFromJson(String str) => CartItem.fromJson(json.decode(str));

String cartItemToJson(CartItem data) => json.encode(data.toJson());

class CartItem {
  final Product product;
  final String itemTotal;
  final String originalTotal;
  final String gstAmount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final int userId;
  final int productId;
  final int quantity;
  final int? variationId;
  final ProductVariation? variation;

  CartItem({
    required this.product,
    required this.itemTotal,
    required this.originalTotal,
    required this.gstAmount,
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.userId,
    required this.productId,
    required this.quantity,
    this.variationId,
    this.variation,
  });

  CartItem copyWith({
    Product? product,
    String? itemTotal,
    String? originalTotal,
    String? gstAmount,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    int? userId,
    int? productId,
    int? quantity,
    int? variationId,
    ProductVariation? variation,
  }) =>
      CartItem(
        product: product ?? this.product,
        itemTotal: itemTotal ?? this.itemTotal,
        originalTotal: originalTotal ?? this.originalTotal,
        gstAmount: gstAmount ?? this.gstAmount,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        userId: userId ?? this.userId,
        productId: productId ?? this.productId,
        quantity: quantity ?? this.quantity,
        variationId: variationId ?? this.variationId,
        variation: variation ?? this.variation,
      );

  factory CartItem.fromJson(Map<String, dynamic> json) => CartItem(
        product: Product.fromJson(json['product']),
        itemTotal: json['itemTotal'],
        originalTotal: json['originalTotal'] ?? '0.00',
        gstAmount: json['gstAmount'] ?? '0.00',
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        userId: json['userId'],
        productId: json['productId'],
        quantity: json['quantity'],
        variationId: json['variationId'],
        variation: json['variation'] != null
            ? ProductVariation.fromJson(json['variation'])
            : null,
      );

  Map<String, dynamic> toJson() => {
        'product': product.toJson(),
        'itemTotal': itemTotal,
        'originalTotal': originalTotal,
        'gstAmount': gstAmount,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'userId': userId,
        'productId': productId,
        'quantity': quantity,
        'variationId': variationId,
        'variation': variation?.toJson(),
      };
}
