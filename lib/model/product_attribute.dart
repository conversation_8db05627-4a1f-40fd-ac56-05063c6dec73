// To parse this JSON data, do
//
//     final productAttribute = productAttributeFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/product_attribute_option.dart';

ProductAttribute productAttributeFromJson(String str) =>
    ProductAttribute.fromJson(json.decode(str));

String productAttributeToJson(ProductAttribute data) =>
    json.encode(data.toJson());

class ProductAttribute {
  final int id;
  final String name;
  final List<ProductAttributeOption> options;

  ProductAttribute({
    required this.id,
    required this.name,
    required this.options,
  });

  ProductAttribute copyWith({
    int? id,
    String? name,
    List<ProductAttributeOption>? options,
  }) =>
      ProductAttribute(
        id: id ?? this.id,
        name: name ?? this.name,
        options: options ?? this.options,
      );

  factory ProductAttribute.fromJson(Map<String, dynamic> json) =>
      ProductAttribute(
        id: json["id"],
        name: json["name"],
        options: List<ProductAttributeOption>.from(
            json["options"].map((x) => ProductAttributeOption.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "options": List<dynamic>.from(options.map((x) => x.toJson())),
      };
}
