// To parse this JSON data, do
//
//     final coupon = couponFromJson(jsonString);

import 'dart:convert';

import 'package:vegmove_ecommerce/model/enums.dart';

Coupon couponFromJson(String str) => Coupon.fromJson(json.decode(str));

String couponToJson(Coupon data) => json.encode(data.toJson());

class Coupon {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String code;
  final double discountValue;
  final AmountType discountType;
  final double? minOrderValue;
  final double? maxDiscount;
  final int? usageLimit;
  final int? perUserLimit;
  final DateTime? expiresAt;
  final bool isActive;

  Coupon({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.code,
    required this.discountValue,
    required this.discountType,
    this.minOrderValue,
    this.maxDiscount,
    this.usageLimit,
    this.perUserLimit,
    this.expiresAt,
    required this.isActive,
  });

  Coupon copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? code,
    double? discountValue,
    AmountType? discountType,
    double? minOrderValue,
    double? maxDiscount,
    int? usageLimit,
    int? perUserLimit,
    DateTime? expiresAt,
    bool? isActive,
  }) =>
      Coupon(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        code: code ?? this.code,
        discountValue: discountValue ?? this.discountValue,
        discountType: discountType ?? this.discountType,
        minOrderValue: minOrderValue ?? this.minOrderValue,
        maxDiscount: maxDiscount ?? this.maxDiscount,
        usageLimit: usageLimit ?? this.usageLimit,
        perUserLimit: perUserLimit ?? this.perUserLimit,
        expiresAt: expiresAt ?? this.expiresAt,
        isActive: isActive ?? this.isActive,
      );

  factory Coupon.fromJson(Map<String, dynamic> json) => Coupon(
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        id: json["id"],
        code: json["code"],
        discountValue: json["discountValue"] is int
            ? (json["discountValue"] as int).toDouble()
            : json["discountValue"],
        discountType: AmountType.values.byName(json["discountType"]),
        minOrderValue: json["minOrderValue"] != null
            ? (json["minOrderValue"] is int
                ? (json["minOrderValue"] as int).toDouble()
                : json["minOrderValue"])
            : null,
        maxDiscount: json["maxDiscount"] != null
            ? (json["maxDiscount"] is int
                ? (json["maxDiscount"] as int).toDouble()
                : json["maxDiscount"])
            : null,
        usageLimit: json["usageLimit"],
        perUserLimit: json["perUserLimit"],
        expiresAt: json["expiresAt"] != null
            ? DateTime.parse(json["expiresAt"])
            : null,
        isActive: json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "id": id,
        "code": code,
        "discountValue": discountValue,
        "discountType": discountType.name,
        "minOrderValue": minOrderValue,
        "maxDiscount": maxDiscount,
        "usageLimit": usageLimit,
        "perUserLimit": perUserLimit,
        "expiresAt": expiresAt?.toIso8601String(),
        "isActive": isActive,
      };
}
