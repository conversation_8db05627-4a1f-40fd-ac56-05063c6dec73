// To parse this JSON data, do
//
//     final productVariationOptionMapping = productVariationOptionMappingFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/product_attribute_option.dart';

ProductVariationOptionMapping productVariationOptionMappingFromJson(String str) =>
    ProductVariationOptionMapping.fromJson(json.decode(str));

String productVariationOptionMappingToJson(ProductVariationOptionMapping data) =>
    json.encode(data.toJson());

class ProductVariationOptionMapping {
  final ProductAttributeOption option;

  ProductVariationOptionMapping({
    required this.option,
  });

  ProductVariationOptionMapping copyWith({
    ProductAttributeOption? option,
  }) =>
      ProductVariationOptionMapping(
        option: option ?? this.option,
      );

  factory ProductVariationOptionMapping.fromJson(Map<String, dynamic> json) =>
      ProductVariationOptionMapping(
        option: ProductAttributeOption.fromJson(json["option"]),
      );

  Map<String, dynamic> toJson() => {
        "option": option.toJson(),
      };
}
