// To parse this JSON data, do
//
//     final country = countryFromJson(jsonString);

import 'dart:convert';

List<Country> countriesFromJson(String str) =>
    List<Country>.from(json.decode(str).map((x) => Country.fromJson(x)));

Country countryFromJson(String str) => Country.fromJson(json.decode(str));

String countryToJson(Country data) => json.encode(data.toJson());

class Country {
  final int id;
  final String name;
  final String code;
  final String dialCode;
  final String flagUrl;

  Country({
    required this.id,
    required this.name,
    required this.code,
    required this.dialCode,
    required this.flagUrl,
  });

  Country copyWith({
    int? id,
    String? name,
    String? code,
    String? dialCode,
    String? flagUrl,
  }) =>
      Country(
        id: id ?? this.id,
        name: name ?? this.name,
        code: code ?? this.code,
        dialCode: dialCode ?? this.dialCode,
        flagUrl: flagUrl ?? this.flagUrl,
      );

  factory Country.fromJson(Map<String, dynamic> json) => Country(
        id: json['id'],
        name: json['name'],
        code: json['code'],
        dialCode: json['dialCode'],
        flagUrl: json['flagUrl'].isEmpty
            ? 'https://flagsapi.com/${json['code'].toUpperCase()}/flat/64.png'
            : json['flagUrl'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'code': code,
        'dialCode': dialCode,
        'flagUrl': flagUrl,
      };
}
