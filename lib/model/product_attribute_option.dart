// To parse this JSON data, do
//
//     final productAttributeOption = productAttributeOptionFromJson(jsonString);

import 'dart:convert';

ProductAttributeOption productAttributeOptionFromJson(String str) =>
    ProductAttributeOption.fromJson(json.decode(str));

String productAttributeOptionToJson(ProductAttributeOption data) =>
    json.encode(data.toJson());

class ProductAttributeOption {
  final int id;
  final String name;
  final String? colorCode;
  final String? imageUrl;
  final ProductAttributeMinimal? attribute;

  ProductAttributeOption({
    required this.id,
    required this.name,
    this.colorCode,
    this.imageUrl,
    this.attribute,
  });

  ProductAttributeOption copyWith({
    int? id,
    String? name,
    String? colorCode,
    String? imageUrl,
    ProductAttributeMinimal? attribute,
  }) =>
      ProductAttributeOption(
        id: id ?? this.id,
        name: name ?? this.name,
        colorCode: colorCode ?? this.colorCode,
        imageUrl: imageUrl ?? this.imageUrl,
        attribute: attribute ?? this.attribute,
      );

  factory ProductAttributeOption.fromJson(Map<String, dynamic> json) =>
      ProductAttributeOption(
        id: json["id"],
        name: json["name"],
        colorCode: json["colorCode"],
        imageUrl: json["imageUrl"],
        attribute: json["attribute"] != null
            ? ProductAttributeMinimal.fromJson(json["attribute"])
            : null,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "colorCode": colorCode,
        "imageUrl": imageUrl,
        "attribute": attribute?.toJson(),
      };
}

class ProductAttributeMinimal {
  final int id;
  final String name;

  ProductAttributeMinimal({
    required this.id,
    required this.name,
  });

  ProductAttributeMinimal copyWith({
    int? id,
    String? name,
  }) =>
      ProductAttributeMinimal(
        id: id ?? this.id,
        name: name ?? this.name,
      );

  factory ProductAttributeMinimal.fromJson(Map<String, dynamic> json) =>
      ProductAttributeMinimal(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}
