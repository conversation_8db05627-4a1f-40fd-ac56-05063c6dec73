// To parse this JSON data, do
//
//     final banner = bannerFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/model/language.dart';
import 'package:vegmove_ecommerce/model/media.dart';

List<Banner> bannersFromJson(String str) =>
    List<Banner>.from(json.decode(str).map((x) => Banner.fromJson(x)));

Banner bannerFromJson(String str) => Banner.fromJson(json.decode(str));

String bannerToJson(Banner data) => json.encode(data.toJson());

class Banner {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final bool active;
  final int mediaId;
  final int? languageId;
  final Language? language;
  final Category? category;
  final int? categoryId;
  final Media media;

  Banner({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.active,
    required this.mediaId,
    this.languageId,
    this.language,
    this.category,
    this.categoryId,
    required this.media,
  });

  Banner copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    bool? active,
    int? mediaId,
    int? languageId,
    Language? language,
    Category? category,
    int? categoryId,
    Media? media,
  }) =>
      Banner(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        active: active ?? this.active,
        mediaId: mediaId ?? this.mediaId,
        languageId: languageId ?? this.languageId,
        language: language ?? this.language,
        category: category ?? this.category,
        categoryId: categoryId ?? this.categoryId,
        media: media ?? this.media,
      );

  factory Banner.fromJson(Map<String, dynamic> json) => Banner(
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        id: json["id"],
        active: json["active"],
        mediaId: json["mediaId"],
        languageId: json["languageId"],
        language: json["language"] != null
            ? Language.fromJson(json["language"])
            : null,
        category: json["category"] != null
            ? Category.fromJson(json["category"])
            : null,
        categoryId: json["categoryId"],
        media: Media.fromJson(json["media"]),
      );

  Map<String, dynamic> toJson() => {
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "id": id,
        "active": active,
        "mediaId": mediaId,
        "languageId": languageId,
        "language": language?.toJson(),
        "category": category?.toJson(),
        "categoryId": categoryId,
        "media": media.toJson(),
      };
}
