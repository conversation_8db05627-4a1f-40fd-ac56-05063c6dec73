// To parse this JSON data, do
//
//     final rewardPointTransaction = rewardPointTransactionFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/order.dart';

RewardPointTransaction rewardPointTransactionFromJson(String str) =>
    RewardPointTransaction.fromJson(json.decode(str));

String rewardPointTransactionToJson(RewardPointTransaction data) =>
    json.encode(data.toJson());

class RewardPointTransaction {
  final DateTime createdAt;
  final DateTime updatedAt;
  final String id;
  final String amount;
  final String type; // "CREDIT" or "DEBIT"
  final int userId;
  final int? orderId;
  final String? note;
  final DateTime? expiry;
  final Order? order;

  RewardPointTransaction({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.amount,
    required this.type,
    required this.userId,
    this.orderId,
    this.note,
    this.expiry,
    this.order,
  });

  RewardPointTransaction copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    String? id,
    String? amount,
    String? type,
    int? userId,
    int? orderId,
    String? note,
    DateTime? expiry,
    Order? order,
  }) =>
      RewardPointTransaction(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        amount: amount ?? this.amount,
        type: type ?? this.type,
        userId: userId ?? this.userId,
        orderId: orderId ?? this.orderId,
        note: note ?? this.note,
        expiry: expiry ?? this.expiry,
        order: order ?? this.order,
      );

  factory RewardPointTransaction.fromJson(Map<String, dynamic> json) =>
      RewardPointTransaction(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        amount: json['amount'],
        type: json['type'],
        userId: json['userId'],
        orderId: json['orderId'],
        note: json['note'],
        expiry: json['expiry'] != null ? DateTime.parse(json['expiry']) : null,
        order: json['order'] != null ? Order.fromJson(json['order']) : null,
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'amount': amount,
        'type': type,
        'userId': userId,
        'orderId': orderId,
        'note': note,
        'expiry': expiry?.toIso8601String(),
        'order': order?.toJson(),
      };
}
