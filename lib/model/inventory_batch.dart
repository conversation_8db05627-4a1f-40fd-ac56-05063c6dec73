// To parse this JSON data, do
//
//     final inventoryBatch = inventoryBatchFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/warehouse.dart';

InventoryBatch inventoryBatchFromJson(String str) =>
    InventoryBatch.fromJson(json.decode(str));

String inventoryBatchToJson(InventoryBatch data) => json.encode(data.toJson());

class InventoryBatch {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String name;
  final Warehouse warehouse;

  InventoryBatch({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.name,
    required this.warehouse,
  });

  InventoryBatch copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? name,
    Warehouse? warehouse,
  }) =>
      InventoryBatch(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        name: name ?? this.name,
        warehouse: warehouse ?? this.warehouse,
      );

  factory InventoryBatch.fromJson(Map<String, dynamic> json) => InventoryBatch(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        name: json['name'],
        warehouse: Warehouse.fromJson(json['warehouse']),
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'name': name,
        'warehouse': warehouse.toJson(),
      };
}
