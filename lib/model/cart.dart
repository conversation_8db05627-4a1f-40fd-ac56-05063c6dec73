// To parse this JSON data, do
//
//     final cartResponse = cartResponseFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/cart_item.dart';

Cart cartFromJson(String str) => Cart.fromJson(json.decode(str));

String cartToJson(Cart data) => json.encode(data.toJson());

class Cart {
  final List<CartItem> items;
  final String subtotal;
  final String handlingCharge;
  final String deliveryFee;
  final String? couponCode;
  final bool couponValid;
  final String couponDiscount;
  final bool useRewardPoints;
  final String rewardDiscount;
  final double rewardPointsUsed;
  final String total;
  final bool canOrder;

  Cart({
    required this.items,
    required this.subtotal,
    required this.handlingCharge,
    required this.deliveryFee,
    this.couponCode,
    required this.couponValid,
    required this.couponDiscount,
    required this.useRewardPoints,
    required this.rewardDiscount,
    required this.rewardPointsUsed,
    required this.total,
    required this.canOrder,
  });

  Cart copyWith({
    List<CartItem>? items,
    String? subtotal,
    String? handlingCharge,
    String? deliveryFee,
    String? couponCode,
    bool? couponValid,
    String? couponDiscount,
    bool? useRewardPoints,
    String? rewardDiscount,
    double? rewardPointsUsed,
    String? total,
    bool? canOrder,
  }) =>
      Cart(
        items: items ?? this.items,
        subtotal: subtotal ?? this.subtotal,
        handlingCharge: handlingCharge ?? this.handlingCharge,
        deliveryFee: deliveryFee ?? this.deliveryFee,
        couponCode: couponCode ?? this.couponCode,
        couponValid: couponValid ?? this.couponValid,
        couponDiscount: couponDiscount ?? this.couponDiscount,
        useRewardPoints: useRewardPoints ?? this.useRewardPoints,
        rewardDiscount: rewardDiscount ?? this.rewardDiscount,
        rewardPointsUsed: rewardPointsUsed ?? this.rewardPointsUsed,
        total: total ?? this.total,
        canOrder: canOrder ?? this.canOrder,
      );

  factory Cart.fromJson(Map<String, dynamic> json) => Cart(
        items:
            List<CartItem>.from(json['items'].map((x) => CartItem.fromJson(x))),
        subtotal: json['subtotal'],
        handlingCharge: json['handlingCharge'] ?? '0.00',
        deliveryFee: json['deliveryFee'] ?? '0.00',
        couponCode: json['couponCode'],
        couponValid: json['couponValid'] ?? false,
        couponDiscount: json['couponDiscount'],
        useRewardPoints: json['useRewardPoints'],
        rewardDiscount: json['rewardDiscount'],
        rewardPointsUsed: json['rewardPointsUsed'] is int
            ? (json['rewardPointsUsed'] as int).toDouble()
            : json['rewardPointsUsed'],
        total: json['total'],
        canOrder: json['canOrder'] ?? true,
      );

  Map<String, dynamic> toJson() => {
        'items': List<dynamic>.from(items.map((x) => x.toJson())),
        'subtotal': subtotal,
        'handlingCharge': handlingCharge,
        'deliveryFee': deliveryFee,
        'couponCode': couponCode,
        'couponValid': couponValid,
        'couponDiscount': couponDiscount,
        'useRewardPoints': useRewardPoints,
        'rewardDiscount': rewardDiscount,
        'rewardPointsUsed': rewardPointsUsed,
        'total': total,
        'canOrder': canOrder,
      };
}
