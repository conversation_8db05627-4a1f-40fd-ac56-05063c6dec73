// To parse this JSON data, do
//
//     final productTranslation = productTranslationFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/language.dart';

ProductTranslation productTranslationFromJson(String str) =>
    ProductTranslation.fromJson(json.decode(str));

String productTranslationToJson(ProductTranslation data) =>
    json.encode(data.toJson());

class ProductTranslation {
  final int id;
  final int languageId;
  final Language language;
  final String name;
  final String description;
  final int productId;

  ProductTranslation({
    required this.id,
    required this.languageId,
    required this.language,
    required this.name,
    required this.description,
    required this.productId,
  });

  ProductTranslation copyWith({
    int? id,
    int? languageId,
    Language? language,
    String? name,
    String? description,
    int? productId,
  }) =>
      ProductTranslation(
        id: id ?? this.id,
        languageId: languageId ?? this.languageId,
        language: language ?? this.language,
        name: name ?? this.name,
        description: description ?? this.description,
        productId: productId ?? this.productId,
      );

  factory ProductTranslation.fromJson(Map<String, dynamic> json) =>
      ProductTranslation(
        id: json['id'],
        languageId: json['languageId'],
        language: Language.fromJson(json['language']),
        name: json['name'],
        description: json['description'],
        productId: json['productId'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'languageId': languageId,
        'language': language.toJson(),
        'name': name,
        'description': description,
        'productId': productId,
      };
}
