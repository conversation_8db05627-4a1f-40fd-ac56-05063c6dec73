// To parse this JSON data, do
//
//     final language = languageFrom<PERSON>son(jsonString);

import 'dart:convert';

List<Language> languagesFromJson(String str) =>
    List<Language>.from(json.decode(str).map((x) => Language.fromJson(x)));

Language languageFromJson(String str) => Language.fromJson(json.decode(str));

String languageToJson(Language data) => json.encode(data.toJson());

class Language {
  final int id;
  final String name;
  final String code;
  final String flagUrl;

  Language({
    required this.id,
    required this.name,
    required this.code,
    required this.flagUrl,
  });

  Language copyWith({
    int? id,
    String? name,
    String? code,
    String? flagUrl,
  }) =>
      Language(
        id: id ?? this.id,
        name: name ?? this.name,
        code: code ?? this.code,
        flagUrl: flagUrl ?? this.flagUrl,
      );

  factory Language.fromJson(Map<String, dynamic> json) => Language(
        id: json["id"],
        name: json["name"],
        code: json["code"],
        flagUrl: json["flagUrl"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "flagUrl": flagUrl,
      };
}
