// To parse this JSON data, do
//
//     final homeSection = homeSectionFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/model/language.dart';
import 'package:vegmove_ecommerce/model/product.dart';
import 'package:vegmove_ecommerce/model/warehouse.dart';

List<HomeSection> homeSectionsFromJson(String str) => List<HomeSection>.from(
    json.decode(str).map((x) => HomeSection.fromJson(x)));

HomeSection homeSectionFromJson(String str) =>
    HomeSection.fromJson(json.decode(str));

String homeSectionToJson(HomeSection data) => json.encode(data.toJson());

enum HomeSectionType { CATEGORY, PRODUCT }

class HomeSection {
  final DateTime createdAt;
  final DateTime updatedAt;
  final String id;
  final String title;
  final bool onlyDiscount;
  final HomeSectionType type;
  final int? warehouseId;
  final Warehouse? warehouse;
  final int displayOrder;
  final List<HomeSectionCategory>? homeSectionCategory;
  final List<HomeSectionTranslation>? translations;
  final List<Category>? categories;
  final List<Product>? products;

  HomeSection({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.title,
    required this.onlyDiscount,
    required this.type,
    this.warehouseId,
    this.warehouse,
    required this.displayOrder,
    this.homeSectionCategory,
    this.translations,
    this.categories,
    this.products,
  });

  HomeSection copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    String? id,
    String? title,
    bool? onlyDiscount,
    HomeSectionType? type,
    int? warehouseId,
    Warehouse? warehouse,
    int? displayOrder,
    List<HomeSectionCategory>? homeSectionCategory,
    List<HomeSectionTranslation>? translations,
    List<Category>? categories,
    List<Product>? products,
  }) =>
      HomeSection(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        title: title ?? this.title,
        onlyDiscount: onlyDiscount ?? this.onlyDiscount,
        type: type ?? this.type,
        warehouseId: warehouseId ?? this.warehouseId,
        warehouse: warehouse ?? this.warehouse,
        displayOrder: displayOrder ?? this.displayOrder,
        homeSectionCategory: homeSectionCategory ?? this.homeSectionCategory,
        translations: translations ?? this.translations,
        categories: categories ?? this.categories,
        products: products ?? this.products,
      );

  factory HomeSection.fromJson(Map<String, dynamic> json) => HomeSection(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        title: json['title'],
        onlyDiscount: json['onlyDiscount'],
        type: HomeSectionType.values.byName(json['type']),
        warehouseId: json['warehouseId'],
        warehouse: json['warehouse'] != null
            ? Warehouse.fromJson(json['warehouse'])
            : null,
        displayOrder: json['displayOrder'],
        homeSectionCategory: json['homeSectionCategory'] != null
            ? List<HomeSectionCategory>.from(json['homeSectionCategory']
                .map((x) => HomeSectionCategory.fromJson(x)))
            : null,
        translations: json['translations'] != null
            ? List<HomeSectionTranslation>.from(json['translations']
                .map((x) => HomeSectionTranslation.fromJson(x)))
            : null,
        categories: json['categories'] != null
            ? List<Category>.from(
                json['categories'].map((x) => Category.fromJson(x)))
            : null,
        products: json['products'] != null
            ? List<Product>.from(
                json['products'].map((x) => Product.fromJson(x)))
            : null,
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'title': title,
        'onlyDiscount': onlyDiscount,
        'type': type.name,
        'warehouseId': warehouseId,
        'warehouse': warehouse?.toJson(),
        'displayOrder': displayOrder,
        'homeSectionCategory': homeSectionCategory != null
            ? List<dynamic>.from(homeSectionCategory!.map((x) => x.toJson()))
            : null,
        'translations': translations != null
            ? List<dynamic>.from(translations!.map((x) => x.toJson()))
            : null,
        'categories': categories != null
            ? List<dynamic>.from(categories!.map((x) => x.toJson()))
            : null,
        'products': products != null
            ? List<dynamic>.from(products!.map((x) => x.toJson()))
            : null,
      };
}

class HomeSectionCategory {
  final String id;
  final String homeSectionId;
  final int categoryId;
  final Category? category;
  final int displayOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  HomeSectionCategory({
    required this.id,
    required this.homeSectionId,
    required this.categoryId,
    this.category,
    required this.displayOrder,
    required this.createdAt,
    required this.updatedAt,
  });

  HomeSectionCategory copyWith({
    String? id,
    String? homeSectionId,
    int? categoryId,
    Category? category,
    int? displayOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      HomeSectionCategory(
        id: id ?? this.id,
        homeSectionId: homeSectionId ?? this.homeSectionId,
        categoryId: categoryId ?? this.categoryId,
        category: category ?? this.category,
        displayOrder: displayOrder ?? this.displayOrder,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );

  factory HomeSectionCategory.fromJson(Map<String, dynamic> json) =>
      HomeSectionCategory(
        id: json['id'],
        homeSectionId: json['homeSectionId'],
        categoryId: json['categoryId'],
        category: json['category'] != null
            ? Category.fromJson(json['category'])
            : null,
        displayOrder: json['displayOrder'],
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'homeSectionId': homeSectionId,
        'categoryId': categoryId,
        'category': category?.toJson(),
        'displayOrder': displayOrder,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
      };
}

class HomeSectionTranslation {
  final int id;
  final String homeSectionId;
  final int languageId;
  final Language language;
  final String title;

  HomeSectionTranslation({
    required this.id,
    required this.homeSectionId,
    required this.languageId,
    required this.language,
    required this.title,
  });

  HomeSectionTranslation copyWith({
    int? id,
    String? homeSectionId,
    int? languageId,
    Language? language,
    String? title,
  }) =>
      HomeSectionTranslation(
        id: id ?? this.id,
        homeSectionId: homeSectionId ?? this.homeSectionId,
        languageId: languageId ?? this.languageId,
        language: language ?? this.language,
        title: title ?? this.title,
      );

  factory HomeSectionTranslation.fromJson(Map<String, dynamic> json) =>
      HomeSectionTranslation(
        id: json['id'],
        homeSectionId: json['homeSectionId'],
        languageId: json['languageId'],
        language: Language.fromJson(json['language']),
        title: json['title'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'homeSectionId': homeSectionId,
        'languageId': languageId,
        'language': language.toJson(),
        'title': title,
      };
}
