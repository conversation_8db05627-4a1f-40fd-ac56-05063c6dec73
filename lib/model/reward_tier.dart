// To parse this JSON data, do
//
//     final rewardTier = rewardTierFromJson(jsonString);

import 'dart:convert';

RewardTier rewardTierFromJson(String str) =>
    RewardTier.fromJson(json.decode(str));

String rewardTierToJson(RewardTier data) => json.encode(data.toJson());

class RewardTier {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String name;
  final String type;
  final String requiredRollingSpend;
  final double earnPercentage;
  final double redeemPercentage;
  final String maxDiscountValue;
  final String? minOrderAmountForBonus;

  RewardTier({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.name,
    required this.type,
    required this.requiredRollingSpend,
    required this.earnPercentage,
    required this.redeemPercentage,
    required this.maxDiscountValue,
    this.minOrderAmountForBonus,
  });

  RewardTier copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? name,
    String? type,
    String? requiredRollingSpend,
    double? earnPercentage,
    double? redeemPercentage,
    String? maxDiscountValue,
    String? minOrderAmountForBonus,
  }) =>
      RewardTier(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        name: name ?? this.name,
        type: type ?? this.type,
        requiredRollingSpend: requiredRollingSpend ?? this.requiredRollingSpend,
        earnPercentage: earnPercentage ?? this.earnPercentage,
        redeemPercentage: redeemPercentage ?? this.redeemPercentage,
        maxDiscountValue: maxDiscountValue ?? this.maxDiscountValue,
        minOrderAmountForBonus:
            minOrderAmountForBonus ?? this.minOrderAmountForBonus,
      );

  factory RewardTier.fromJson(Map<String, dynamic> json) => RewardTier(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        name: json['name'],
        type: json['type'],
        requiredRollingSpend: json['requiredRollingSpend'],
        earnPercentage: json['earnPercentage'] is int
            ? (json['earnPercentage'] as int).toDouble()
            : json['earnPercentage'],
        redeemPercentage: json['redeemPercentage'] is int
            ? (json['redeemPercentage'] as int).toDouble()
            : json['redeemPercentage'],
        maxDiscountValue: json['maxDiscountValue'],
        minOrderAmountForBonus: json['minOrderAmountForBonus'],
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'name': name,
        'type': type,
        'requiredRollingSpend': requiredRollingSpend,
        'earnPercentage': earnPercentage,
        'redeemPercentage': redeemPercentage,
        'maxDiscountValue': maxDiscountValue,
        'minOrderAmountForBonus': minOrderAmountForBonus,
      };
}
