// To parse this JSON data, do
//
//     final user = user<PERSON>rom<PERSON><PERSON>(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/country.dart';
import 'package:vegmove_ecommerce/model/reward_tier.dart';

User userFromJson(String str) => User.fromJson(json.decode(str));
String userToJson(User data) => json.encode(data.toJson());

class User {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String email;
  final String name;
  final String type;
  final String phone;
  final Country phoneCountry;
  final int countryId;
  final String? profilePicture;
  final String referralCode;
  final List<UserReferral> referrals;
  final RewardTier? rewardTier;
  final int? rewardTierId;
  final int rewardPoints;
  final UserPerformance? performance;
  final int? totalOrders;
  final String? totalOrderPrice;
  final String? averageOrderPrice;

  User({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.email,
    required this.name,
    required this.type,
    required this.phone,
    required this.phoneCountry,
    required this.countryId,
    this.profilePicture,
    required this.referralCode,
    required this.referrals,
    this.rewardTier,
    this.rewardTierId,
    required this.rewardPoints,
    this.performance,
    this.totalOrders,
    this.totalOrderPrice,
    this.averageOrderPrice,
  });

  User copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? email,
    String? name,
    String? type,
    String? phone,
    Country? phoneCountry,
    int? countryId,
    String? profilePicture,
    String? referralCode,
    List<UserReferral>? referrals,
    RewardTier? rewardTier,
    int? rewardTierId,
    int? rewardPoints,
    UserPerformance? performance,
    int? totalOrders,
    String? totalOrderPrice,
    String? averageOrderPrice,
  }) =>
      User(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        email: email ?? this.email,
        name: name ?? this.name,
        type: type ?? this.type,
        phone: phone ?? this.phone,
        phoneCountry: phoneCountry ?? this.phoneCountry,
        countryId: countryId ?? this.countryId,
        profilePicture: profilePicture ?? this.profilePicture,
        referralCode: referralCode ?? this.referralCode,
        referrals: referrals ?? this.referrals,
        rewardTier: rewardTier ?? this.rewardTier,
        rewardTierId: rewardTierId ?? this.rewardTierId,
        rewardPoints: rewardPoints ?? this.rewardPoints,
        performance: performance ?? this.performance,
        totalOrders: totalOrders ?? this.totalOrders,
        totalOrderPrice: totalOrderPrice ?? this.totalOrderPrice,
        averageOrderPrice: averageOrderPrice ?? this.averageOrderPrice,
      );

  factory User.fromJson(Map<String, dynamic> json) => User(
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        id: json["id"],
        email: json["email"],
        name: json["name"],
        type: json["type"],
        phone: json["phone"],
        phoneCountry: Country.fromJson(json["phoneCountry"]),
        countryId: json["countryId"],
        profilePicture: json["profilePicture"],
        referralCode: json["referralCode"] ?? "",
        referrals: json["referrals"] != null
            ? List<UserReferral>.from(
                json["referrals"].map((x) => UserReferral.fromJson(x)))
            : [],
        rewardTier: json["rewardTier"] != null
            ? RewardTier.fromJson(json["rewardTier"])
            : null,
        rewardTierId: json["rewardTierId"],
        rewardPoints: json["rewardPoints"] ?? 0,
        performance: json["performance"] != null
            ? UserPerformance.fromJson(json["performance"])
            : null,
        totalOrders: json["totalOrders"],
        totalOrderPrice: json["totalOrderPrice"],
        averageOrderPrice: json["averageOrderPrice"],
      );

  Map<String, dynamic> toJson() => {
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "id": id,
        "email": email,
        "name": name,
        "type": type,
        "phone": phone,
        "phoneCountry": phoneCountry.toJson(),
        "countryId": countryId,
        "profilePicture": profilePicture,
        "referralCode": referralCode,
        "referrals": List<dynamic>.from(referrals.map((x) => x.toJson())),
        "rewardTier": rewardTier?.toJson(),
        "rewardTierId": rewardTierId,
        "rewardPoints": rewardPoints,
        "performance": performance?.toJson(),
        "totalOrders": totalOrders,
        "totalOrderPrice": totalOrderPrice,
        "averageOrderPrice": averageOrderPrice,
      };
}

class UserReferral {
  final int id;

  UserReferral({
    required this.id,
  });

  UserReferral copyWith({
    int? id,
  }) =>
      UserReferral(
        id: id ?? this.id,
      );

  factory UserReferral.fromJson(Map<String, dynamic> json) => UserReferral(
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
      };
}

class UserPerformance {
  final int totalOrderCount;
  final int avgPickupMins;
  final int avgDeliveryMins;
  final int lateDeliveryCount;
  final int avgLateMins;

  UserPerformance({
    required this.totalOrderCount,
    required this.avgPickupMins,
    required this.avgDeliveryMins,
    required this.lateDeliveryCount,
    required this.avgLateMins,
  });

  UserPerformance copyWith({
    int? totalOrderCount,
    int? avgPickupMins,
    int? avgDeliveryMins,
    int? lateDeliveryCount,
    int? avgLateMins,
  }) =>
      UserPerformance(
        totalOrderCount: totalOrderCount ?? this.totalOrderCount,
        avgPickupMins: avgPickupMins ?? this.avgPickupMins,
        avgDeliveryMins: avgDeliveryMins ?? this.avgDeliveryMins,
        lateDeliveryCount: lateDeliveryCount ?? this.lateDeliveryCount,
        avgLateMins: avgLateMins ?? this.avgLateMins,
      );

  factory UserPerformance.fromJson(Map<String, dynamic> json) =>
      UserPerformance(
        totalOrderCount: json["totalOrderCount"],
        avgPickupMins: json["avgPickupMins"],
        avgDeliveryMins: json["avgDeliveryMins"],
        lateDeliveryCount: json["lateDeliveryCount"],
        avgLateMins: json["avgLateMins"],
      );

  Map<String, dynamic> toJson() => {
        "totalOrderCount": totalOrderCount,
        "avgPickupMins": avgPickupMins,
        "avgDeliveryMins": avgDeliveryMins,
        "lateDeliveryCount": lateDeliveryCount,
        "avgLateMins": avgLateMins,
      };
}
