// To parse this JSON data, do
//
//     final productPolicy = productPolicyFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/product_policy_type.dart';

ProductPolicy productPolicyFromJson(String str) =>
    ProductPolicy.fromJson(json.decode(str));

String productPolicyToJson(ProductPolicy data) => json.encode(data.toJson());

class ProductPolicy {
  final int id;
  final int productId;
  final int productPolicyTypeId;
  final Map<String, dynamic> details;
  final ProductPolicyType productPolicyType;

  ProductPolicy({
    required this.id,
    required this.productId,
    required this.productPolicyTypeId,
    required this.details,
    required this.productPolicyType,
  });

  ProductPolicy copyWith({
    int? id,
    int? productId,
    int? productPolicyTypeId,
    Map<String, dynamic>? details,
    ProductPolicyType? productPolicyType,
  }) =>
      ProductPolicy(
        id: id ?? this.id,
        productId: productId ?? this.productId,
        productPolicyTypeId: productPolicyTypeId ?? this.productPolicyTypeId,
        details: details ?? this.details,
        productPolicyType: productPolicyType ?? this.productPolicyType,
      );

  factory ProductPolicy.fromJson(Map<String, dynamic> json) => ProductPolicy(
        id: json["id"],
        productId: json["productId"],
        productPolicyTypeId: json["productPolicyTypeId"],
        details: Map<String, dynamic>.from(json["details"]),
        productPolicyType: ProductPolicyType.fromJson(json["productPolicyType"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "productId": productId,
        "productPolicyTypeId": productPolicyTypeId,
        "details": details,
        "productPolicyType": productPolicyType.toJson(),
      };
}
