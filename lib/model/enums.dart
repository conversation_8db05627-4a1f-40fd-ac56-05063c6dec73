/// Warehouse types
enum WarehouseType { <PERSON><PERSON><PERSON><PERSON>, <PERSON>UPER }

/// Weight units
enum WeightUnit { GRAM, MI<PERSON><PERSON><PERSON><PERSON>, <PERSON>IE<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> }

/// Amount types for discounts
enum AmountType { FLAT, PERCENTAGE }

/// Order status
enum OrderStatus { PENDING, CONFIRMED, READY, SHIPPED, DELIVERED, CA<PERSON><PERSON>LED }

/// Payment status
enum PaymentStatus { PENDING, PAID, FAILED, REFUNDED }

/// Address types
enum AddressType { BILLING, SHIPPING }

/// Category types
enum CategoryType { COLLECTION, CATEGORY, SEGMENT }

/// Media types
enum MediaType { IMAGE, VIDEO }

/// Inventory transaction types
enum InventoryTransactionType { PURCHASE, SALE, ADJUSTMENT, RETURN }

/// User types
enum UserType { CUSTOMER, ADMIN, WAREHOUSE_STAFF, DELIVERY_PERSON }
