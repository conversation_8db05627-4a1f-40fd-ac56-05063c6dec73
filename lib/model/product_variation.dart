// To parse this JSON data, do
//
//     final productVariation = productVariationFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/media.dart';
import 'package:vegmove_ecommerce/model/product_variation_option_mapping.dart';

ProductVariation productVariationFromJson(String str) =>
    ProductVariation.fromJson(json.decode(str));

String productVariationToJson(ProductVariation data) =>
    json.encode(data.toJson());

class ProductVariation {
  final int id;
  final String barcode;
  final int? quantity;
  final bool? inStock;
  final String? price;
  final String? originalPrice;
  final List<ProductVariationOptionMapping> options;
  final List<Media> media;

  ProductVariation({
    required this.id,
    required this.barcode,
    required this.quantity,
    required this.inStock,
    required this.price,
    required this.originalPrice,
    required this.options,
    required this.media,
  });

  ProductVariation copyWith({
    int? id,
    String? barcode,
    int? quantity,
    bool? inStock,
    String? price,
    String? originalPrice,
    List<ProductVariationOptionMapping>? options,
    List<Media>? media,
  }) =>
      ProductVariation(
        id: id ?? this.id,
        barcode: barcode ?? this.barcode,
        quantity: quantity ?? this.quantity,
        inStock: inStock ?? this.inStock,
        price: price ?? this.price,
        originalPrice: originalPrice ?? this.originalPrice,
        options: options ?? this.options,
        media: media ?? this.media,
      );

  factory ProductVariation.fromJson(Map<String, dynamic> json) =>
      ProductVariation(
        id: json["id"],
        barcode: json["barcode"],
        quantity: json["quantity"] ?? 0,
        inStock: json["inStock"],
        price: json["price"],
        originalPrice: json["originalPrice"],
        options: List<ProductVariationOptionMapping>.from(json["options"]
            .map((x) => ProductVariationOptionMapping.fromJson(x))),
        media: json["media"] != null
            ? List<Media>.from(json["media"].map((x) => Media.fromJson(x)))
            : [],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "barcode": barcode,
        "quantity": quantity,
        "inStock": inStock,
        "price": price,
        "originalPrice": originalPrice,
        "options": List<dynamic>.from(options.map((x) => x.toJson())),
        "media": List<dynamic>.from(media.map((x) => x.toJson())),
      };
}
