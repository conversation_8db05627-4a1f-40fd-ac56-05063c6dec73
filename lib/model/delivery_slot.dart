// To parse this JSON data, do
//
//     final deliverySlot = deliverySlotFromJson(jsonString);

import 'dart:convert';

List<DeliverySlot> deliverySlotsFromJson(String str) =>
    List<DeliverySlot>.from(json.decode(str).map((x) => DeliverySlot.fromJson(x)));

DeliverySlot deliverySlotFromJson(String str) => DeliverySlot.fromJson(json.decode(str));

String deliverySlotToJson(DeliverySlot data) => json.encode(data.toJson());

enum DeliverySlotType { REGULAR, HOLIDAY }

enum Weekday {
  SUNDAY,
  MONDAY,
  TUESDAY,
  WEDNESDAY,
  THURSDAY,
  FRIDAY,
  SATURDAY
}

class DeliverySlot {
  final int id;
  final String? startTime;
  final String? endTime;
  final DateTime? date;
  final Weekday? weekday;
  final DeliverySlotType type;
  final String? reason;

  DeliverySlot({
    required this.id,
    this.startTime,
    this.endTime,
    this.date,
    this.weekday,
    required this.type,
    this.reason,
  });

  DeliverySlot copyWith({
    int? id,
    String? startTime,
    String? endTime,
    DateTime? date,
    Weekday? weekday,
    DeliverySlotType? type,
    String? reason,
  }) =>
      DeliverySlot(
        id: id ?? this.id,
        startTime: startTime ?? this.startTime,
        endTime: endTime ?? this.endTime,
        date: date ?? this.date,
        weekday: weekday ?? this.weekday,
        type: type ?? this.type,
        reason: reason ?? this.reason,
      );

  factory DeliverySlot.fromJson(Map<String, dynamic> json) => DeliverySlot(
        id: json["id"],
        startTime: json["startTime"],
        endTime: json["endTime"],
        date: json["date"] != null ? DateTime.parse(json["date"]) : null,
        weekday: json["weekday"] != null
            ? Weekday.values.byName(json["weekday"])
            : null,
        type: DeliverySlotType.values.byName(json["type"]),
        reason: json["reason"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "startTime": startTime,
        "endTime": endTime,
        "date": date?.toIso8601String(),
        "weekday": weekday?.name,
        "type": type.name,
        "reason": reason,
      };
}
