// To parse this JSON data, do
//
//     final media = mediaFromJson(jsonString);

import 'dart:convert';

Media mediaFromJson(String str) => Media.fromJson(json.decode(str));

String mediaToJson(Media data) => json.encode(data.toJson());

class Media {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final String url;
  final String mediaType;
  final int? variationId;
  final int? productId;

  Media({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.url,
    required this.mediaType,
    this.variationId,
    required this.productId,
  });

  Media copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    String? url,
    String? mediaType,
    int? variationId,
    int? productId,
  }) =>
      Media(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        url: url ?? this.url,
        mediaType: mediaType ?? this.mediaType,
        variationId: variationId ?? this.variationId,
        productId: productId ?? this.productId,
      );

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        id: json["id"],
        url: json["url"],
        mediaType: json["mediaType"],
        variationId: json["variationId"],
        productId: json["productId"],
      );

  Map<String, dynamic> toJson() => {
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "id": id,
        "url": url,
        "mediaType": mediaType,
        "variationId": variationId,
        "productId": productId,
      };
}
