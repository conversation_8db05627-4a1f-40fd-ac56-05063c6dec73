// To parse this JSON data, do
//
//     final deliveryDriverLocation = deliveryDriverLocationFromJson(jsonString);

import 'dart:convert';

DeliveryDriverLocation deliveryDriverLocationFromJson(String str) =>
    DeliveryDriverLocation.fromJson(json.decode(str));

String deliveryDriverLocationToJson(DeliveryDriverLocation data) =>
    json.encode(data.toJson());

class DeliveryDriverLocation {
  final int id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String lat;
  final String long;
  final int driverId;

  DeliveryDriverLocation({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.lat,
    required this.long,
    required this.driverId,
  });

  DeliveryDriverLocation copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? lat,
    String? long,
    int? driverId,
  }) =>
      DeliveryDriverLocation(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        lat: lat ?? this.lat,
        long: long ?? this.long,
        driverId: driverId ?? this.driverId,
      );

  factory DeliveryDriverLocation.fromJson(Map<String, dynamic> json) =>
      DeliveryDriverLocation(
        id: json["id"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        lat: json["lat"],
        long: json["long"],
        driverId: json["driverId"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "lat": lat,
        "long": long,
        "driverId": driverId,
      };
}
