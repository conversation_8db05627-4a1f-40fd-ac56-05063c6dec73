// To parse this JSON data, do
//
//     final inventory = inventoryFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/inventory_batch.dart';
import 'package:vegmove_ecommerce/model/inventory_transaction.dart';
import 'package:vegmove_ecommerce/model/product.dart';

Inventory inventoryFromJson(String str) => Inventory.fromJson(json.decode(str));

String inventoryToJson(Inventory data) => json.encode(data.toJson());

class Inventory {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final int batchId;
  final int productId;
  final Product product;
  final String buyingPrice;
  final String sellingPrice;
  final DateTime? manufactureDate;
  final DateTime? expiryDate;
  final int? supplierId;
  final int quantity;
  final List<InventoryTransaction> inventoryTransactions;
  final InventoryBatch batch;

  Inventory({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.batchId,
    required this.productId,
    required this.product,
    required this.buyingPrice,
    required this.sellingPrice,
    this.manufactureDate,
    this.expiryDate,
    this.supplierId,
    required this.quantity,
    required this.inventoryTransactions,
    required this.batch,
  });

  Inventory copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    int? batchId,
    int? productId,
    Product? product,
    String? buyingPrice,
    String? sellingPrice,
    DateTime? manufactureDate,
    DateTime? expiryDate,
    int? supplierId,
    int? quantity,
    List<InventoryTransaction>? inventoryTransactions,
    InventoryBatch? batch,
  }) =>
      Inventory(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        batchId: batchId ?? this.batchId,
        productId: productId ?? this.productId,
        product: product ?? this.product,
        buyingPrice: buyingPrice ?? this.buyingPrice,
        sellingPrice: sellingPrice ?? this.sellingPrice,
        manufactureDate: manufactureDate ?? this.manufactureDate,
        expiryDate: expiryDate ?? this.expiryDate,
        supplierId: supplierId ?? this.supplierId,
        quantity: quantity ?? this.quantity,
        inventoryTransactions:
            inventoryTransactions ?? this.inventoryTransactions,
        batch: batch ?? this.batch,
      );

  factory Inventory.fromJson(Map<String, dynamic> json) => Inventory(
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
        id: json["id"],
        batchId: json["batchId"],
        productId: json["productId"],
        product: Product.fromJson(json["product"]),
        buyingPrice: json["buyingPrice"],
        sellingPrice: json["sellingPrice"],
        manufactureDate: json["manufactureDate"] != null
            ? DateTime.parse(json["manufactureDate"])
            : null,
        expiryDate: json["expiryDate"] != null
            ? DateTime.parse(json["expiryDate"])
            : null,
        supplierId: json["supplierId"],
        quantity: json["quantity"],
        inventoryTransactions: json["inventoryTransactions"] != null
            ? List<InventoryTransaction>.from(json["inventoryTransactions"]
                .map((x) => InventoryTransaction.fromJson(x)))
            : [],
        batch: InventoryBatch.fromJson(json["batch"]),
      );

  Map<String, dynamic> toJson() => {
        "createdAt": createdAt.toIso8601String(),
        "updatedAt": updatedAt.toIso8601String(),
        "id": id,
        "batchId": batchId,
        "productId": productId,
        "product": product.toJson(),
        "buyingPrice": buyingPrice,
        "sellingPrice": sellingPrice,
        "manufactureDate": manufactureDate?.toIso8601String(),
        "expiryDate": expiryDate?.toIso8601String(),
        "supplierId": supplierId,
        "quantity": quantity,
        "inventoryTransactions":
            List<dynamic>.from(inventoryTransactions.map((x) => x.toJson())),
        "batch": batch.toJson(),
      };
}
