// To parse this JSON data, do
//
//     final productPolicyType = productPolicyTypeFromJson(jsonString);

import 'dart:convert';

ProductPolicyType productPolicyTypeFromJson(String str) =>
    ProductPolicyType.fromJson(json.decode(str));

String productPolicyTypeToJson(ProductPolicyType data) =>
    json.encode(data.toJson());

class ProductPolicyType {
  final int id;
  final String name;
  final String iconUrl;

  ProductPolicyType({
    required this.id,
    required this.name,
    required this.iconUrl,
  });

  ProductPolicyType copyWith({
    int? id,
    String? name,
    String? iconUrl,
  }) =>
      ProductPolicyType(
        id: id ?? this.id,
        name: name ?? this.name,
        iconUrl: iconUrl ?? this.iconUrl,
      );

  factory ProductPolicyType.fromJson(Map<String, dynamic> json) =>
      ProductPolicyType(
        id: json["id"],
        name: json["name"],
        iconUrl: json["icon"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "icon": iconUrl,
      };
}
