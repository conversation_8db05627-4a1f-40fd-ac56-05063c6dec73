// To parse this JSON data, do
//
//     final rewardPointConfig = rewardPointConfigFromJson(jsonString);

import 'dart:convert';

RewardPointConfig rewardPointConfigFromJson(String str) =>
    RewardPointConfig.fromJson(json.decode(str));

String rewardPointConfigToJson(RewardPointConfig data) =>
    json.encode(data.toJson());

class RewardPointConfig {
  final DateTime createdAt;
  final DateTime updatedAt;
  final int id;
  final double minOrderAmountForBonus;
  final int fixedBonusPoints;
  final int referralBonusPoints;
  final bool active;

  RewardPointConfig({
    required this.createdAt,
    required this.updatedAt,
    required this.id,
    required this.minOrderAmountForBonus,
    required this.fixedBonusPoints,
    required this.referralBonusPoints,
    required this.active,
  });

  RewardPointConfig copyWith({
    DateTime? createdAt,
    DateTime? updatedAt,
    int? id,
    double? minOrderAmountForBonus,
    int? fixedBonusPoints,
    int? referralBonusPoints,
    bool? active,
  }) =>
      RewardPointConfig(
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        id: id ?? this.id,
        minOrderAmountForBonus:
            minOrderAmountForBonus ?? this.minOrderAmountForBonus,
        fixedBonusPoints: fixedBonusPoints ?? this.fixedBonusPoints,
        referralBonusPoints: referralBonusPoints ?? this.referralBonusPoints,
        active: active ?? this.active,
      );

  factory RewardPointConfig.fromJson(Map<String, dynamic> json) =>
      RewardPointConfig(
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        id: json['id'],
        minOrderAmountForBonus: json['minOrderAmountForBonus'] is int
            ? (json['minOrderAmountForBonus'] as int).toDouble()
            : double.parse(json['minOrderAmountForBonus'].toString()),
        fixedBonusPoints: json['fixedBonusPoints'],
        referralBonusPoints: json['referralBonusPoints'],
        active: json['active'],
      );

  Map<String, dynamic> toJson() => {
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'id': id,
        'minOrderAmountForBonus': minOrderAmountForBonus,
        'fixedBonusPoints': fixedBonusPoints,
        'referralBonusPoints': referralBonusPoints,
        'active': active,
      };
}
