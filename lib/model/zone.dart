// To parse this JSON data, do
//
//     final zone = zoneFromJson(jsonString);

import 'dart:convert';

Zone zoneFromJson(String str) => Zone.fromJson(json.decode(str));

String zoneToJson(Zone data) => json.encode(data.toJson());

class Zone {
  final int id;
  final String name;
  final List<Coordinate> coordinates;

  Zone({
    required this.id,
    required this.name,
    required this.coordinates,
  });

  Zone copyWith({
    int? id,
    String? name,
    List<Coordinate>? coordinates,
  }) =>
      Zone(
        id: id ?? this.id,
        name: name ?? this.name,
        coordinates: coordinates ?? this.coordinates,
      );

  factory Zone.fromJson(Map<String, dynamic> json) => Zone(
        id: json["id"],
        name: json["name"],
        coordinates: List<Coordinate>.from(
            json["coordinates"].map((x) => Coordinate.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "coordinates": List<dynamic>.from(coordinates.map((x) => x.toJson())),
      };
}

class Coordinate {
  final String lat;
  final String long;

  Coordinate({
    required this.lat,
    required this.long,
  });

  Coordinate copyWith({
    String? lat,
    String? long,
  }) =>
      Coordinate(
        lat: lat ?? this.lat,
        long: long ?? this.long,
      );

  factory Coordinate.fromJson(Map<String, dynamic> json) => Coordinate(
        lat: json["lat"],
        long: json["long"],
      );

  Map<String, dynamic> toJson() => {
        "lat": lat,
        "long": long,
      };
}
