// To parse this JSON data, do
//
//     final inventoryTransaction = inventoryTransactionFromJson(jsonString);

import 'dart:convert';

InventoryTransaction inventoryTransactionFromJson(String str) =>
    InventoryTransaction.fromJson(json.decode(str));

String inventoryTransactionToJson(InventoryTransaction data) =>
    json.encode(data.toJson());

class InventoryTransaction {
  final DateTime createdAt;
  final int id;
  final int inventoryId;
  final int quantity;
  final String type;
  final int? orderId;
  final String? remark;

  InventoryTransaction({
    required this.createdAt,
    required this.id,
    required this.inventoryId,
    required this.quantity,
    required this.type,
    this.orderId,
    this.remark,
  });

  InventoryTransaction copyWith({
    DateTime? createdAt,
    int? id,
    int? inventoryId,
    int? quantity,
    String? type,
    int? orderId,
    String? remark,
  }) =>
      InventoryTransaction(
        createdAt: createdAt ?? this.createdAt,
        id: id ?? this.id,
        inventoryId: inventoryId ?? this.inventoryId,
        quantity: quantity ?? this.quantity,
        type: type ?? this.type,
        orderId: orderId ?? this.orderId,
        remark: remark ?? this.remark,
      );

  factory InventoryTransaction.fromJson(Map<String, dynamic> json) =>
      InventoryTransaction(
        createdAt: DateTime.parse(json["createdAt"]),
        id: json["id"],
        inventoryId: json["inventoryId"],
        quantity: json["quantity"],
        type: json["type"],
        orderId: json["orderId"],
        remark: json["remark"],
      );

  Map<String, dynamic> toJson() => {
        "createdAt": createdAt.toIso8601String(),
        "id": id,
        "inventoryId": inventoryId,
        "quantity": quantity,
        "type": type,
        "orderId": orderId,
        "remark": remark,
      };
}
