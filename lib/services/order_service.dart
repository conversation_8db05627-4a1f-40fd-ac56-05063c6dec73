import 'dart:developer';

import 'package:vegmove_ecommerce/network/dto/order_paginated_response_dto.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/model/order.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class OrderService {
  final ApiClient _api = ApiClient();

  Future<OrderPaginatedResponseDto> getOrders({
    int page = 1,
    int pageSize = 10,
    String? languageCode,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'page': page,
        'pageSize': pageSize,
      };

      if (languageCode != null) {
        queryParams['lang'] = languageCode;
      }

      OrderPaginatedResponseDto result =
          await _api.get<OrderPaginatedResponseDto>(
        'orders',
        queries: queryParams,
        parser: (json) => OrderPaginatedResponseDto.fromJson(json),
      );

      return result;
    } catch (e, stacktrace) {
      log(e.toString());
      log(stacktrace.toString());
      rethrow;
    }
  }

  Future<Order> getOrderById(int id, {String? languageCode}) async {
    try {
      final Map<String, dynamic> queryParams = {};

      if (languageCode != null) {
        queryParams['lang'] = languageCode;
      }

      return _api.get<Order>(
        'orders/$id',
        queries: queryParams,
        parser: (json) => Order.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<Order> createOrder({
    required int shippingAddressId,
    required DateTime deliveryDate,
    required String deliveryStartTime,
    required String deliveryEndTime,
    required double lat,
    required double long,
    required WarehouseType warehouseType,
    String? note,
    String? couponCode,
    bool useRewardPoints = false,
  }) async {
    try {
      return _api.post<Order>(
        'orders',
        data: {
          'shippingAddressId': shippingAddressId,
          'deliveryDate': deliveryDate.toIso8601String(),
          'deliveryStartTime': deliveryStartTime,
          'deliveryEndTime': deliveryEndTime,
          'lat': lat,
          'long': long,
          'warehouseType': warehouseType.name,
          'note': note,
          'couponCode': couponCode,
          'useRewardPoints': useRewardPoints,
        },
        parser: (json) => Order.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<void> cancelOrder(int id) async {
    try {
      await _api.delete<Map<String, dynamic>>(
        'orders/$id',
        parser: (json) => json as Map<String, dynamic>,
      );
    } catch (e) {
      rethrow;
    }
  }
}
