import 'package:flutter/material.dart';
import 'package:vegmove_ecommerce/network/dto/auth_response_dto.dart';
import 'package:vegmove_ecommerce/model/user.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthService {
  final ApiClient _api = ApiClient();

  Future<AuthResponseDto> login(String email, String password) async {
    try {
      final response = await _api.post<AuthResponseDto>(
        'auth/login',
        data: {
          'email': email,
          'password': password,
        },
        parser: (json) => AuthResponseDto.fromJson(json),
      );

      // Save token to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('token', response.token);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<AuthResponseDto> register(
    String name,
    String email,
    String password,
    int countryId, {
    String? referralCode,
  }) async {
    try {
      final data = {
        'name': name,
        'email': email,
        'password': password,
        'countryId': countryId,
      };

      if (referralCode != null && referralCode.isNotEmpty) {
        data['referralCode'] = referralCode;
      }

      final response = await _api.post<AuthResponseDto>(
        'auth/register',
        data: data,
        parser: (json) => AuthResponseDto.fromJson(json),
      );

      // Save token to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('token', response.token);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<User> getCurrentUser() async {
    try {
      return await _api.get<User>(
        'users',
        parser: (json) => User.fromJson(json),
      );
    } catch (e, stacktrace) {
      debugPrint(e.toString());
      debugPrint(stacktrace.toString());
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('token');
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');
      return token != null && token.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  Future<void> updateProfile(Map<String, dynamic> data) async {
    try {
      await _api.patch<Map<String, dynamic>>(
        'users',
        data: data,
        parser: (json) => json,
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<User> updateName(String name) async {
    try {
      return await _api.patch<User>(
        'users/name',
        data: {'name': name},
        parser: (json) => User.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<User> updateEmail(String email) async {
    try {
      return await _api.patch<User>(
        'users/email',
        data: {'email': email},
        parser: (json) => User.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<Map<String, dynamic>> changePassword(
      String currentPassword, String newPassword) async {
    try {
      return await _api.patch<Map<String, dynamic>>(
        'users/password',
        data: {
          'currentPassword': currentPassword,
          'newPassword': newPassword,
        },
        parser: (json) => json,
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<void> updateFirebaseToken(String token) async {
    try {
      await _api.patch<Map<String, dynamic>>(
        'users/firebase',
        data: {'token': token},
        parser: (json) => json,
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> validateReferralCode(String code) async {
    try {
      final response = await _api.get<Map<String, dynamic>>(
        'auth/validate-referral-code/$code',
        parser: (json) => json,
      );
      return response['valid'] ?? false;
    } catch (e) {
      return false;
    }
  }
}
