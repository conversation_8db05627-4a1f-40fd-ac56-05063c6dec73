import 'package:vegmove_ecommerce/network/api_client.dart';
import 'package:vegmove_ecommerce/network/dto/check_zone_response_dto.dart';

/// Service class for zone-related operations
class ZoneService {
  final ApiClient _api = ApiClient();

  /// Check if a location is within any active zone
  ///
  /// [lat] - Latitude of the location to check
  /// [long] - Longitude of the location to check
  ///
  /// Returns a [CheckZoneResponseDto] with a boolean indicating if the location is within a zone
  Future<CheckZoneResponseDto> checkZone({
    required double lat,
    required double long,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'lat': lat,
        'long': long,
      };

      return _api.get<CheckZoneResponseDto>(
        'zones/check',
        queries: queryParams,
        parser: (json) => CheckZoneResponseDto.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }
}
