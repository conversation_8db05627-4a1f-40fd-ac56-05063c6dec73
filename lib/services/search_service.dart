import 'package:flutter/foundation.dart';
import 'package:vegmove_ecommerce/network/dto/search_response_dto.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class SearchService {
  final ApiClient _api = ApiClient();

  Future<SearchResponseDto> searchProducts({
    required String query,
    int page = 0, // Algolia uses 0-based pagination
    int pageSize = 20,
    int? categoryId,
    bool? active,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'query': query.trim(),
        'page': page,
        'pageSize': pageSize,
      };

      if (categoryId != null) {
        queryParams['categoryId'] = categoryId;
      }

      if (active != null) {
        queryParams['active'] = active;
      }

      debugPrint('SearchService.searchProducts queryParams: $queryParams');

      return _api.get<SearchResponseDto>(
        'search/products',
        queries: queryParams,
        parser: (json) {
          debugPrint(
              'SearchService.searchProducts response: ${json.runtimeType}');
          try {
            return SearchResponseDto.fromJson(json);
          } catch (parseError, parseStackTrace) {
            debugPrint('Error parsing search response: $parseError');
            debugPrint('Parse error stack trace: $parseStackTrace');
            debugPrint('JSON data: $json');
            rethrow;
          }
        },
      );
    } catch (e, stackTrace) {
      debugPrint('SearchService.searchProducts error: $e');
      debugPrint('Stack trace: $stackTrace');
      rethrow;
    }
  }

  Future<List<String>> getSearchSuggestions({
    required String query,
    int limit = 5,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'query': query.trim(),
        'limit': limit,
      };

      debugPrint('SearchService.getSearchSuggestions queryParams: $queryParams');

      return _api.get<List<String>>(
        'search/suggestions',
        queries: queryParams,
        parser: (json) {
          debugPrint(
              'SearchService.getSearchSuggestions response: ${json.runtimeType}');
          try {
            if (json is List) {
              return List<String>.from(json);
            } else {
              debugPrint('Expected List but got: ${json.runtimeType}');
              return [];
            }
          } catch (parseError, parseStackTrace) {
            debugPrint('Error parsing suggestions response: $parseError');
            debugPrint('Parse error stack trace: $parseStackTrace');
            debugPrint('JSON data: $json');
            return [];
          }
        },
      );
    } catch (e, stackTrace) {
      debugPrint('SearchService.getSearchSuggestions error: $e');
      debugPrint('Stack trace: $stackTrace');
      return [];
    }
  }
}
