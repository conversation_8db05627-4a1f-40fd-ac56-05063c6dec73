import 'package:vegmove_ecommerce/network/dto/expiring_points_response_dto.dart';
import 'package:vegmove_ecommerce/network/dto/reward_points_response_dto.dart';
import 'package:vegmove_ecommerce/model/reward_point_transaction.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class RewardPointService {
  final ApiClient _api = ApiClient();

  /// Get the user's current reward points
  Future<double> getUserRewardPoints() async {
    try {
      final response = await _api.get<RewardPointsResponseDto>(
        'reward-points',
        parser: (json) => RewardPointsResponseDto.fromJson(json),
      );
      return response.points;
    } catch (e) {
      rethrow;
    }
  }

  /// Get the user's reward point transactions
  Future<List<RewardPointTransaction>> getUserTransactions() async {
    try {
      return _api.get<List<RewardPointTransaction>>(
        'reward-points/transactions',
        parser: (json) => List<RewardPointTransaction>.from(
          (json as List).map((x) => RewardPointTransaction.fromJson(x)),
        ),
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Get information about the user's expiring points
  Future<ExpiringPointsResponseDto> getExpiringPoints() async {
    try {
      return _api.get<ExpiringPointsResponseDto>(
        'reward-points/expiring',
        parser: (json) => ExpiringPointsResponseDto.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }
}
