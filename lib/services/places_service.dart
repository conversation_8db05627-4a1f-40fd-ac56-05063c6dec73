import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;

class PlacesService {
  // This will be replaced with the actual API key
  static const String _apiKey = 'AIzaSyAH8ec01tt7sPgG4CsVcIxO_u7GEqn6ymA';

  // Base URL for Google Places API
  static const String _baseUrl = 'https://maps.googleapis.com/maps/api/place';

  /// Search for places using Google Places Autocomplete API
  Future<List<PlaceAutocompleteResult>> getPlaceAutocomplete(
      String query) async {
    if (query.isEmpty) {
      return [];
    }

    final url = Uri.parse(
        '$_baseUrl/autocomplete/json?input=$query&key=$_apiKey&types=geocode');

    try {
      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK') {
          final predictions = data['predictions'] as List;
          return predictions
              .map((prediction) => PlaceAutocompleteResult.fromJson(prediction))
              .toList();
        } else {
          debugPrint('Error from Places API: ${data['status']}');
          return [];
        }
      } else {
        debugPrint('Error fetching place autocomplete: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('Exception fetching place autocomplete: $e');
      return [];
    }
  }

  /// Get place details using Google Places Details API
  Future<PlaceDetails?> getPlaceDetails(String placeId) async {
    final url = Uri.parse(
        '$_baseUrl/details/json?place_id=$placeId&key=$_apiKey&fields=formatted_address,geometry,name');

    try {
      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK') {
          final result = data['result'];
          return PlaceDetails.fromJson(result);
        } else {
          debugPrint('Error from Places API: ${data['status']}');
          return null;
        }
      } else {
        debugPrint('Error fetching place details: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('Exception fetching place details: $e');
      return null;
    }
  }
}

/// Model for place autocomplete results
class PlaceAutocompleteResult {
  final String placeId;
  final String description;
  final String mainText;
  final String secondaryText;

  PlaceAutocompleteResult({
    required this.placeId,
    required this.description,
    required this.mainText,
    required this.secondaryText,
  });

  factory PlaceAutocompleteResult.fromJson(Map<String, dynamic> json) {
    final structuredFormatting = json['structured_formatting'] ?? {};

    return PlaceAutocompleteResult(
      placeId: json['place_id'] ?? '',
      description: json['description'] ?? '',
      mainText: structuredFormatting['main_text'] ?? '',
      secondaryText: structuredFormatting['secondary_text'] ?? '',
    );
  }
}

/// Model for place details
class PlaceDetails {
  final String name;
  final String formattedAddress;
  final LatLng location;

  PlaceDetails({
    required this.name,
    required this.formattedAddress,
    required this.location,
  });

  factory PlaceDetails.fromJson(Map<String, dynamic> json) {
    final geometry = json['geometry'] ?? {};
    final location = geometry['location'] ?? {};

    return PlaceDetails(
      name: json['name'] ?? '',
      formattedAddress: json['formatted_address'] ?? '',
      location: LatLng(
        location['lat'] ?? 0.0,
        location['lng'] ?? 0.0,
      ),
    );
  }
}
