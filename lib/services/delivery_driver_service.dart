import 'dart:developer';

import 'package:vegmove_ecommerce/model/delivery_driver_location.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class DeliveryDriverService {
  final ApiClient _api = ApiClient();

  /// Get the last location of a delivery driver
  Future<DeliveryDriverLocation?> getDriverLastLocation(int driverId) async {
    try {
      return _api.get<DeliveryDriverLocation>(
        'delivery-drivers/$driverId/location',
        parser: (json) => DeliveryDriverLocation.fromJson(json),
      );
    } catch (e, stacktrace) {
      log('Error getting driver location: $e');
      log(stacktrace.toString());
      return null;
    }
  }

  /// Get the distance between a driver and a warehouse
  Future<Map<String, dynamic>?> getDriverDistanceFromWarehouse(
    int driverId,
    int warehouseId,
  ) async {
    try {
      return _api.get<Map<String, dynamic>>(
        'delivery-drivers/$driverId/distance/$warehouseId',
        parser: (json) => json as Map<String, dynamic>,
      );
    } catch (e, stacktrace) {
      log('Error getting driver distance: $e');
      log(stacktrace.toString());
      return null;
    }
  }
}
