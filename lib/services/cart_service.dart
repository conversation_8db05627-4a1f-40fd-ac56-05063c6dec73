import 'package:vegmove_ecommerce/model/cart_item.dart';
import 'package:vegmove_ecommerce/model/cart.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class CartService {
  final ApiClient _api = ApiClient();

  Future<Cart> getCart({
    required double lat,
    required double long,
    required WarehouseType warehouseType,
    String? languageCode,
    String? couponCode,
    bool useRewardPoints = false,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'lat': lat,
        'long': long,
        'warehouseType': warehouseType.name,
        'useRewardPoints': useRewardPoints,
      };

      if (languageCode != null) {
        queryParams['lang'] = languageCode;
      }

      if (couponCode != null) {
        queryParams['couponCode'] = couponCode;
      }

      return _api.get<Cart>(
        'cart',
        queries: queryParams,
        parser: (json) => Cart.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<CartItem> addToCart({
    required int productId,
    required int quantity,
    required double lat,
    required double long,
    required WarehouseType warehouseType,
    String? languageCode,
    int? variationId,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'lat': lat,
        'long': long,
        'warehouseType': warehouseType.name,
      };

      if (languageCode != null) {
        queryParams['lang'] = languageCode;
      }

      final Map<String, dynamic> data = {
        'productId': productId,
        'quantity': quantity,
      };

      if (variationId != null) {
        data['variationId'] = variationId;
      }

      return _api.post<CartItem>(
        'cart',
        data: data,
        queries: queryParams,
        parser: (json) => CartItem.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<CartItem> removeFromCart({
    required int productId,
    required double lat,
    required double long,
    required WarehouseType warehouseType,
    String? languageCode,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'lat': lat,
        'long': long,
        'warehouseType': warehouseType.name,
      };

      if (languageCode != null) {
        queryParams['lang'] = languageCode;
      }

      return _api.delete<CartItem>(
        'cart/$productId',
        queries: queryParams,
        parser: (json) => CartItem.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<Cart> clearCart() async {
    try {
      return _api.delete<Cart>(
        'cart/clear',
        parser: (json) => Cart.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }
}
