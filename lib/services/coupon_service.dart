import 'package:vegmove_ecommerce/model/coupon.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class CouponService {
  final ApiClient _api = ApiClient();

  Future<List<Coupon>> getCoupons() async {
    try {
      return _api.get<List<Coupon>>(
        'coupons',
        parser: (json) => List<Coupon>.from(
          (json as List).map((x) => Coupon.fromJson(x)),
        ),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<Coupon> validateCoupon(String code) async {
    try {
      return _api.get<Coupon>(
        'coupons/validate/$code',
        parser: (json) => Coupon.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }
}
