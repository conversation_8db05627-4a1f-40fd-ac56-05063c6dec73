import 'package:vegmove_ecommerce/model/country.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class CountryService {
  final ApiClient _api = ApiClient();

  Future<List<Country>> getCountries() async {
    try {
      return _api.get<List<Country>>(
        'countries',
        parser: (json) => List<Country>.from(
          (json as List).map((x) => Country.fromJson(x)),
        ),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<Country> getCountryById(int id) async {
    try {
      return _api.get<Country>(
        'countries/$id',
        parser: (json) => Country.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }
}
