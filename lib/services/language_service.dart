import 'package:vegmove_ecommerce/model/language.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class LanguageService {
  final ApiClient _api = ApiClient();

  Future<List<Language>> getLanguages() async {
    try {
      return _api.get<List<Language>>(
        'languages',
        parser: (json) => List<Language>.from(
          (json as List).map((x) => Language.fromJson(x)),
        ),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<Language> getLanguageByCode(String code) async {
    try {
      return _api.get<Language>(
        'languages/$code',
        parser: (json) => Language.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }
}
