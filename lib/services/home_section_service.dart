import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/model/home_section.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class HomeSectionService {
  final ApiClient _api = ApiClient();

  Future<List<HomeSection>> getHomeSections({
    required double lat,
    required double long,
    required WarehouseType warehouseType,
    String? languageCode,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'lat': lat,
        'long': long,
        'warehouseType': warehouseType.name,
      };

      if (languageCode != null) {
        queryParams['lang'] = languageCode;
      }

      return _api.get<List<HomeSection>>(
        'home-sections',
        queries: queryParams,
        parser: (json) => List<HomeSection>.from(
          (json as List).map((x) => HomeSection.fromJson(x)),
        ),
      );
    } catch (e) {
      rethrow;
    }
  }
}
