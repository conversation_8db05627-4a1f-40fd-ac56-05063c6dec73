import 'package:vegmove_ecommerce/model/delivery_slot.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class DeliverySlotService {
  final ApiClient _api = ApiClient();

  Future<List<DeliverySlot>> getDeliverySlots({required DateTime date}) async {
    try {
      final Map<String, dynamic> queryParams = {
        'date': date.toIso8601String(),
      };

      return _api.get<List<DeliverySlot>>(
        'delivery-slots',
        queries: queryParams,
        parser: (json) => List<DeliverySlot>.from(
          (json as List).map((x) => DeliverySlot.fromJson(x)),
        ),
      );
    } catch (e) {
      rethrow;
    }
  }
}
