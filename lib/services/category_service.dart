import 'package:flutter/material.dart';
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class CategoryService {
  final ApiClient _api = ApiClient();

  Future<List<Category>> getCategories({
    String? languageCode,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {};

      if (languageCode != null) {
        queryParams['lang'] = languageCode;
      }

      return await _api.get<List<Category>>(
        'categories',
        queries: queryParams,
        parser: (json) => List<Category>.from(
          (json as List).map((x) => Category.fromJson(x)),
        ),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<Category> getCategoryById(int id, {String? languageCode}) async {
    try {
      final Map<String, dynamic> queryParams = {};

      if (languageCode != null) {
        queryParams['lang'] = languageCode;
      }

      return await _api.get<Category>(
        'categories/$id',
        queries: queryParams,
        parser: (json) => Category.fromJson(json),
      );
    } catch (e, stacktrace) {
      debugPrint(e.toString());
      debugPrint(stacktrace.toString());
      rethrow;
    }
  }
}
