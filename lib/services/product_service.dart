import 'package:flutter/foundation.dart';
import 'package:vegmove_ecommerce/network/dto/product_paginated_response_dto.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/model/product.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class ProductService {
  final ApiClient _api = ApiClient();

  Future<ProductPaginatedResponseDto> getProducts({
    required double lat,
    required double long,
    required WarehouseType warehouseType,
    int page = 1,
    int pageSize = 10,
    String? search,
    int? categoryId,
    String? languageCode,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'lat': lat,
        'long': long,
        'warehouseType': warehouseType.name,
        'page': page,
        'pageSize': pageSize,
      };

      // Only add search parameter if it's not null and not empty after trimming
      if (search != null && search.trim().isNotEmpty) {
        queryParams['search'] = search.trim();
      }

      if (categoryId != null) {
        queryParams['categoryId'] = categoryId;
      }

      if (languageCode != null && languageCode.isNotEmpty) {
        queryParams['lang'] = languageCode;
      }

      debugPrint('ProductService.getProducts queryParams: $queryParams');

      return _api.get<ProductPaginatedResponseDto>(
        'products',
        queries: queryParams,
        parser: (json) {
          debugPrint(
              'ProductService.getProducts response: ${json.runtimeType}');
          try {
            return ProductPaginatedResponseDto.fromJson(json);
          } catch (parseError, parseStackTrace) {
            debugPrint('Error parsing product response: $parseError');
            debugPrint('Parse error stack trace: $parseStackTrace');
            debugPrint('JSON data: $json');
            rethrow;
          }
        },
      );
    } catch (e, stackTrace) {
      debugPrint('ProductService.getProducts error: $e');
      debugPrint('Stack trace: $stackTrace');
      rethrow;
    }
  }

  Future<Product> getProductBySlug({
    required String slug,
    required double lat,
    required double long,
    required WarehouseType warehouseType,
    String? languageCode,
    List<int>? optionIds,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'lat': lat,
        'long': long,
        'warehouseType': warehouseType.name,
      };

      if (languageCode != null) {
        queryParams['lang'] = languageCode;
      }

      if (optionIds != null && optionIds.isNotEmpty) {
        queryParams['optionIds'] = optionIds.join(',');
      }

      return _api.get<Product>(
        'products/$slug',
        queries: queryParams,
        parser: (json) => Product.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<void> subscribeToProductAvailability(int productId) async {
    try {
      await _api.post<Map<String, dynamic>>(
        'products/$productId/subscribe',
        parser: (json) => json as Map<String, dynamic>,
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<List<Product>> getRelatedProducts({
    required int productId,
    required double lat,
    required double long,
    required WarehouseType warehouseType,
    String? languageCode,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'lat': lat,
        'long': long,
        'warehouseType': warehouseType.name,
      };

      if (languageCode != null && languageCode.isNotEmpty) {
        queryParams['lang'] = languageCode;
      }

      debugPrint('ProductService.getRelatedProducts queryParams: $queryParams');

      return _api.get<List<Product>>(
        'products/$productId/related',
        queries: queryParams,
        parser: (json) {
          debugPrint(
              'ProductService.getRelatedProducts response: ${json.runtimeType}');
          try {
            if (json is List) {
              return json.map((item) => Product.fromJson(item)).toList();
            } else {
              throw Exception('Expected List but got ${json.runtimeType}');
            }
          } catch (parseError, parseStackTrace) {
            debugPrint('Error parsing related products response: $parseError');
            debugPrint('Parse error stack trace: $parseStackTrace');
            debugPrint('JSON data: $json');
            rethrow;
          }
        },
      );
    } catch (e, stackTrace) {
      debugPrint('ProductService.getRelatedProducts error: $e');
      debugPrint('Stack trace: $stackTrace');
      rethrow;
    }
  }
}
