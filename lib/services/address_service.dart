import 'package:vegmove_ecommerce/model/address.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class AddressService {
  final ApiClient _api = ApiClient();

  Future<List<Address>> getAddresses() async {
    try {
      return await _api.get<List<Address>>(
        'addresses',
        parser: (json) => List<Address>.from(
          (json as List).map((x) => Address.fromJson(x)),
        ),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<Address> getAddressById(int id) async {
    try {
      return _api.get<Address>(
        'addresses/$id',
        parser: (json) => Address.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<Address> createAddress({
    required AddressType type,
    required String streetName,
    required String city,
    required String state,
    required int countryId,
    required String zipCode,
    required String lat,
    required String long,
    String? apartment,
    String? block,
  }) async {
    try {
      final data = {
        'type': type.name,
        'streetName': streetName,
        'city': city,
        'state': state,
        'countryId': countryId,
        'zipCode': zipCode,
        'apartment': apartment,
        'block': block,
        'lat': lat,
        'long': long,
      };

      return _api.post<Address>(
        'addresses',
        data: data,
        parser: (json) => Address.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<Address> updateAddress({
    required int id,
    AddressType? type,
    String? streetName,
    String? city,
    String? state,
    int? countryId,
    String? zipCode,
    String? apartment,
    String? block,
    String? lat,
    String? long,
  }) async {
    try {
      final data = {
        if (type != null) 'type': type.name,
        if (streetName != null) 'streetName': streetName,
        if (city != null) 'city': city,
        if (state != null) 'state': state,
        if (countryId != null) 'countryId': countryId,
        if (zipCode != null) 'zipCode': zipCode,
        if (apartment != null) 'apartment': apartment,
        if (block != null) 'block': block,
        if (lat != null) 'lat': lat,
        if (long != null) 'long': long,
      };

      return _api.patch<Address>(
        'addresses/$id',
        data: data,
        parser: (json) => Address.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<Address> deleteAddress(int id) async {
    try {
      return _api.delete<Address>(
        'addresses/$id',
        parser: (json) => Address.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }
}
