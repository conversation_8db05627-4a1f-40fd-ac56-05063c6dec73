import 'package:vegmove_ecommerce/model/banner.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class BannerService {
  final ApiClient _api = ApiClient();

  Future<List<Banner>> getBanners({
    required double lat,
    required double long,
    String? languageCode,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'lat': lat,
        'long': long,
      };

      if (languageCode != null) {
        queryParams['lang'] = languageCode;
      }

      return _api.get<List<Banner>>(
        'banners',
        queries: queryParams,
        parser: (json) => List<Banner>.from(
          (json as List).map((x) => Banner.fromJson(x)),
        ),
      );
    } catch (e) {
      rethrow;
    }
  }
}
