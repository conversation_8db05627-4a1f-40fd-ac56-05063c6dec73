import 'package:vegmove_ecommerce/network/dto/rolling_spend_response_dto.dart';
import 'package:vegmove_ecommerce/network/dto/user_tier_response_dto.dart';
import 'package:vegmove_ecommerce/model/reward_tier.dart';
import 'package:vegmove_ecommerce/network/api_client.dart';

class RewardTierService {
  final ApiClient _api = ApiClient();

  /// Get all available reward tiers
  Future<List<RewardTier>> getAllRewardTiers() async {
    try {
      return _api.get<List<RewardTier>>(
        'reward-tiers/all',
        parser: (json) => List<RewardTier>.from(
          (json as List).map((x) => RewardTier.fromJson(x)),
        ),
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Get the user's current reward tier
  Future<UserTierResponseDto> getUserTier() async {
    try {
      return _api.get<UserTierResponseDto>(
        'reward-tiers',
        parser: (json) => UserTierResponseDto.fromJson(json),
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Get the user's current rolling spend
  Future<double> getRollingSpend() async {
    try {
      final response = await _api.get<RollingSpendResponseDto>(
        'reward-tiers/spending',
        parser: (json) => RollingSpendResponseDto.fromJson(json),
      );
      return response.spending;
    } catch (e) {
      rethrow;
    }
  }
}
