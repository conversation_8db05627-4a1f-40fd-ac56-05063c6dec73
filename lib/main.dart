import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/router.dart';
import 'package:vegmove_ecommerce/services/notification_service.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

import 'generated/l10n/app_localizations.dart';
import 'providers/language_provider.dart';
import 'providers/location_provider.dart';
import 'providers/cart_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await NotificationService.init();
  FirebaseMessaging.onBackgroundMessage(_backgroundHandler);
  runApp(const ProviderScope(child: MyApp()));
}

Future<void> _backgroundHandler(RemoteMessage message) async {
  // Handle background message
  await NotificationService.show(message);
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  @override
  void initState() {
    super.initState();
    _firebaseMessaging.requestPermission();
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Message data: ${message.data}');
      if (message.notification != null) {
        print('Message also contained a notification: ${message.notification}');
      }

      NotificationService.show(message);
    });
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('Message clicked! ${message.messageId}');
      // final screen = message.data['screen'];
      // final orderId = int.tryParse(message.data['orderId'] ?? '');
      //
      // if (screen == 'order-details' && orderId != null) {
      //   context.pushNamed('order-details', extra: orderId);
      // }
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageCode = ref.watch(languageProvider).code;
    // Access location provider to ensure it's initialized
    ref.watch(locationProvider);
    // Initialize cart provider
    ref.watch(cartProvider);
    // Get the router from the provider
    final router = ref.watch(routerProvider);
    // Get the theme from the provider
    final theme = ref.watch(themeProvider);

    return MaterialApp.router(
      title: 'Vegmove',
      theme: theme,
      routerConfig: router,
      locale: Locale(languageCode),
      localizationsDelegates: [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        AppLocalizations.delegate,
      ],
      supportedLocales: [
        const Locale('en', ''),
        const Locale('bn', ''),
      ],
      debugShowCheckedModeBanner: false,
    );
  }
}
