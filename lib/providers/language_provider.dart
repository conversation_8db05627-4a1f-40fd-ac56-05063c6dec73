import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

final languageProvider = ChangeNotifierProvider<LanguageNotifier>((ref) {
  return LanguageNotifier();
});

class LanguageNotifier extends ChangeNotifier {
  String _code = 'en';
  static const String _languageKey = 'language_code';

  LanguageNotifier() {
    _loadLanguage();
  }

  String get code => _code;

  Future<void> _loadLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final savedCode = prefs.getString(_languageKey);
    if (savedCode != null) {
      _code = savedCode;
      notifyListeners();
    }
  }

  Future<void> setCode(String code) async {
    if (_code != code) {
      _code = code;

      // Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, code);

      notifyListeners();
    }
  }
}
