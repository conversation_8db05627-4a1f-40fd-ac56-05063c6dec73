import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/model/product.dart';
import 'package:vegmove_ecommerce/providers/language_provider.dart';
import 'package:vegmove_ecommerce/providers/location_provider.dart';
import 'package:vegmove_ecommerce/providers/warehouse_provider.dart';
import 'package:vegmove_ecommerce/services/category_service.dart';
import 'package:vegmove_ecommerce/services/product_service.dart';

/// Provider for fetching a category by ID
final categoryDetailsProvider =
    FutureProvider.family<Category, int>((ref, categoryId) async {
  final categoryService = CategoryService();
  final languageCode = ref.watch(languageProvider).code;

  try {
    return await categoryService.getCategoryById(
      categoryId,
      languageCode: languageCode,
    );
  } catch (e) {
    throw Exception('Failed to load category: $e');
  }
});

/// Provider for fetching products by category ID
final categoryProductsProvider = StateNotifierProvider.family<
    CategoryProductsNotifier, CategoryProductsState, int>((ref, categoryId) {
  final locationData = ref.watch(locationProvider);
  final warehouseType = ref.watch(warehouseTypeProvider).type;
  final languageCode = ref.watch(languageProvider).code;

  return CategoryProductsNotifier(
    productService: ProductService(),
    categoryId: categoryId,
    lat: locationData.lat,
    long: locationData.long,
    warehouseType: warehouseType,
    languageCode: languageCode,
  );
});

/// State for category products
class CategoryProductsState {
  final bool isLoading;
  final String? error;
  final List<Product> products;
  final int currentPage;
  final int totalPages;
  final bool hasMore;
  final int? selectedSegmentId;

  CategoryProductsState({
    this.isLoading = false,
    this.error,
    this.products = const [],
    this.currentPage = 1,
    this.totalPages = 1,
    this.hasMore = false,
    this.selectedSegmentId,
  });

  CategoryProductsState copyWith({
    bool? isLoading,
    String? error,
    List<Product>? products,
    int? currentPage,
    int? totalPages,
    bool? hasMore,
    int? selectedSegmentId,
  }) {
    return CategoryProductsState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      products: products ?? this.products,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMore: hasMore ?? this.hasMore,
      selectedSegmentId: selectedSegmentId ?? this.selectedSegmentId,
    );
  }
}

/// Notifier for category products
class CategoryProductsNotifier extends StateNotifier<CategoryProductsState> {
  final ProductService productService;
  final int categoryId;
  final double lat;
  final double long;
  final WarehouseType warehouseType;
  final String languageCode;

  CategoryProductsNotifier({
    required this.productService,
    required this.categoryId,
    required this.lat,
    required this.long,
    required this.warehouseType,
    required this.languageCode,
  }) : super(CategoryProductsState()) {
    // Load products when created
    loadProducts();
  }

  /// Load products for the category
  Future<void> loadProducts(
      {bool refresh = false, int? segmentId, bool resetSegment = false}) async {
    try {
      final page = refresh ? 1 : state.currentPage;

      // If resetSegment is true, we want to show products from the main category
      // Otherwise, use the provided segmentId or the currently selected one
      final targetCategoryId = resetSegment
          ? categoryId
          : (segmentId ?? state.selectedSegmentId ?? categoryId);

      if (page == 1) {
        state = state.copyWith(isLoading: true, error: null);
      }

      final response = await productService.getProducts(
        lat: lat,
        long: long,
        warehouseType: warehouseType,
        page: page,
        pageSize: 10,
        categoryId: targetCategoryId,
        languageCode: languageCode,
      );

      final products =
          page == 1 ? response.data : [...state.products, ...response.data];

      state = state.copyWith(
        isLoading: false,
        products: products,
        currentPage: response.page,
        totalPages: response.totalPages,
        hasMore: response.page < response.totalPages,
        // If resetSegment is true, set selectedSegmentId to null
        selectedSegmentId:
            resetSegment ? null : (segmentId ?? state.selectedSegmentId),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load products: $e',
      );
    }
  }

  /// Load more products
  Future<void> loadMore() async {
    if (state.hasMore && !state.isLoading) {
      state = state.copyWith(currentPage: state.currentPage + 1);
      await loadProducts();
    }
  }

  /// Set selected segment and load its products
  Future<void> selectSegment(int segmentId) async {
    // Always update the state and refresh products, even if the segment is the same
    // This ensures the UI updates correctly when a tab is selected multiple times
    state = state.copyWith(
      selectedSegmentId: segmentId,
      currentPage: 1,
      // Clear products to show loading state
      products: [],
      isLoading: true,
    );
    await loadProducts(refresh: true, segmentId: segmentId);
  }

  /// Reset to show all products (no segment selected)
  Future<void> resetToAllProducts() async {
    // Clear products and show loading state
    state = state.copyWith(
      products: [],
      isLoading: true,
      selectedSegmentId: null,
      currentPage: 1,
    );

    // Load products with resetSegment to show all products
    await loadProducts(refresh: true, resetSegment: true);
  }
}

/// Provider for fetching the first category in a collection
final firstCategoryDetailsProvider =
    FutureProvider.family<Category, int>((ref, collectionCategoryId) async {
  final categoryService = CategoryService();
  final languageCode = ref.watch(languageProvider).code;

  try {
    // First, get the collection details
    final collection = await categoryService.getCategoryById(
      collectionCategoryId,
      languageCode: languageCode,
    );

    // Check if the collection has categories
    if (collection.categories == null || collection.categories!.isEmpty) {
      throw Exception('Collection has no categories');
    }

    // Get the first category details
    final firstCategoryId = collection.categories!.first.id;
    return await categoryService.getCategoryById(
      firstCategoryId,
      languageCode: languageCode,
    );
  } catch (e) {
    throw Exception('Failed to load first category: $e');
  }
});

/// Provider for all categories (for breadcrumb navigation)
final categoriesProvider = FutureProvider<List<Category>>((ref) async {
  final categoryService = CategoryService();
  final languageCode = ref.watch(languageProvider).code;

  try {
    return await categoryService.getCategories(
      languageCode: languageCode,
    );
  } catch (e) {
    throw Exception('Failed to load categories: $e');
  }
});
