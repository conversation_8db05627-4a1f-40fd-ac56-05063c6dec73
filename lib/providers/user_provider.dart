import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_ecommerce/model/user.dart';
import 'package:vegmove_ecommerce/services/auth_service.dart';

/// State for the user provider
class UserState {
  final bool isLoading;
  final User? user;
  final String? error;
  final bool isLoggedIn;

  UserState({
    this.isLoading = false,
    this.user,
    this.error,
    this.isLoggedIn = false,
  });

  UserState copyWith({
    bool? isLoading,
    User? user,
    String? error,
    bool? isLoggedIn,
  }) {
    return UserState(
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error,
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
    );
  }
}

/// Notifier for the user provider
class UserNotifier extends StateNotifier<UserState> {
  final AuthService _authService;

  UserNotifier({required AuthService authService})
      : _authService = authService,
        super(UserState()) {
    // Initialize by checking if the user is logged in
    checkLoginStatus();
  }

  /// Check if the user is logged in and load user data if they are
  Future<void> checkLoginStatus() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final isLoggedIn = await _authService.isLoggedIn();

      if (isLoggedIn) {
        await loadUserData();
      } else {
        state = state.copyWith(
          isLoading: false,
          isLoggedIn: false,
          user: null,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error checking login status: $e',
        isLoggedIn: false,
      );
    }
  }

  /// Load the current user's data
  Future<void> loadUserData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final user = await _authService.getCurrentUser();
      state = state.copyWith(
        isLoading: false,
        user: user,
        isLoggedIn: true,
      );
    } catch (e) {
      debugPrint('Error loading user data: $e');

      // Check if the error is due to an invalid token
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null || token.isEmpty) {
        // Token was removed, user is not logged in
        state = state.copyWith(
          isLoading: false,
          isLoggedIn: false,
          user: null,
        );
      } else {
        // Some other error occurred
        state = state.copyWith(
          isLoading: false,
          error: 'Error loading user data: $e',
        );
      }
    }
  }

  /// Handle user login
  Future<bool> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _authService.login(email, password);
      await loadUserData();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Login failed: $e',
      );
      return false;
    }
  }

  /// Handle user registration
  Future<bool> register(
    String name,
    String email,
    String password,
    int countryId, {
    String? referralCode,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _authService.register(
        name,
        email,
        password,
        countryId,
        referralCode: referralCode,
      );
      await loadUserData();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Registration failed: $e',
      );
      return false;
    }
  }

  /// Handle user logout
  Future<void> logout() async {
    state = state.copyWith(isLoading: true);

    try {
      await _authService.logout();
      state = UserState(); // Reset to initial state
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Logout failed: $e',
      );
    }
  }

  /// Update user's name
  Future<bool> updateName(String name) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final updatedUser = await _authService.updateName(name);
      state = state.copyWith(
        isLoading: false,
        user: updatedUser,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update name: $e',
      );
      return false;
    }
  }

  /// Update user's email
  Future<bool> updateEmail(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final updatedUser = await _authService.updateEmail(email);
      state = state.copyWith(
        isLoading: false,
        user: updatedUser,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update email: $e',
      );
      return false;
    }
  }

  /// Change user's password
  Future<bool> changePassword(
      String currentPassword, String newPassword) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _authService.changePassword(currentPassword, newPassword);
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to change password: $e',
      );
      return false;
    }
  }
}

/// Provider for the auth service
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

/// Provider for the user state
final userProvider = StateNotifierProvider<UserNotifier, UserState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return UserNotifier(authService: authService);
});
