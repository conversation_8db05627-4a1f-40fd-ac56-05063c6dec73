import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/model/algolia_product.dart';
import 'package:vegmove_ecommerce/services/search_service.dart';

/// Search state class
class SearchState {
  final List<AlgoliaProduct> products;
  final bool isLoading;
  final String? error;
  final String query;
  final int currentPage;
  final int totalPages;
  final bool hasMore;

  const SearchState({
    this.products = const [],
    this.isLoading = false,
    this.error,
    this.query = '',
    this.currentPage = 0, // Algolia uses 0-based pagination
    this.totalPages = 0,
    this.hasMore = false,
  });

  SearchState copyWith({
    List<AlgoliaProduct>? products,
    bool? isLoading,
    String? error,
    String? query,
    int? currentPage,
    int? totalPages,
    bool? hasMore,
  }) {
    return SearchState(
      products: products ?? this.products,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      query: query ?? this.query,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

/// Search notifier class
class SearchNotifier extends StateNotifier<SearchState> {
  final SearchService _searchService;

  SearchNotifier({
    required SearchService searchService,
  })  : _searchService = searchService,
        super(const SearchState());

  /// Search for products
  Future<void> search(String query, {bool refresh = false}) async {
    if (query.isEmpty) {
      state = state.copyWith(
        products: [],
        isLoading: false,
        error: null,
        query: '',
        currentPage: 0,
        totalPages: 0,
        hasMore: false,
      );
      return;
    }

    // If query changed, reset state
    if (query != state.query) {
      state = state.copyWith(
        products: [],
        isLoading: true,
        error: null,
        query: query,
        currentPage: 0,
        totalPages: 0,
        hasMore: false,
      );
    } else if (refresh) {
      state = state.copyWith(
        isLoading: true,
        error: null,
        currentPage: 0,
      );
    } else if (state.isLoading || !state.hasMore) {
      // Don't search if already loading or no more results
      return;
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 0 : state.currentPage;

      // Ensure the search query is properly trimmed and not null
      final String trimmedQuery = query.trim();

      debugPrint('Search query: "$trimmedQuery"');
      debugPrint('Search parameters: page=$page');

      final response = await _searchService.searchProducts(
        query: trimmedQuery,
        page: page,
        pageSize: 20,
        active: true, // Only search for active products
      );

      final products =
          page == 0 ? response.hits : [...state.products, ...response.hits];

      state = state.copyWith(
        products: products,
        isLoading: false,
        error: null,
        currentPage: page + 1,
        totalPages: response.totalPages,
        hasMore: page + 1 < response.totalPages,
      );
    } catch (e, stackTrace) {
      debugPrint('Search error: $e');
      debugPrint('Stack trace: $stackTrace');

      state = state.copyWith(
        isLoading: false,
        error: 'Failed to search products: $e',
      );
    }
  }

  /// Load more search results
  Future<void> loadMore() async {
    if (state.query.isEmpty || state.isLoading || !state.hasMore) {
      return;
    }

    await search(state.query);
  }

  /// Clear search results
  void clear() {
    state = const SearchState();
  }
}

/// Provider for search state
final searchProvider =
    StateNotifierProvider<SearchNotifier, SearchState>((ref) {
  return SearchNotifier(
    searchService: SearchService(),
  );
});
