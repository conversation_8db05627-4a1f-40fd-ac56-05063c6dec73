import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/providers/language_provider.dart';
import 'package:vegmove_ecommerce/providers/location_provider.dart';
import 'package:vegmove_ecommerce/model/product.dart';
import 'package:vegmove_ecommerce/services/product_service.dart';
import 'package:vegmove_ecommerce/providers/warehouse_provider.dart';

/// Product state class
class ProductState {
  final Product? product;
  final bool isLoading;
  final String? error;
  final String slug;
  final List<int>? optionIds;

  const ProductState({
    this.product,
    this.isLoading = false,
    this.error,
    required this.slug,
    this.optionIds,
  });

  ProductState copyWith({
    Product? product,
    bool? isLoading,
    String? error,
    String? slug,
    List<int>? optionIds,
    bool clearError = false,
  }) {
    return ProductState(
      product: product ?? this.product,
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
      slug: slug ?? this.slug,
      optionIds: optionIds ?? this.optionIds,
    );
  }
}

/// Product notifier class
class ProductNotifier extends StateNotifier<ProductState> {
  final ProductService _productService;
  final Ref _ref;

  ProductNotifier({
    required ProductService productService,
    required Ref ref,
    required String initialProductSlug,
  })  : _productService = productService,
        _ref = ref,
        super(ProductState(
          slug: initialProductSlug,
          isLoading: initialProductSlug
              .isNotEmpty, // Only set loading if we have a valid ID
        )) {
    // Only load initial product if we have a valid ID
    if (initialProductSlug.isNotEmpty) {
      _fetchProduct();
    }
  }

  /// Update selected options and fetch product
  Future<void> updateOptions(String slug, List<int>? optionIds) async {
    // Only update if options changed
    if (state.slug == slug && _areOptionIdsEqual(state.optionIds, optionIds)) {
      return;
    }

    // Store previous variation ID to detect changes
    final previousVariationId = state.product?.selectedVariation?.id;

    state = state.copyWith(
      slug: slug,
      optionIds: optionIds,
      isLoading: true,
      clearError: true,
    );

    await _fetchProduct();

    // Check if variation changed
    final newVariationId = state.product?.selectedVariation?.id;
    if (newVariationId != null && newVariationId != previousVariationId) {
      debugPrint(
          'Variation changed from $previousVariationId to $newVariationId');
    }
  }

  // Helper to compare option IDs
  bool _areOptionIdsEqual(List<int>? list1, List<int>? list2) {
    if (list1 == null && list2 == null) return true;
    if (list1 == null || list2 == null) return false;
    if (list1.length != list2.length) return false;

    // Sort both lists to compare
    final sorted1 = List<int>.from(list1)..sort();
    final sorted2 = List<int>.from(list2)..sort();

    for (int i = 0; i < sorted1.length; i++) {
      if (sorted1[i] != sorted2[i]) return false;
    }

    return true;
  }

  /// Fetch product with current state
  Future<void> _fetchProduct() async {
    // Skip if product ID is invalid
    if (state.slug.isEmpty) {
      state = state.copyWith(
        isLoading: false,
        error: 'Invalid product ID',
      );
      return;
    }

    try {
      final locationData = _ref.read(locationProvider);
      final warehouseType = _ref.read(warehouseTypeProvider).type;
      final languageCode = _ref.read(languageProvider).code;

      debugPrint(
          'Fetching product ID: ${state.slug} with options: ${state.optionIds}');

      final product = await _productService.getProductBySlug(
        slug: state.slug,
        lat: locationData.lat,
        long: locationData.long,
        warehouseType: warehouseType,
        languageCode: languageCode,
        optionIds: state.optionIds,
      );

      state = state.copyWith(
        product: product,
        isLoading: false,
        clearError: true,
      );

      debugPrint('Product fetched successfully: ${product.name}');
      if (product.selectedVariation != null) {
        debugPrint('Selected variation: ${product.selectedVariation!.id}');
      }
    } catch (e) {
      debugPrint('Error fetching product: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load product: $e',
      );
    }
  }
}

/// Provider for product state
final productDetailsProvider =
    StateNotifierProvider<ProductNotifier, ProductState>((ref) {
  // Create with a loading state but don't fetch anything yet
  return ProductNotifier(
    productService: ProductService(),
    ref: ref,
    initialProductSlug:
        '', // Use -1 as a sentinel value to indicate no product is loaded yet
  );
});

/// Provider for fetching a product by ID (legacy version for backward compatibility)
final productByIdProvider =
    FutureProvider.family<Product, String>((ref, slug) async {
  final productService = ProductService();
  final locationData = ref.watch(locationProvider);
  final warehouseType = ref.watch(warehouseTypeProvider).type;
  final languageCode = ref.watch(languageProvider).code;

  try {
    return await productService.getProductBySlug(
      slug: slug,
      lat: locationData.lat,
      long: locationData.long,
      warehouseType: warehouseType,
      languageCode: languageCode,
    );
  } catch (e) {
    throw Exception('Failed to load product: $e');
  }
});
