import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/model/enums.dart';

final warehouseTypeProvider =
    ChangeNotifierProvider<WarehouseTypeNotifier>((ref) {
  return WarehouseTypeNotifier();
});

class WarehouseTypeNotifier extends ChangeNotifier {
  WarehouseType _type = WarehouseType.GENERAL;

  WarehouseType get type => _type;

  void setType(WarehouseType newType) {
    if (_type != newType) {
      _type = newType;
      notifyListeners();
    }
  }
}
