import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/providers/language_provider.dart';
import 'package:vegmove_ecommerce/providers/location_provider.dart';
import 'package:vegmove_ecommerce/model/cart.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/model/product.dart';
import 'package:vegmove_ecommerce/services/cart_service.dart';
import 'package:vegmove_ecommerce/ui/screens/my_bag/my_bag_screen.dart';
import 'package:vegmove_ecommerce/util.dart';
import 'package:vegmove_ecommerce/providers/warehouse_provider.dart';

/// Provider for the cart state
final cartProvider = StateNotifierProvider<CartNotifier, CartState>((ref) {
  final locationData = ref.watch(locationProvider);
  final warehouseType = ref.watch(warehouseTypeProvider).type;
  final couponCode = ref.watch(couponCodeProvider);
  final useRewardPoints = ref.watch(useRewardPointsProvider);
  final languageCode = ref.watch(languageProvider);

  return CartNotifier(
    cartService: CartService(),
    lat: locationData.lat,
    long: locationData.long,
    warehouseType: warehouseType,
    couponCode: couponCode,
    useRewardPoints: useRewardPoints,
    languageCode: languageCode.code,
  );
});

/// State for the cart
class CartState {
  final bool isLoading;
  final String? error;
  final Cart? cart;

  CartState({
    this.isLoading = false,
    this.error,
    this.cart,
  });

  /// Check if a product is in the cart
  bool isProductInCart(int productId, [int? variationId]) {
    return cart?.items.any((item) {
          if (variationId != null) {
            // If variation ID is provided, check both product ID and variation ID
            return item.productId == productId &&
                item.variationId == variationId;
          }
          // Otherwise just check product ID and that there's no variation
          return item.productId == productId && item.variationId == null;
        }) ??
        false;
  }

  /// Get the quantity of a product in the cart
  int getProductQuantity(int productId, [int? variationId]) {
    final item = cart?.items.firstWhereOrNull(
      (item) {
        if (variationId != null) {
          return item.productId == productId && item.variationId == variationId;
        }
        return item.productId == productId && item.variationId == null;
      },
    );
    return item?.quantity ?? 0;
  }

  CartState copyWith({
    bool? isLoading,
    String? error,
    Cart? cart,
  }) {
    return CartState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      cart: cart ?? this.cart,
    );
  }
}

/// Notifier for the cart
class CartNotifier extends StateNotifier<CartState> {
  final CartService cartService;
  final double lat;
  final double long;
  final WarehouseType warehouseType;
  final String? couponCode;
  final bool useRewardPoints;
  final String languageCode;

  CartNotifier({
    required this.cartService,
    required this.lat,
    required this.long,
    required this.warehouseType,
    required this.couponCode,
    required this.useRewardPoints,
    required this.languageCode,
  }) : super(CartState()) {
    // Initialize by fetching the cart
    fetchCart();
  }

  /// Fetch the cart from the API
  Future<void> fetchCart() async {
    if (!mounted) return;

    state = state.copyWith(isLoading: true);

    try {
      final Cart cart = await cartService.getCart(
        lat: lat,
        long: long,
        warehouseType: warehouseType,
        languageCode: languageCode,
        useRewardPoints: useRewardPoints,
        couponCode: couponCode,
      );

      if (!mounted) return;

      state = state.copyWith(
        isLoading: false,
        cart: cart,
      );
    } catch (e, stacktrace) {
      debugPrint('Error fetching cart: $e');
      debugPrint(stacktrace.toString());
      if (!mounted) return;

      state = state.copyWith(
        isLoading: false,
        error: 'Error fetching cart: $e',
      );
      // Don't show toast for every error as it might be annoying
      // Only show for user-initiated actions
    }
  }

  /// Add a product to the cart
  Future<void> addToCart(Product product) async {
    if (!mounted) return;

    if (product.maxCartQuantity != null &&
        state.getProductQuantity(product.id, product.selectedVariation?.id) >=
            product.maxCartQuantity!) {
      Util.showErrorToast(
          'Maximum ${product.maxCartQuantity} items allowed in cart for this product');
      return;
    }

    if (state.isProductInCart(product.id, product.selectedVariation?.id)) {
      return;
    }

    state = state.copyWith(isLoading: true);

    try {
      await cartService.addToCart(
        productId: product.id,
        quantity: 1,
        lat: lat,
        long: long,
        warehouseType: warehouseType,
        variationId: product.selectedVariation?.id,
      );

      // Refresh the cart to get the updated state
      await fetchCart();

      Util.showSuccessToast('${product.name} added to bag');
    } catch (e) {
      if (!mounted) return;

      state = state.copyWith(
        isLoading: false,
        error: 'Error adding to cart: $e',
      );
      Util.showErrorToast('Error adding to cart: $e');
    }
  }

  /// Update the quantity of a product in the cart
  Future<void> updateQuantity(int productId, int quantity,
      {int? variationId}) async {
    if (!mounted) return;

    if (quantity <= 0) {
      final cartItem = state.cart?.items.firstWhereOrNull(
        (item) {
          if (variationId != null) {
            return item.productId == productId &&
                item.variationId == variationId;
          }
          return item.productId == productId && item.variationId == null;
        },
      );
      if (cartItem != null) await removeFromCart(cartItem.id);
      return;
    }

    state = state.copyWith(isLoading: true);

    try {
      await cartService.addToCart(
        productId: productId,
        quantity: quantity,
        lat: lat,
        long: long,
        warehouseType: warehouseType,
        variationId: variationId,
      );

      // Refresh the cart to get the updated state
      await fetchCart();
    } catch (e) {
      if (!mounted) return;

      state = state.copyWith(
        isLoading: false,
        error: 'Error updating quantity: $e',
      );
      Util.showErrorToast('Error updating quantity: $e');
    }
  }

  /// Remove a product from the cart
  Future<void> removeFromCart(int productId) async {
    if (!mounted) return;

    state = state.copyWith(isLoading: true);

    try {
      await cartService.removeFromCart(
        productId: productId,
        lat: lat,
        long: long,
        warehouseType: warehouseType,
      );

      // Refresh the cart to get the updated state
      await fetchCart();

      Util.showSuccessToast('Item removed from bag');
    } catch (e) {
      if (!mounted) return;

      state = state.copyWith(
        isLoading: false,
        error: 'Error removing from cart: $e',
      );
      Util.showErrorToast('Error removing from cart: $e');
    }
  }

  /// Clear the cart
  Future<void> clearCart() async {
    if (!mounted) return;

    state = state.copyWith(isLoading: true);

    try {
      await cartService.clearCart();

      // Refresh the cart to get the updated state
      await fetchCart();

      Util.showSuccessToast('Cart cleared');
    } catch (e) {
      if (!mounted) return;

      state = state.copyWith(
        isLoading: false,
        error: 'Error clearing cart: $e',
      );
      Util.showErrorToast('Error clearing cart: $e');
    }
  }
}
