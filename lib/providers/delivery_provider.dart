import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/model/delivery_slot.dart';
import 'package:vegmove_ecommerce/services/delivery_slot_service.dart';

/// Provider for the selected delivery date
final selectedDeliveryDateProvider = StateProvider<DateTime?>((ref) {
  // Default to tomorrow
  final tomorrow = DateTime.now().add(const Duration(days: 1));
  return DateTime(tomorrow.year, tomorrow.month, tomorrow.day);
});

/// Provider for the available delivery slots based on the selected date
final deliverySlotsProvider = FutureProvider.autoDispose<List<DeliverySlot>>((ref) async {
  final selectedDate = ref.watch(selectedDeliveryDateProvider);
  
  if (selectedDate == null) {
    return [];
  }
  
  try {
    final deliverySlotService = DeliverySlotService();
    return await deliverySlotService.getDeliverySlots(date: selectedDate);
  } catch (e) {
    debugPrint('Error fetching delivery slots: $e');
    return [];
  }
});

/// Provider for the selected delivery slot
final selectedDeliverySlotProvider = StateProvider<DeliverySlot?>((ref) => null);

/// Class to hold delivery information for the order
class DeliveryInfo {
  final DateTime? deliveryDate;
  final DeliverySlot? deliverySlot;
  
  DeliveryInfo({
    this.deliveryDate,
    this.deliverySlot,
  });
  
  bool get isComplete => deliveryDate != null && deliverySlot != null;
  
  String? get formattedDeliveryTime {
    if (deliverySlot == null) {
      return null;
    }
    return '${deliverySlot!.startTime} - ${deliverySlot!.endTime}';
  }
}

/// Provider for the complete delivery information
final deliveryInfoProvider = Provider<DeliveryInfo>((ref) {
  final selectedDate = ref.watch(selectedDeliveryDateProvider);
  final selectedSlot = ref.watch(selectedDeliverySlotProvider);
  
  return DeliveryInfo(
    deliveryDate: selectedDate,
    deliverySlot: selectedSlot,
  );
});
