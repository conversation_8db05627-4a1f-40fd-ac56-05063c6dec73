import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/providers/language_provider.dart';
import 'package:vegmove_ecommerce/providers/location_provider.dart';
import 'package:vegmove_ecommerce/model/product.dart';
import 'package:vegmove_ecommerce/services/product_service.dart';
import 'package:vegmove_ecommerce/providers/warehouse_provider.dart';

/// Related products state class
class RelatedProductsState {
  final List<Product> products;
  final bool isLoading;
  final String? error;
  final int? productId;

  const RelatedProductsState({
    this.products = const [],
    this.isLoading = false,
    this.error,
    this.productId,
  });

  RelatedProductsState copyWith({
    List<Product>? products,
    bool? isLoading,
    String? error,
    int? productId,
    bool clearError = false,
  }) {
    return RelatedProductsState(
      products: products ?? this.products,
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
      productId: productId ?? this.productId,
    );
  }
}

/// Related products notifier class
class RelatedProductsNotifier extends StateNotifier<RelatedProductsState> {
  final ProductService _productService;
  final Ref _ref;

  RelatedProductsNotifier({
    required ProductService productService,
    required Ref ref,
  })  : _productService = productService,
        _ref = ref,
        super(const RelatedProductsState());

  /// Fetch related products for a given product ID
  Future<void> fetchRelatedProducts(int productId) async {
    // Skip if we're already loading the same product
    if (state.isLoading && state.productId == productId) {
      return;
    }

    // Skip if we already have data for this product and it's not loading
    if (state.productId == productId && 
        state.products.isNotEmpty && 
        !state.isLoading && 
        state.error == null) {
      return;
    }

    state = state.copyWith(
      productId: productId,
      isLoading: true,
      clearError: true,
    );

    try {
      final locationData = _ref.read(locationProvider);
      final warehouseType = _ref.read(warehouseTypeProvider).type;
      final languageCode = _ref.read(languageProvider).code;

      debugPrint('Fetching related products for product ID: $productId');

      final relatedProducts = await _productService.getRelatedProducts(
        productId: productId,
        lat: locationData.lat,
        long: locationData.long,
        warehouseType: warehouseType,
        languageCode: languageCode,
      );

      state = state.copyWith(
        products: relatedProducts,
        isLoading: false,
        clearError: true,
      );

      debugPrint('Related products fetched successfully: ${relatedProducts.length} products');
    } catch (e) {
      debugPrint('Error fetching related products: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load related products: $e',
      );
    }
  }

  /// Clear the related products state
  void clear() {
    state = const RelatedProductsState();
  }
}

/// Provider for related products state
final relatedProductsProvider =
    StateNotifierProvider<RelatedProductsNotifier, RelatedProductsState>((ref) {
  return RelatedProductsNotifier(
    productService: ProductService(),
    ref: ref,
  );
});
