import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/model/order.dart';
import 'package:vegmove_ecommerce/providers/address_provider.dart';
import 'package:vegmove_ecommerce/providers/delivery_provider.dart';
import 'package:vegmove_ecommerce/providers/language_provider.dart';
import 'package:vegmove_ecommerce/providers/warehouse_provider.dart';
import 'package:vegmove_ecommerce/services/order_service.dart';

/// Provider for the current order being viewed
final currentOrderProvider = StateProvider<Order?>((ref) => null);

/// Provider for the orders list state
final ordersProvider =
    StateNotifierProvider<OrdersNotifier, OrdersState>((ref) {
  final languageCode = ref.watch(languageProvider).code;

  return OrdersNotifier(
    orderService: OrderService(),
    languageCode: languageCode,
  );
});

/// State for the orders list
class OrdersState {
  final bool isLoading;
  final String? error;
  final List<Order> orders;
  final int currentPage;
  final int totalPages;
  final bool hasMore;

  OrdersState({
    this.isLoading = false,
    this.error,
    this.orders = const [],
    this.currentPage = 1,
    this.totalPages = 1,
    this.hasMore = false,
  });

  OrdersState copyWith({
    bool? isLoading,
    String? error,
    List<Order>? orders,
    int? currentPage,
    int? totalPages,
    bool? hasMore,
  }) {
    return OrdersState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      orders: orders ?? this.orders,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

/// Notifier for the orders list
class OrdersNotifier extends StateNotifier<OrdersState> {
  final OrderService orderService;
  final String languageCode;

  OrdersNotifier({
    required this.orderService,
    required this.languageCode,
  }) : super(OrdersState());

  /// Load orders
  Future<void> loadOrders({bool refresh = false}) async {
    try {
      final page = refresh ? 1 : state.currentPage;

      if (page == 1) {
        state = state.copyWith(isLoading: true, error: null);
      }

      final response = await orderService.getOrders(
        page: page,
        pageSize: 10,
        languageCode: languageCode,
      );

      final orders =
          page == 1 ? response.data : [...state.orders, ...response.data];

      state = state.copyWith(
        isLoading: false,
        orders: orders,
        currentPage: response.page,
        totalPages: response.totalPages,
        hasMore: response.page < response.totalPages,
      );
    } catch (e, stacktrace) {
      log(e.toString());
      log(stacktrace.toString());
      print(e.toString());
      print(stacktrace.toString());
      debugPrint(e.toString());
      debugPrintStack(stackTrace: stacktrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load orders: $e',
      );
    }
  }

  /// Load more orders
  Future<void> loadMore() async {
    if (state.hasMore && !state.isLoading) {
      state = state.copyWith(currentPage: state.currentPage + 1);
      await loadOrders();
    }
  }

  /// Get order by ID
  Future<Order?> getOrderById(int id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final order =
          await orderService.getOrderById(id, languageCode: languageCode);
      state = state.copyWith(isLoading: false);
      return order;
    } catch (e, stacktrace) {
      log(e.toString());
      log(stacktrace.toString());
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load order: $e',
      );
      return null;
    }
  }

  /// Cancel order
  Future<bool> cancelOrder(int id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await orderService.cancelOrder(id);

      // Refresh orders after cancellation
      await loadOrders(refresh: true);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to cancel order: $e',
      );
      return false;
    }
  }
}

/// Provider for placing a new order
final placeOrderProvider =
    FutureProvider.autoDispose.family<Order?, Map<String, dynamic>>(
  (ref, orderData) async {
    try {
      final orderService = OrderService();
      final selectedAddress = ref.read(selectedAddressProvider);
      final deliveryDate = ref.read(selectedDeliveryDateProvider);
      final deliverySlot = ref.read(selectedDeliverySlotProvider);
      final warehouseType = ref.read(warehouseTypeProvider).type;

      if (selectedAddress == null ||
          deliveryDate == null ||
          deliverySlot == null) {
        throw Exception('Missing required order information');
      }

      final order = await orderService.createOrder(
        shippingAddressId: selectedAddress.id,
        deliveryDate: deliveryDate,
        deliveryStartTime: deliverySlot.startTime ?? '',
        deliveryEndTime: deliverySlot.endTime ?? '',
        lat: double.parse(selectedAddress.lat),
        long: double.parse(selectedAddress.long),
        warehouseType: warehouseType,
        note: orderData['note'] as String?,
        couponCode: orderData['couponCode'] as String?,
        useRewardPoints: orderData['useRewardPoints'] as bool? ?? false,
      );

      return order;
    } catch (e, stacktrace) {
      log('Error placing order: $e');
      log(stacktrace.toString());
      return null;
    }
  },
);
