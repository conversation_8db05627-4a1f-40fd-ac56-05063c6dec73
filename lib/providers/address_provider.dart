import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_ecommerce/model/address.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/services/address_service.dart';

/// Provider for the address state
final addressProvider =
    StateNotifierProvider<AddressNotifier, AddressState>((ref) {
  return AddressNotifier(addressService: AddressService());
});

/// Provider for the selected address
final selectedAddressProvider =
    StateNotifierProvider<SelectedAddressNotifier, Address?>((ref) {
  final addressState = ref.watch(addressProvider);
  return SelectedAddressNotifier(addresses: addressState.addresses);
});

/// Notifier for the selected address
class SelectedAddressNotifier extends StateNotifier<Address?> {
  final List<Address> addresses;

  SelectedAddressNotifier({required this.addresses}) : super(null) {
    _loadSavedAddress();
  }

  /// Load the saved address from SharedPreferences
  Future<void> _loadSavedAddress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedAddressId = prefs.getInt('selectedAddressId');

      if (savedAddressId != null && addresses.isNotEmpty) {
        // Try to find the saved address in the list
        final savedAddress = addresses.firstWhere(
          (address) => address.id == savedAddressId,
          orElse: () => addresses.first,
        );
        state = savedAddress;
      } else if (addresses.isNotEmpty) {
        // If no saved address, use the first one
        state = addresses.first;
      }
    } catch (e) {
      debugPrint('Error loading saved address: $e');
      if (addresses.isNotEmpty) {
        state = addresses.first;
      }
    }
  }

  /// Select an address and save it to SharedPreferences
  Future<void> selectAddress(Address address) async {
    state = address;
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('selectedAddressId', address.id);
    } catch (e) {
      debugPrint('Error saving selected address: $e');
    }
  }
}

/// State for the address provider
class AddressState {
  final bool isLoading;
  final List<Address> addresses;
  final String? error;

  AddressState({
    this.isLoading = false,
    this.addresses = const [],
    this.error,
  });

  AddressState copyWith({
    bool? isLoading,
    List<Address>? addresses,
    String? error,
  }) {
    return AddressState(
      isLoading: isLoading ?? this.isLoading,
      addresses: addresses ?? this.addresses,
      error: error,
    );
  }
}

/// Notifier for the address provider
class AddressNotifier extends StateNotifier<AddressState> {
  final AddressService addressService;

  AddressNotifier({required this.addressService}) : super(AddressState()) {
    // Load addresses when the provider is created
    loadAddresses();
  }

  /// Load all addresses for the current user
  Future<void> loadAddresses() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final addresses = await addressService.getAddresses();
      state = state.copyWith(
        isLoading: false,
        addresses: addresses,
      );
    } catch (e, stacktrace) {
      debugPrint('Error loading addresses: $e');
      debugPrint(stacktrace.toString());
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load addresses: $e',
      );
    }
  }

  /// Create a new address
  Future<Address?> createAddress({
    required AddressType type,
    required String streetName,
    required String city,
    required String state,
    required int countryId,
    required String zipCode,
    required String lat,
    required String long,
    String? apartment,
    String? block,
  }) async {
    try {
      this.state = this.state.copyWith(isLoading: true, error: null);
      final address = await addressService.createAddress(
        type: type,
        streetName: streetName,
        city: city,
        state: state,
        countryId: countryId,
        zipCode: zipCode,
        lat: lat,
        long: long,
        apartment: apartment,
        block: block,
      );

      // Add the new address to the list
      final updatedAddresses = [...this.state.addresses, address];
      this.state = this.state.copyWith(
            isLoading: false,
            addresses: updatedAddresses,
          );
      return address;
    } catch (e) {
      this.state = this.state.copyWith(
            isLoading: false,
            error: 'Failed to create address: $e',
          );
      return null;
    }
  }

  /// Update an existing address
  Future<bool> updateAddress(Address address) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Call the API to update the address
      final updatedAddress = await addressService.updateAddress(
        id: address.id,
        type: address.type,
        streetName: address.streetName,
        city: address.city,
        state: address.state,
        countryId: address.countryId,
        zipCode: address.zipCode,
        apartment: address.apartment,
        block: address.block,
        lat: address.lat,
        long: address.long,
      );

      // Update the address in the local state
      final updatedAddresses = state.addresses.map((a) {
        if (a.id == updatedAddress.id) {
          return updatedAddress;
        }
        return a;
      }).toList();

      state = state.copyWith(
        isLoading: false,
        addresses: updatedAddresses,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update address: $e',
      );
      return false;
    }
  }

  /// Delete an address
  Future<bool> deleteAddress(int addressId) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Call the API to delete the address
      await addressService.deleteAddress(addressId);

      // Update the local state by removing the deleted address
      final updatedAddresses =
          state.addresses.where((a) => a.id != addressId).toList();

      state = state.copyWith(
        isLoading: false,
        addresses: updatedAddresses,
      );
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to delete address: $e',
      );
      return false;
    }
  }
}
