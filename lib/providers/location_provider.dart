import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_ecommerce/providers/address_provider.dart';
import 'package:vegmove_ecommerce/util.dart';

final locationProvider = ChangeNotifierProvider<LocationNotifier>((ref) {
  // Watch the selected address provider to update location when address changes
  final selectedAddress = ref.watch(selectedAddressProvider);

  // Create the location notifier
  final locationNotifier = LocationNotifier();

  // Initialize with saved location from SharedPreferences or current location
  // This is the highest priority - we'll always use this location
  locationNotifier.initializeWithSavedLocation(selectedAddress);

  return locationNotifier;
});

class LocationNotifier extends ChangeNotifier {
  // Default location (Dhaka)
  double _lat = 23.8103;
  double _long = 90.4125;
  bool _isLoading = false;
  String? _error;
  String _address = '';
  bool _addressLoading = false;

  double get lat => _lat;
  double get long => _long;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String get address => _address;
  bool get addressLoading => _addressLoading;

  LocationNotifier() {
    // We'll only get the current location if no address is selected
    // The provider will set the location from the selected address if available
  }

  // Method to initialize location if no address is selected
  void initializeLocation() {
    _loadSavedLocation();
  }

  // Initialize with saved location from SharedPreferences, prioritizing it over selected address
  Future<void> initializeWithSavedLocation(dynamic selectedAddress) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLat = prefs.getDouble('selected_location_lat');
      final savedLng = prefs.getDouble('selected_location_lng');
      final savedAddress = prefs.getString('selected_location_address');

      if (savedLat != null && savedLng != null) {
        // Use the saved location from SharedPreferences (highest priority)
        _lat = savedLat;
        _long = savedLng;

        if (savedAddress != null && savedAddress.isNotEmpty) {
          _address = savedAddress;
        } else {
          // Get address for the saved location
          await getAddressFromCoordinates(savedLat, savedLng);
        }

        notifyListeners();
      } else if (selectedAddress != null) {
        // If no saved location, fall back to selected address
        try {
          final lat = double.parse(selectedAddress.lat);
          final long = double.parse(selectedAddress.long);

          // Update the location with the selected address coordinates
          await setLocation(lat, long);
          await setAddress(selectedAddress.streetName);
        } catch (e) {
          debugPrint('Error parsing address coordinates: $e');
          // If we can't parse the coordinates, get current location
          getCurrentLocation();
        }
      } else {
        // No saved location or selected address, get current location
        getCurrentLocation();
      }
    } catch (e) {
      debugPrint('Error loading saved location: $e');
      // Fall back to getting current location
      getCurrentLocation();
    }
  }

  // Load saved location from SharedPreferences
  Future<void> _loadSavedLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLat = prefs.getDouble('selected_location_lat');
      final savedLng = prefs.getDouble('selected_location_lng');
      final savedAddress = prefs.getString('selected_location_address');

      if (savedLat != null && savedLng != null) {
        // Use the saved location
        _lat = savedLat;
        _long = savedLng;

        if (savedAddress != null && savedAddress.isNotEmpty) {
          _address = savedAddress;
        } else {
          // Get address for the saved location
          await getAddressFromCoordinates(savedLat, savedLng);
        }

        notifyListeners();
      } else {
        // No saved location, get current location
        getCurrentLocation();
      }
    } catch (e) {
      debugPrint('Error loading saved location: $e');
      // Fall back to getting current location
      getCurrentLocation();
    }
  }

  Future<void> setLocation(double lat, double long) async {
    _lat = lat;
    _long = long;
    _error = null;
    notifyListeners();

    // Save to SharedPreferences
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('selected_location_lat', lat);
      await prefs.setDouble('selected_location_lng', long);
    } catch (e) {
      debugPrint('Error saving location to SharedPreferences: $e');
    }

    // Get address for the new location
    await getAddressFromCoordinates(lat, long);
  }

  /// Set the address directly (used when selecting from saved addresses)
  Future<void> setAddress(String address) async {
    _address = address;
    _error = null;
    notifyListeners();

    // Save to SharedPreferences
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selected_location_address', address);
    } catch (e) {
      debugPrint('Error saving address to SharedPreferences: $e');
    }
  }

  Future<void> getAddressFromCoordinates(double lat, double long) async {
    _addressLoading = true;
    notifyListeners();

    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(lat, long);
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        _address = [
          place.street,
          place.subLocality,
          place.locality,
          place.postalCode,
          place.country,
        ].where((element) => element != null && element.isNotEmpty).join(', ');
      } else {
        _address = 'Your Location:';
      }

      // Save the address to SharedPreferences
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('selected_location_address', _address);
      } catch (e) {
        debugPrint('Error saving address to SharedPreferences: $e');
      }
    } catch (e) {
      _address = 'Your Location:';
      debugPrint('Error getting address: $e');
    }

    _addressLoading = false;
    notifyListeners();
  }

  Future<void> getCurrentLocation() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _error =
            'Location services are disabled. Please enable location services.';
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Check for location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _error =
              'Location permissions are denied. Please enable location permissions.';
          _isLoading = false;
          notifyListeners();
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _error =
            'Location permissions are permanently denied. Please enable location permissions in settings.';
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Get the current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      _lat = position.latitude;
      _long = position.longitude;

      // Get address for the current location
      await getAddressFromCoordinates(_lat, _long);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Error getting location: $e';
      _isLoading = false;
      notifyListeners();

      // Log the error
      debugPrint('Error getting location: $e');

      // Show a toast message
      Util.showErrorToast(
          'Could not get your location. Using default location.');
    }
  }
}
