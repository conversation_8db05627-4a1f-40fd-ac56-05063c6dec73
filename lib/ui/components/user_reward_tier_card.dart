import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/reward_tier.dart';
import 'package:vegmove_ecommerce/model/user.dart';
import 'package:vegmove_ecommerce/services/reward_tier_service.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

/// Provider to fetch all reward tiers for computing progress
final allRewardTiersProvider = FutureProvider<List<RewardTier>>(
  (ref) => RewardTierService().getAllRewardTiers(),
);

/// Provider to fetch all reward tiers for computing progress
final rollingSpendProvider = FutureProvider<double>(
  (ref) => RewardTierService().getRollingSpend(),
);

class UserRewardTierCard extends ConsumerWidget {
  final User user;

  const UserRewardTierCard({super.key, required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tiersAsync = ref.watch(allRewardTiersProvider);
    final rollingSpendAsync = ref.watch(rollingSpendProvider);

    return GestureDetector(
      onTap: () {
        // Navigate to reward tiers screen
        context.push('/reward-tiers');
      },
      child: rollingSpendAsync.when(
        data: (rollingSpend) {
          return tiersAsync.when(
            data: (tiers) {
              final currentTier = user.rewardTier;
              if (currentTier == null ||
                  currentTier.name.toString().toLowerCase() == 'none') {
                // Show blank card for None tier with proper margin
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: _buildNoneTierCard(),
                );
              }

              // Determine next tier (for progress calculation)
              final currentIndex =
                  tiers.indexWhere((t) => t.id == currentTier.id);
              final nextTier =
                  (currentIndex >= 0 && currentIndex < tiers.length - 1)
                      ? tiers[currentIndex + 1]
                      : currentTier;

              // Progress = current points / points needed for next tier
              final progress =
                  (rollingSpend / int.parse(nextTier.requiredRollingSpend))
                      .clamp(0, 1)
                      .toDouble();

              return SizedBox(
                width: double.infinity,
                // Fixed height to accommodate card + overlapping pill
                height: 220,
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Background SVG
                    Positioned.fill(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: SvgPicture.asset(
                          'assets/images/${currentTier.type.toLowerCase()}_background.svg',
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    // Top row: name, badge, icon
                    Positioned(
                      top: 16,
                      left: 16,
                      right: 16,
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              user.name,
                              style: const TextStyle(
                                fontSize: 20,
                                color: AppColors.black,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              currentTier.name,
                              style: const TextStyle(color: AppColors.darkGrey),
                            ),
                          ),
                          const SizedBox(width: 8),
                          // Tier icon in top-right
                          SvgPicture.asset(
                            'assets/images/${currentTier.type.toLowerCase()}_icon.svg',
                            width: 32,
                            height: 32,
                          ),
                        ],
                      ),
                    ),
                    // Points, progress bar, and subtext
                    Positioned(
                      top: 56,
                      left: 16,
                      right: 16,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Available Points',
                            style: TextStyle(color: AppColors.black),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Image.asset(
                                'assets/images/ic_points.png',
                                width: 24,
                                height: 24,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${user.rewardPoints.toInt()}',
                                style: const TextStyle(
                                  fontSize: 32,
                                  color: AppColors.black,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: LinearProgressIndicator(
                              value: progress,
                              minHeight: 4,
                              backgroundColor: AppColors.grey,
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                  AppColors.black),
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Using points won\'t reduce your progress',
                            style: TextStyle(color: AppColors.black),
                          ),
                        ],
                      ),
                    ),
                    // Overlapping bottom pill
                    Positioned(
                      bottom: -16,
                      left: 16,
                      right: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          'Spend at least ₹${currentTier.requiredRollingSpend} per month',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                              color: Colors.black87,
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
            loading: () => const SizedBox(
              height: 200,
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (e, st) => const SizedBox.shrink(),
          );
        },
        loading: () => const CircularProgressIndicator(),
        error: (error, stackTrace) => Text('Error: $error'),
      ),
    );
  }

  // Build a blank card for users with no tier
  Widget _buildNoneTierCard() {
    // Get the next tier information
    return FutureBuilder<List<RewardTier>>(
      future: RewardTierService().getAllRewardTiers(),
      builder: (context, tiersSnapshot) {
        return FutureBuilder<double>(
          future: RewardTierService().getRollingSpend(),
          builder: (context, spendSnapshot) {
            // Default next tier amount if data is still loading
            String nextTierAmount = '...';
            double currentSpend = 0;

            // If we have the tiers data, find the first non-NONE tier
            if (tiersSnapshot.hasData) {
              final tiers = tiersSnapshot.data!;
              // Create a new list to avoid modifying the original
              final sortedTiers = List<RewardTier>.from(tiers)
                  .where((tier) => tier.type.toLowerCase() != 'none')
                  .toList()
                ..sort((a, b) => int.parse(a.requiredRollingSpend)
                    .compareTo(int.parse(b.requiredRollingSpend)));

              if (sortedTiers.isNotEmpty) {
                nextTierAmount = sortedTiers.first.requiredRollingSpend;
              }
            }

            // Get current spending if available
            if (spendSnapshot.hasData) {
              currentSpend = spendSnapshot.data!;
            }

            // Simple card with fixed layout
            return Card(
              margin: EdgeInsets.zero,
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top section with logo and tier
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Image.asset(
                          'assets/images/logo.png',
                          width: 40,
                          height: 40,
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'None',
                            style: TextStyle(color: AppColors.darkGrey),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Points section
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Available Points',
                          style: TextStyle(color: AppColors.black),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Image.asset(
                              'assets/images/ic_points.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(width: 4),
                            const Text(
                              '0',
                              style: TextStyle(
                                fontSize: 32,
                                color: AppColors.black,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Next tier section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(16),
                        bottomRight: Radius.circular(16),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Next Tier',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColors.black,
                              ),
                            ),
                            if (spendSnapshot.hasData)
                              Text(
                                '₹${currentSpend.toStringAsFixed(0)} spent',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.primary,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Spend ₹$nextTierAmount to unlock Bronze',
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.darkGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
