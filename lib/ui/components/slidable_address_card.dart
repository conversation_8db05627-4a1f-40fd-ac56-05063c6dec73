import 'package:flutter/material.dart';
import 'package:vegmove_ecommerce/model/address.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class SlidableAddressCard extends StatefulWidget {
  final Address address;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const SlidableAddressCard({
    super.key,
    required this.address,
    required this.isSelected,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  State<SlidableAddressCard> createState() => _SlidableAddressCardState();
}

class _SlidableAddressCardState extends State<SlidableAddressCard> {
  final GlobalKey _backgroundKey = GlobalKey();
  final GlobalKey _foregroundKey = GlobalKey();
  final GlobalKey _actionsKey = GlobalKey();

  double _dragExtent = 0;
  double _dragThreshold = 0;
  bool _isOpen = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateDragThreshold();
    });
  }

  void _updateDragThreshold() {
    final RenderBox? actionsBox =
        _actionsKey.currentContext?.findRenderObject() as RenderBox?;
    if (actionsBox != null) {
      _dragThreshold = actionsBox.size.width;
    }
  }

  void _handleDragUpdate(DragUpdateDetails details) {
    setState(() {
      _dragExtent = (_dragExtent + details.delta.dx).clamp(-_dragThreshold, 0);
      _isOpen = _dragExtent.abs() > _dragThreshold / 2;
    });
  }

  void _handleDragEnd(DragEndDetails details) {
    setState(() {
      if (_isOpen) {
        _dragExtent = -_dragThreshold;
      } else {
        _dragExtent = 0;
      }
    });
  }

  void _close() {
    setState(() {
      _dragExtent = 0;
      _isOpen = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final String addressText = [
      if (widget.address.apartment.isNotEmpty)
        'Flat No. ${widget.address.apartment}',
      if (widget.address.block.isNotEmpty) 'Block ${widget.address.block}',
      widget.address.streetName,
    ].join(', ');

    return GestureDetector(
      onTap: () {
        if (_isOpen) {
          _close();
        } else {
          widget.onTap();
        }
      },
      onHorizontalDragUpdate: _handleDragUpdate,
      onHorizontalDragEnd: _handleDragEnd,
      child: Stack(
        children: [
          // Background with actions
          Container(
            key: _backgroundKey,
            margin: const EdgeInsets.only(bottom: 16),
            height: 100, // Fixed height to match action buttons
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              key: _actionsKey,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Edit button
                GestureDetector(
                  onTap: () {
                    _close();
                    widget.onEdit();
                  },
                  child: Container(
                    width: 80,
                    height: 100, // Fixed height instead of infinity
                    decoration: const BoxDecoration(
                      color: Color(0xFF4CAF50),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        bottomLeft: Radius.circular(16),
                      ),
                    ),
                    child: const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.edit, color: Colors.white),
                        SizedBox(height: 4),
                        Text(
                          'Edit',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Delete button
                GestureDetector(
                  onTap: () {
                    _close();
                    widget.onDelete();
                  },
                  child: Container(
                    width: 80,
                    height: 100, // Fixed height instead of infinity
                    decoration: const BoxDecoration(
                      color: Colors.redAccent,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(16),
                        bottomRight: Radius.circular(16),
                      ),
                    ),
                    child: const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.delete, color: Colors.white),
                        SizedBox(height: 4),
                        Text(
                          'Delete',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Foreground card
          AnimatedContainer(
            key: _foregroundKey,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeOut,
            transform: Matrix4.translationValues(_dragExtent, 0, 0),
            margin: const EdgeInsets.only(bottom: 16),
            height: 100, // Fixed height to match background and action buttons
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: widget.isSelected
                    ? AppColors.primary
                    : const Color(0xFFE3E9E6),
                width: widget.isSelected ? 2 : 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  // Location icon
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE8F1ED),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: const Icon(
                      Icons.location_on,
                      color: AppColors.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Address details
                  Expanded(
                    child: Column(
                      mainAxisSize:
                          MainAxisSize.min, // Use minimum space needed
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.address.streetName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          addressText,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                          maxLines: 1, // Limit to 1 line to fit in fixed height
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  // Selected indicator
                  if (widget.isSelected)
                    Container(
                      width: 24,
                      height: 24,
                      decoration: const BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
