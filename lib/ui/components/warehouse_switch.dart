import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';
import 'package:vegmove_ecommerce/providers/warehouse_provider.dart';

class WarehouseSwitch extends ConsumerStatefulWidget {
  const WarehouseSwitch({super.key});

  @override
  ConsumerState<WarehouseSwitch> createState() => _WarehouseSwitchState();
}

class _WarehouseSwitchState extends ConsumerState<WarehouseSwitch>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  static const double _width = 120;
  static const double _height = 48;
  static const double _thumbSize = 40;
  static const double _thumbPadding = (_height - _thumbSize) / 2;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
      value: 0.0,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final warehouseType = ref.watch(warehouseTypeProvider).type;
    final isSuper = warehouseType == WarehouseType.SUPER;

    // drive the animation
    if (isSuper) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    return GestureDetector(
      onTap: () {
        final newType = isSuper ? WarehouseType.GENERAL : WarehouseType.SUPER;
        ref.read(warehouseTypeProvider.notifier).setType(newType);
      },
      child: Container(
        width: _width,
        height: _height,
        decoration: BoxDecoration(
          color: isSuper ? const Color(0xFFFFEA4D) : const Color(0xFFDCEDE4),
          borderRadius: BorderRadius.circular(_height / 2),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            // sliding thumb
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Positioned(
                  left: _animation.value * (_width - _thumbSize),
                  top: _thumbPadding,
                  child: Container(
                    width: _thumbSize,
                    height: _thumbSize,
                    decoration: BoxDecoration(
                      color: isSuper ? AppColors.primary : Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSuper ? Colors.white : AppColors.primary,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),

            // Super Store SVG label, sliding opposite to thumb
            Positioned.fill(
              child: AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  // alignment goes from center-right (0) to center-left (1)
                  final align = Alignment.lerp(
                    Alignment.centerRight,
                    Alignment.centerLeft,
                    _animation.value,
                  )!;
                  return Align(alignment: align, child: child);
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: SvgPicture.asset(
                    'assets/images/ic_super_store.svg',
                    height: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
