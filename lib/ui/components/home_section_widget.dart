import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/home_section.dart';
import 'package:vegmove_ecommerce/ui/components/category_card.dart';
import 'package:vegmove_ecommerce/ui/components/product_card.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class HomeSectionWidget extends StatelessWidget {
  final HomeSection section;

  const HomeSectionWidget({
    super.key,
    required this.section,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        if (section.type == HomeSectionType.PRODUCT)
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'Hot Deals',
                style: TextStyle(
                  fontSize: 18,
                  color: Color(0xFF9A9C9E),
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    section.title,
                    style: TextStyle(
                      fontSize: 18,
                      color: Color(0xFF1A1A1A),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          )
        else
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                section.title,
                style: AppTextStyles.h3,
              ),
            ],
          ),

        const SizedBox(height: 16),

        // Section content
        if (section.type == HomeSectionType.CATEGORY &&
            section.categories != null)
          SizedBox(
            height: 180,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: section.categories!.length,
              itemBuilder: (context, index) {
                final category = section.categories![index];
                return CategoryCard(
                  category: category,
                  onTap: () {
                    context.goNamed(
                      'category-details',
                      pathParameters: {'categoryId': category.id.toString()},
                    );
                  },
                );
              },
            ),
          )
        else if (section.type == HomeSectionType.PRODUCT &&
            section.products != null)
          SizedBox(
            height: 400, // Further increased height for taller cards
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.zero, // Remove padding from ListView
              itemCount: section.products!.length,
              itemBuilder: (context, index) {
                final product = section.products![index];
                return Padding(
                  // Add padding only on the right side for the first item, otherwise on both sides
                  padding: EdgeInsets.only(
                    left: index == 0 ? 0 : 8,
                    right: 8,
                  ),
                  child: ProductCard(
                    product: product,
                    onTap: () {
                      context.goNamed(
                        'product-details',
                        pathParameters: {
                          'productId': product.id.toString(),
                          'slug': product.slug!
                        },
                      );
                    },
                  ),
                );
              },
            ),
          ),

        const SizedBox(height: 24),
      ],
    );
  }
}
