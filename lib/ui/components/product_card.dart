import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/product.dart';
import 'package:vegmove_ecommerce/providers/cart_provider.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';
import 'package:vegmove_ecommerce/util.dart';

class ProductCard extends ConsumerWidget {
  final Product product;
  final VoidCallback? onTap;

  const ProductCard({
    super.key,
    required this.product,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check if product has a discount
    final hasDiscount =
        product.discountValue != null && product.discountValue! > 0;

    // Get cart state to check if product is in cart
    final cartState = ref.watch(cartProvider);
    final isInCart = cartState.isProductInCart(product.id);
    final quantity = isInCart ? cartState.getProductQuantity(product.id) : 0;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 180,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image
            Padding(
              padding: const EdgeInsets.all(8),
              child: AspectRatio(
                aspectRatio: 1, // 1:1 ratio for the image container
                child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(12)),
                  child: Container(
                    color: const Color(
                        0xFFE8F1ED), // Green background as per design
                    child: product.thumbnail != null
                        ? Image.network(
                            product.thumbnail!.url,
                            fit: BoxFit.contain, // Better product visibility
                            errorBuilder: (context, error, stackTrace) =>
                                const Icon(
                              Icons.image,
                              size: 40,
                              color: Colors.grey,
                            ),
                          )
                        : product.media.isNotEmpty
                            ? Image.network(
                                product.media.first.url,
                                fit:
                                    BoxFit.contain, // Better product visibility
                                errorBuilder: (context, error, stackTrace) =>
                                    const Icon(
                                  Icons.image,
                                  size: 40,
                                  color: Colors.grey,
                                ),
                              )
                            : const Icon(
                                Icons.image,
                                size: 40,
                                color: Colors.grey,
                              ),
                  ),
                ),
              ),
            ),

            // Product details
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product name
                  Text(
                    product.name,
                    style: AppTextStyles.bodySmall.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),

                  Text(
                    '${product.weight} ${product.weightUnit.name.toLowerCase()}',
                    style: AppTextStyles.caption.copyWith(
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  // Price section
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Expanded(
                        child: hasDiscount
                            ? _buildDiscountedPriceSection()
                            : _buildRegularPriceSection(),
                      )
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Add to bag button or quantity selector
                  product.inStock
                      ? isInCart
                          ? _buildQuantitySelector(context, ref, quantity)
                          : _buildAddToBagButton(context, ref)
                      : _buildOutOfStockButton(context, ref),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscountedPriceSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFFFEB3B), // Yellow background for discount price
        borderRadius: BorderRadius.circular(100),
        border: Border.all(color: Color(0xFF786A09)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            '₹${product.price}',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w700,
              color: Colors.red,
              fontSize: 15,
            ),
          ),
          Text(
            '₹${product.originalPrice}',
            style: AppTextStyles.caption.copyWith(
                decoration: TextDecoration.lineThrough,
                decorationColor: Colors.red,
                decorationThickness: 2,
                color: Color(0xFF786A09).withOpacity(0.7),
                fontSize: 13,
                fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Widget _buildRegularPriceSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(100),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          Text(
            '₹${product.price}',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddToBagButton(BuildContext context, WidgetRef ref) {
    return ElevatedButton.icon(
      onPressed: () async {
        // Check if user is logged in
        final isLoggedIn = await Util.isUserLoggedIn();
        if (isLoggedIn) {
          ref.read(cartProvider.notifier).addToCart(product);
        } else if (context.mounted) {
          // Navigate to login screen
          context.pushNamed('login');
        }
      },
      icon: SvgPicture.asset(
        'assets/images/ic_bag.svg',
        colorFilter: const ColorFilter.mode(
          Color(0xFF1A1A1A),
          BlendMode.srcIn,
        ),
        height: 16,
      ),
      label: const Text('Add to Bag'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        foregroundColor: Color(0xFF1A1A1A),
        textStyle: AppTextStyles.caption.copyWith(
          fontWeight: FontWeight.w600,
        ),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        minimumSize: const Size(double.infinity, 36),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(100),
          side: const BorderSide(color: Color(0xFF1A1A1A), width: 1),
        ),
      ),
    );
  }

  Widget _buildOutOfStockButton(BuildContext context, WidgetRef ref) {
    return ElevatedButton.icon(
      onPressed: () {},
      icon: SvgPicture.asset(
        'assets/images/ic_bag.svg',
        colorFilter: const ColorFilter.mode(
          Colors.red,
          BlendMode.srcIn,
        ),
        height: 16,
      ),
      label: const Text(
        'Out of Stock',
        style: TextStyle(
          fontWeight: FontWeight.w600,
          color: Colors.red,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        foregroundColor: Color(0xFF1A1A1A),
        textStyle: AppTextStyles.caption.copyWith(
          fontWeight: FontWeight.w600,
          color: Colors.red,
        ),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        minimumSize: const Size(double.infinity, 36),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(100),
          side: const BorderSide(color: Colors.red, width: 1),
        ),
      ),
    );
  }

  Widget _buildQuantitySelector(
      BuildContext context, WidgetRef ref, int quantity) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            border: Border.all(color: Color(0xFFE3E9E6)),
          ),
          child: IconButton(
            icon: const Icon(Icons.remove),
            onPressed: () async {
              // Check if user is logged in
              final isLoggedIn = await Util.isUserLoggedIn();
              if (isLoggedIn) {
                ref
                    .read(cartProvider.notifier)
                    .updateQuantity(product.id, quantity - 1);
              } else if (context.mounted) {
                // Navigate to login screen
                context.pushNamed('login');
              }
            },
            iconSize: 16,
            padding: EdgeInsets.zero,
            color: AppColors.darkGrey,
          ),
        ),
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: AppColors.lightGrey,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              quantity.toString(),
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ),
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            border: Border.all(color: Color(0xFFE3E9E6)),
          ),
          child: IconButton(
            icon: const Icon(Icons.add),
            onPressed: () async {
              // Check if user is logged in
              final isLoggedIn = await Util.isUserLoggedIn();
              if (isLoggedIn) {
                if (product.maxCartQuantity != null &&
                    quantity >= product.maxCartQuantity!) {
                  Util.showErrorToast(
                      'Maximum ${product.maxCartQuantity} items allowed in cart for this product');
                  return;
                }
                ref
                    .read(cartProvider.notifier)
                    .updateQuantity(product.id, quantity + 1);
              } else if (context.mounted) {
                // Navigate to login screen
                context.pushNamed('login');
              }
            },
            iconSize: 16,
            padding: EdgeInsets.zero,
            color: AppColors.darkGrey,
          ),
        ),
      ],
    );
  }
}
