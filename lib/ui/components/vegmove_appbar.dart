import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/providers/location_provider.dart';
import 'package:vegmove_ecommerce/providers/address_provider.dart';
import 'package:vegmove_ecommerce/providers/user_provider.dart';
import 'package:vegmove_ecommerce/ui/screens/location_picker/location_picker_screen.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

// Using the centralized user provider from user_provider.dart

class VegmoveAppBar extends ConsumerWidget {
  const VegmoveAppBar({super.key});

  @override
  Widget build(BuildContext context, ref) {
    final locationData = ref.watch(locationProvider);
    final userState = ref.watch(userProvider);
    final selectedAddress = ref.watch(selectedAddressProvider);

    // Check if user is logged in based on the user state
    final isLoggedIn = userState.isLoggedIn;

    return AppBar(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      automaticallyImplyLeading: false,
      toolbarHeight: 80,
      title: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                // Navigate to the location picker screen
                context.pushNamed(
                  'location-picker',
                  extra: (LocationPickerResult result) {
                    // Update the location provider with the new location
                    if (result.isWithinZone) {
                      // Using async method
                      ref.read(locationProvider.notifier).setLocation(
                            result.location.latitude,
                            result.location.longitude,
                          );
                    }
                  },
                );
              },
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: SizedBox(
                        height: 24,
                        width: 24,
                        child: SvgPicture.asset(
                          'assets/images/ic_location.svg',
                          height: 24,
                          width: 24,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Delivery to',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          selectedAddress?.streetName != null
                              ? selectedAddress!.streetName
                              : locationData.address.isNotEmpty
                                  ? locationData.address
                                  : 'Detecting location...',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // User profile section - show different UI based on login status
          isLoggedIn
              ? _buildLoggedInUserProfile(userState)
              : _buildLoginButton(context),
        ],
      ),
    );
  }

  // Widget for logged-in user profile
  Widget _buildLoggedInUserProfile(UserState userState) {
    final user = userState.user;

    if (user == null) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              user.rewardTier?.name.toLowerCase() != 'none' &&
                      user.rewardTier != null
                  ? user.rewardTier!.name
                  : 'Platinum',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(
                  Icons.monetization_on,
                  color: AppColors.secondary,
                  size: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  user.rewardTier?.name.toLowerCase() != 'None'
                      ? user.rewardPoints.toString()
                      : '',
                  style: AppTextStyles.h4,
                ),
              ],
            ),
          ],
        ),
        const SizedBox(width: 12),
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.person,
            color: AppColors.darkGrey,
            size: 30,
          ),
        ),
      ],
    );
  }

  // Widget for login button when user is not logged in
  Widget _buildLoginButton(BuildContext context) {
    return GestureDetector(
      onTap: () => context.pushNamed('login'),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(20),
        ),
        child: const Text(
          'Login',
          style: TextStyle(
            fontSize: 14,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
