import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class CategoryCard extends StatelessWidget {
  final Category category;
  final VoidCallback? onTap;

  const CategoryCard({
    super.key,
    required this.category,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ??
          () {
            // Navigate to category details screen
            context.goNamed(
              'category-details',
              pathParameters: {'categoryId': category.id.toString()},
            );
          },
      child: Container(
        width: 120,
        margin: const EdgeInsets.only(right: 12),
        child: Column(
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                // Only use background color for fallback icon
                color: category.iconUrl == null
                    ? AppColors.categoryCardBackground
                    : null,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: category.iconUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Image.network(
                          category.iconUrl!,
                          width: 120, // Full width of container
                          height: 120, // Full height of container
                          fit: BoxFit.cover, // Cover the entire container
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              color: AppColors.categoryCardBackground,
                              child: Center(
                                child: CircularProgressIndicator(
                                  color: AppColors.primary,
                                  value: loadingProgress.expectedTotalBytes !=
                                          null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                                ),
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                            color: AppColors.categoryCardBackground,
                            child: const Icon(
                              Icons.category,
                              size: 40,
                              color: AppColors.primary,
                            ),
                          ),
                        ),
                      )
                    : const Icon(
                        Icons.category,
                        size: 40,
                        color: AppColors.primary,
                      ),
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Text(
                category.name,
                textAlign: TextAlign.center,
                style: AppTextStyles.caption.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                  color: Color(0xFF1A1A1A),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
