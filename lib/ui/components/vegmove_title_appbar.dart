import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class VegmoveTitleAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  @override
  Size get preferredSize => const Size.fromHeight(56);
  final String title;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const VegmoveTitleAppBar({
    super.key,
    required this.title,
    this.showBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      automaticallyImplyLeading: false,
      title: Row(
        children: [
          if (showBackButton)
            IconButton(
              icon: const Icon(
                Icons.arrow_back_ios,
                color: Colors.black,
                size: 20,
              ),
              onPressed: onBackPressed ?? () => context.pop(),
            ),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
              textAlign: showBackButton ? TextAlign.start : TextAlign.center,
            ),
          ),
        ],
      ),
      elevation: 0,
    );
  }
}
