import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// App color palette
class AppColors {
  // Primary colors
  static const Color primary = Color(0xFF01AB55);
  static const Color primaryLight = Color(0xFF87D8AF);
  static const Color primaryDark = Color(0xFF307D6C);

  // Secondary colors
  static const Color secondary = Color(0xFFE3D13A);
  static const Color secondaryLight = Color(0xFFFFF6A4);
  static const Color secondaryDark = Color(0xFF766809);

  // Accent colors
  static const Color accent = Color(0xFFFF3440);
  static const Color accentLight = Color(0xFFFF8A8F);
  static const Color accentDark = Color(0xFF520800);

  // Neutral colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color background = Color(0xFFF8F8F8);
  static const Color lightGrey = Color(0xFFE8E8E8);
  static const Color grey = Color(0xFFC1DACD);
  static const Color darkGrey = Color(0xFF656565);
  static const Color black = Color(0xFF1A1A1A);

  // Status colors
  static const Color success = Color(0xFF01AB55);
  static const Color warning = Color(0xFFE3D13A);
  static const Color error = Color(0xFFFF3440);
  static const Color info = Color(0xFF307D6C);

  // others
  static const Color categoryCardBackground = Color(0xFFE8F1ED);
}

/// Text styles for the app
class AppTextStyles {
  // Heading styles
  static const TextStyle h1 = TextStyle(
    fontFamily: 'RedHatDisplay',
    fontSize: 26,
    fontWeight: FontWeight.w700,
    height: 1.38,
    color: AppColors.black,
  );

  static const TextStyle h2 = TextStyle(
    fontFamily: 'RedHatDisplay',
    fontSize: 22,
    fontWeight: FontWeight.w700,
    height: 1.3,
    color: AppColors.black,
  );

  static const TextStyle h3 = TextStyle(
    fontFamily: 'RedHatDisplay',
    fontSize: 20,
    fontWeight: FontWeight.w700,
    height: 1.25,
    color: AppColors.black,
  );

  static const TextStyle h4 = TextStyle(
    fontFamily: 'RedHatDisplay',
    fontSize: 18,
    fontWeight: FontWeight.w600,
    height: 1.4,
    color: AppColors.black,
  );

  // Body text styles
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: 'RedHatDisplay',
    fontSize: 17,
    fontWeight: FontWeight.w500,
    height: 1.58,
    color: AppColors.black,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: 'RedHatDisplay',
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 1.5,
    color: AppColors.black,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: 'RedHatDisplay',
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.57,
    color: AppColors.black,
  );

  // Button text styles
  static const TextStyle buttonLarge = TextStyle(
    fontFamily: 'RedHatDisplay',
    fontSize: 16,
    fontWeight: FontWeight.w600,
    height: 1.5,
    color: AppColors.white,
  );

  static const TextStyle buttonMedium = TextStyle(
    fontFamily: 'RedHatDisplay',
    fontSize: 14,
    fontWeight: FontWeight.w600,
    height: 1.57,
    color: AppColors.white,
  );

  // Label styles
  static const TextStyle label = TextStyle(
    fontFamily: 'RedHatDisplay',
    fontSize: 14,
    fontWeight: FontWeight.w600,
    height: 1.57,
    color: AppColors.black,
  );

  // Caption styles
  static const TextStyle caption = TextStyle(
    fontFamily: 'RedHatDisplay',
    fontSize: 12,
    fontWeight: FontWeight.w400,
    height: 1.5,
    color: AppColors.darkGrey,
  );
}

/// App theme data
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.background,
      fontFamily: 'RedHatDisplay',

      // AppBar theme
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.white,
        foregroundColor: AppColors.black,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: AppTextStyles.h3,
      ),

      // Button themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          textStyle: AppTextStyles.buttonLarge,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100),
          ),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          elevation: 0,
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primary,
          textStyle: AppTextStyles.buttonLarge,
          side: const BorderSide(color: AppColors.primary, width: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100),
          ),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          textStyle: AppTextStyles.buttonMedium,
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.white,
        contentPadding:
            const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.lightGrey, width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.lightGrey, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primary, width: 1),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.error, width: 1),
        ),
        labelStyle:
            AppTextStyles.bodyMedium.copyWith(color: AppColors.darkGrey),
        hintStyle: AppTextStyles.bodyMedium
            .copyWith(color: AppColors.darkGrey.withOpacity(0.5)),
      ),

      // Card theme
      cardTheme: CardThemeData(
        color: AppColors.white,
        elevation: 2,
        shadowColor: AppColors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        margin: const EdgeInsets.all(8),
      ),

      // Divider theme
      dividerTheme: const DividerThemeData(
        color: AppColors.lightGrey,
        thickness: 1,
        space: 24,
      ),

      // Checkbox theme
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.primary;
          }
          return AppColors.white;
        }),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
        side: const BorderSide(color: AppColors.darkGrey, width: 1.5),
      ),

      // Color scheme
      colorScheme: const ColorScheme.light(
        primary: AppColors.primary,
        secondary: AppColors.secondary,
        error: AppColors.error,
        background: AppColors.background,
        surface: AppColors.white,
      ),
    );
  }
}

/// Theme provider
final themeProvider = Provider<ThemeData>((ref) {
  return AppTheme.lightTheme;
});
