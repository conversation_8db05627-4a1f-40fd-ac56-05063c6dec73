import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:vegmove_ecommerce/model/order.dart';
import 'package:vegmove_ecommerce/providers/order_provider.dart';
import 'package:vegmove_ecommerce/services/order_service.dart';
import 'package:vegmove_ecommerce/ui/screens/orders/components/order_item_card.dart';
import 'package:vegmove_ecommerce/ui/screens/orders/components/order_tracking_map.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';
import 'package:vegmove_ecommerce/providers/language_provider.dart';

// Create a provider family for fetching a specific order by ID
final orderDetailsProvider =
    FutureProvider.family<Order?, int>((ref, orderId) async {
  final orderService = OrderService();
  final languageCode = ref.watch(languageProvider).code;

  try {
    final order =
        await orderService.getOrderById(orderId, languageCode: languageCode);

    // Update the current order in the provider
    ref.read(currentOrderProvider.notifier).state = order;

    return order;
  } catch (e) {
    throw Exception('Failed to load order: $e');
  }
});

class OrderDetailsScreen extends ConsumerWidget {
  final int orderId;

  const OrderDetailsScreen({
    super.key,
    required this.orderId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the order details provider
    final orderAsync = ref.watch(orderDetailsProvider(orderId));

    return orderAsync.when(
      data: (order) {
        if (order == null) {
          return _buildErrorScreen(context, 'Order not found', ref);
        }

        // For SHIPPED status, show the tracking view
        if (order.status == OrderStatus.SHIPPED) {
          return OrderTrackingMap(order: order);
        }

        // For other statuses, show the regular order details view
        return _buildRegularView(context, order, ref);
      },
      loading: () => _buildLoadingScreen(),
      error: (e, stacktrace) {
        log(e.toString());
        log(stacktrace.toString());
        return _buildErrorScreen(context, e.toString(), ref);
      },
    );
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Details'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorScreen(
      BuildContext context, String errorMessage, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Details'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(errorMessage, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                // Refresh the provider
                ref.invalidate(orderDetailsProvider(orderId));
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(100),
                ),
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegularView(BuildContext context, Order order, WidgetRef ref) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        title: const Text('Order Details'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.share_outlined),
            onPressed: () {
              // TODO: Implement share functionality
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh the provider
          ref.invalidate(orderDetailsProvider(orderId));
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order ID
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Order ID: #ORD${order.id}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    _buildStatusBadge(order.status),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Order Items
              ...order.items.map((item) => OrderItemCard(item: item)),

              const SizedBox(height: 16),

              // Order Details
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Order Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildOrderDetailRow(
                        'Item Total & GST', '₹${order.subtotal}'),
                    const SizedBox(height: 12),
                    _buildOrderDetailRow(
                        'Handling Charge', '₹${order.handlingCharge}'),
                    const SizedBox(height: 12),
                    _buildOrderDetailRow(
                        'Delivery Fee',
                        double.parse(order.deliveryFee) > 0
                            ? '₹${order.deliveryFee}'
                            : '₹0 (Free)'),
                    const SizedBox(height: 12),
                    _buildOrderDetailRow(
                        'Coupon Discount', '₹${order.couponDiscount}'),
                    const SizedBox(height: 12),
                    _buildOrderDetailRow(
                        'Reward Point Discount', '₹${order.rewardDiscount}'),
                    const SizedBox(height: 16),
                    const Divider(color: Color(0xFFDFE9E4), height: 1),
                    const SizedBox(height: 16),
                    _buildOrderDetailRow(
                      'To Pay',
                      '₹${order.totalAmount}',
                      isBold: true,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Receiver Details
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Receiver Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Address
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF2F4F3),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.local_shipping,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  order.address.streetName,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                if (order.address.apartment.isNotEmpty)
                                  Text(
                                    'Flat No. ${order.address.apartment}, Block ${order.address.block}',
                                    style: const TextStyle(
                                      color: Colors.black54,
                                      fontSize: 14,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Other details
                    _buildReceiverDetailRow('Order ID', '#ORD${order.id}'),
                    const SizedBox(height: 12),
                    _buildReceiverDetailRow(
                        'Receiver Name', order.user?.name ?? 'Not available'),
                    const SizedBox(height: 12),
                    _buildReceiverDetailRow(
                        'Phone Number', order.user?.phone ?? 'Not available'),
                    const SizedBox(height: 12),
                    _buildReceiverDetailRow(
                        'Placed On',
                        DateFormat('dd MMMM yyyy, h:mm a')
                            .format(order.createdAt)),
                    const SizedBox(height: 12),
                    _buildReceiverDetailRow('Delivery Slot Chosen',
                        '${DateFormat('dd MMMM yyyy').format(order.deliveryDate)}, between\n${order.deliveryStartTime} - ${order.deliveryEndTime}'),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Order Again Button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: () {
                    // Navigate back to home screen
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(100),
                    ),
                  ),
                  child: const Text(
                    'Order Again',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  // This method is no longer needed as we're directly returning OrderTrackingMap in the build method

  Widget _buildStatusBadge(OrderStatus status) {
    Color color;
    String text;

    switch (status) {
      case OrderStatus.PENDING:
        color = Colors.orange;
        text = 'Pending';
        break;
      case OrderStatus.CONFIRMED:
        color = Colors.blue;
        text = 'Confirmed';
        break;
      case OrderStatus.READY:
        color = Colors.purple;
        text = 'Ready';
        break;
      case OrderStatus.SHIPPED:
        color = Colors.green;
        text = 'On the Way';
        break;
      case OrderStatus.DELIVERED:
        color = AppColors.primary;
        text = 'Delivered';
        break;
      case OrderStatus.CANCELLED:
        color = Colors.red;
        text = 'Cancelled';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(100),
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildOrderDetailRow(String label, String value,
      {bool isBold = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isBold ? 16 : 14,
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isBold ? 18 : 14,
            fontWeight: isBold ? FontWeight.bold : FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildReceiverDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }
}
