import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:vegmove_ecommerce/model/delivery_driver_location.dart';
import 'package:vegmove_ecommerce/model/order.dart';
import 'package:vegmove_ecommerce/services/delivery_driver_service.dart';
import 'package:vegmove_ecommerce/ui/screens/orders/components/order_status_tracker.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class OrderTrackingMap extends StatefulWidget {
  final Order order;

  const OrderTrackingMap({
    super.key,
    required this.order,
  });

  @override
  State<OrderTrackingMap> createState() => _OrderTrackingMapState();
}

class _OrderTrackingMapState extends State<OrderTrackingMap> {
  final Completer<GoogleMapController> _controller = Completer();
  bool _isBottomSheetExpanded = false;
  late Timer _timer;
  int _remainingSeconds = 0;

  // Delivery driver location
  DeliveryDriverLocation? _driverLocation;
  final DeliveryDriverService _driverService = DeliveryDriverService();

  // For route drawing
  Map<PolylineId, Polyline> polylines = {};
  List<LatLng> polylineCoordinates = [];
  PolylinePoints polylinePoints = PolylinePoints();

  // Location refresh timer
  Timer? _locationTimer;

  @override
  void initState() {
    super.initState();
    _calculateRemainingTime();
    _startTimer();

    // Fetch driver location if available
    if (widget.order.deliveryDriverId != null) {
      _fetchDriverLocation();

      // Set up periodic location updates
      _locationTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
        _fetchDriverLocation();
      });
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    _locationTimer?.cancel();
    super.dispose();
  }

  Future<void> _fetchDriverLocation() async {
    if (widget.order.deliveryDriverId == null) return;

    try {
      final location = await _driverService
          .getDriverLastLocation(widget.order.deliveryDriverId!);

      if (location != null) {
        setState(() {
          _driverLocation = location;
        });

        // Get delivery location
        final deliveryLat = double.tryParse(widget.order.address.lat) ?? 0.0;
        final deliveryLng = double.tryParse(widget.order.address.long) ?? 0.0;

        // Draw route from driver to delivery location
        _getPolyline(
          LatLng(double.parse(location.lat), double.parse(location.long)),
          LatLng(deliveryLat, deliveryLng),
        );

        // Update camera position
        _updateCameraPosition();
      }
    } catch (e) {
      debugPrint('Error fetching driver location: $e');
    }
  }

  Future<void> _updateCameraPosition() async {
    if (!_controller.isCompleted) return;

    final controller = await _controller.future;

    // Get delivery location
    final deliveryLat = double.tryParse(widget.order.address.lat) ?? 0.0;
    final deliveryLng = double.tryParse(widget.order.address.long) ?? 0.0;

    // Get driver location or warehouse location as fallback
    final driverLat = _driverLocation != null
        ? double.parse(_driverLocation!.lat)
        : (widget.order.warehouse?.lat != null
            ? double.parse(widget.order.warehouse!.lat)
            : deliveryLat - 0.005);

    final driverLng = _driverLocation != null
        ? double.parse(_driverLocation!.long)
        : (widget.order.warehouse?.long != null
            ? double.parse(widget.order.warehouse!.long)
            : deliveryLng - 0.003);

    // Calculate bounds to include both points
    final bounds = LatLngBounds(
      southwest: LatLng(
        driverLat < deliveryLat ? driverLat : deliveryLat,
        driverLng < deliveryLng ? driverLng : deliveryLng,
      ),
      northeast: LatLng(
        driverLat > deliveryLat ? driverLat : deliveryLat,
        driverLng > deliveryLng ? driverLng : deliveryLng,
      ),
    );

    // Add some padding
    controller.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
  }

  Future<void> _getPolyline(LatLng origin, LatLng destination) async {
    try {
      // For simplicity, we'll create a straight line between the two points
      // In a production app, you would use the Google Directions API
      polylineCoordinates = [origin, destination];

      setState(() {
        // Create a polyline with the coordinates
        Polyline polyline = Polyline(
          polylineId: const PolylineId('route'),
          color: AppColors.primary,
          points: polylineCoordinates,
          width: 4,
        );

        // Add the polyline to the map
        polylines[const PolylineId('route')] = polyline;
      });
    } catch (e) {
      debugPrint('Error getting polyline: $e');
    }
  }

  void _calculateRemainingTime() {
    final now = DateTime.now();

    // Parse the delivery end time (format: "HH:mm")
    final endTimeParts = widget.order.deliveryEndTime.split(':');
    if (endTimeParts.length != 2) {
      // Fallback if format is incorrect
      _remainingSeconds = 0;
      return;
    }

    final endHour = int.tryParse(endTimeParts[0]);
    final endMinute = int.tryParse(endTimeParts[1]);

    if (endHour == null || endMinute == null) {
      // Fallback if parsing fails
      _remainingSeconds = 0;
      return;
    }

    // Create a DateTime for the delivery deadline
    final deliveryDeadline = DateTime(
      widget.order.deliveryDate.year,
      widget.order.deliveryDate.month,
      widget.order.deliveryDate.day,
      endHour,
      endMinute,
    );

    // Calculate remaining time
    _remainingSeconds = deliveryDeadline.difference(now).inSeconds;
    if (_remainingSeconds < 0) _remainingSeconds = 0;
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          _timer.cancel();
        }
      });
    });
  }

  String _formatRemainingTime() {
    if (_remainingSeconds <= 0) {
      return 'Arriving now';
    }

    final hours = _remainingSeconds ~/ 3600;
    final minutes = (_remainingSeconds % 3600) ~/ 60;

    if (hours > 0) {
      return '$hours hr ${minutes.toString().padLeft(2, '0')} min';
    } else {
      return '${minutes.toString().padLeft(2, '0')} min';
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get delivery address coordinates
    final deliveryLat = double.tryParse(widget.order.address.lat) ?? 22.5726;
    final deliveryLng = double.tryParse(widget.order.address.long) ?? 88.3639;

    // Get driver location or use warehouse location as fallback
    final driverLat = _driverLocation != null
        ? double.parse(_driverLocation!.lat)
        : (widget.order.warehouse?.lat != null
            ? double.parse(widget.order.warehouse!.lat)
            : deliveryLat - 0.005);

    final driverLng = _driverLocation != null
        ? double.parse(_driverLocation!.long)
        : (widget.order.warehouse?.long != null
            ? double.parse(widget.order.warehouse!.long)
            : deliveryLng - 0.003);

    final deliveryLocation = LatLng(deliveryLat, deliveryLng);
    final driverLocation = LatLng(driverLat, driverLng);

    final initialCameraPosition = CameraPosition(
      target: LatLng(
        (deliveryLat + driverLat) / 2,
        (deliveryLng + driverLng) / 2,
      ),
      zoom: 14,
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Tracking'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // Google Map
          GoogleMap(
            mapType: MapType.normal,
            initialCameraPosition: initialCameraPosition,
            onMapCreated: (GoogleMapController controller) {
              _controller.complete(controller);

              // If we already have driver location, draw the route
              if (_driverLocation != null) {
                _getPolyline(
                  driverLocation,
                  deliveryLocation,
                );
              }
            },
            markers: {
              // Delivery location marker
              Marker(
                markerId: const MarkerId('delivery_location'),
                position: deliveryLocation,
                icon: BitmapDescriptor.defaultMarkerWithHue(
                    BitmapDescriptor.hueRed),
                infoWindow: InfoWindow(
                  title: 'Delivery Location',
                  snippet: widget.order.address.streetName,
                ),
              ),
              // Driver location marker
              Marker(
                markerId: const MarkerId('driver_location'),
                position: driverLocation,
                icon: BitmapDescriptor.defaultMarkerWithHue(
                    BitmapDescriptor.hueGreen),
                infoWindow: InfoWindow(
                  title:
                      _driverLocation != null ? 'Delivery Driver' : 'Warehouse',
                  snippet: _driverLocation != null
                      ? widget.order.deliveryDriver?.name ??
                          'On the way to your location'
                      : widget.order.warehouse?.name ?? 'Preparing your order',
                ),
              ),
            },
            polylines: Set<Polyline>.of(polylines.values),
          ),

          // Bottom Sheet
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: GestureDetector(
              onVerticalDragUpdate: (details) {
                if (details.primaryDelta! < -10) {
                  setState(() {
                    _isBottomSheetExpanded = true;
                  });
                } else if (details.primaryDelta! > 10) {
                  setState(() {
                    _isBottomSheetExpanded = false;
                  });
                }
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: _isBottomSheetExpanded
                    ? MediaQuery.of(context).size.height * 0.7
                    : 220,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: SingleChildScrollView(
                  physics: _isBottomSheetExpanded
                      ? const AlwaysScrollableScrollPhysics()
                      : const NeverScrollableScrollPhysics(),
                  child: Column(
                    children: [
                      // Drag handle
                      Container(
                        margin: const EdgeInsets.only(top: 10),
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),

                      // Status Tracker
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: OrderStatusTracker(order: widget.order),
                      ),

                      // Order Summary
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Subtotal: ₹${widget.order.totalAmount}',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  'Estimated arrival',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.amber[100],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    _formatRemainingTime(),
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.amber[800],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        child: Text(
                          'Out for delivery - arriving between ${widget.order.deliveryStartTime} and ${widget.order.deliveryEndTime}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),

                      // Expanded content
                      if (_isBottomSheetExpanded) ...[
                        const Divider(),

                        // Order Details
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Order Details',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              _buildOrderDetailRow('Item Total & GST',
                                  '₹${widget.order.subtotal}'),
                              const SizedBox(height: 12),
                              _buildOrderDetailRow('Handling Charge',
                                  '₹${widget.order.handlingCharge}'),
                              const SizedBox(height: 12),
                              _buildOrderDetailRow(
                                  'Delivery Fee',
                                  double.parse(widget.order.deliveryFee) > 0
                                      ? '₹${widget.order.deliveryFee}'
                                      : '₹0 (Free)'),
                              const SizedBox(height: 12),
                              _buildOrderDetailRow('Coupon Discount',
                                  '₹${widget.order.couponDiscount}'),
                              const SizedBox(height: 12),
                              _buildOrderDetailRow('Reward Point Discount',
                                  '₹${widget.order.rewardDiscount}'),
                              const SizedBox(height: 16),
                              const Divider(
                                  color: Color(0xFFDFE9E4), height: 1),
                              const SizedBox(height: 16),
                              _buildOrderDetailRow(
                                'To Pay',
                                '₹${widget.order.totalAmount}',
                                isBold: true,
                              ),
                            ],
                          ),
                        ),

                        // Receiver Details
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Receiver Details',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Address
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF2F4F3),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 40,
                                      height: 40,
                                      decoration: BoxDecoration(
                                        color: AppColors.primary,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.local_shipping,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            widget.order.address.streetName,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          if (widget.order.address.apartment
                                              .isNotEmpty)
                                            Text(
                                              'Flat No. ${widget.order.address.apartment}, Block ${widget.order.address.block}',
                                              style: const TextStyle(
                                                color: Colors.black54,
                                                fontSize: 14,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Other details
                              _buildReceiverDetailRow(
                                  'Order ID', '#ORD${widget.order.id}'),
                              const SizedBox(height: 12),
                              _buildReceiverDetailRow('Receiver Name',
                                  widget.order.user?.name ?? 'Not available'),
                              const SizedBox(height: 12),
                              _buildReceiverDetailRow('Phone Number',
                                  widget.order.user?.phone ?? 'Not available'),
                              const SizedBox(height: 12),
                              _buildReceiverDetailRow(
                                  'Placed On',
                                  DateFormat('dd MMMM yyyy, h:mm a')
                                      .format(widget.order.createdAt)),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetailRow(String label, String value,
      {bool isBold = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isBold ? 16 : 14,
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isBold ? 18 : 14,
            fontWeight: isBold ? FontWeight.bold : FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildReceiverDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }
}
