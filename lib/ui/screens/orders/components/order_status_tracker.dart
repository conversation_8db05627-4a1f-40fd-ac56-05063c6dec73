import 'package:flutter/material.dart';
import 'package:vegmove_ecommerce/model/order.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class OrderStatusTracker extends StatelessWidget {
  final Order order;
  
  const OrderStatusTracker({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    // Determine the current status index
    int currentIndex = _getStatusIndex(order.status);
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStatusItem(
            icon: Icons.check_circle,
            label: 'Confirmed',
            isActive: currentIndex >= 1,
            isFirst: true,
          ),
          _buildStatusConnector(isActive: currentIndex >= 2),
          _buildStatusItem(
            icon: Icons.inventory_2,
            label: 'Packed',
            isActive: currentIndex >= 2,
          ),
          _buildStatusConnector(isActive: currentIndex >= 3),
          _buildStatusItem(
            icon: Icons.local_shipping,
            label: 'On the Way',
            isActive: currentIndex >= 3,
          ),
          _buildStatusConnector(isActive: currentIndex >= 4),
          _buildStatusItem(
            icon: Icons.check_box,
            label: 'Delivered',
            isActive: currentIndex >= 4,
            isLast: true,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem({
    required IconData icon,
    required String label,
    required bool isActive,
    bool isFirst = false,
    bool isLast = false,
  }) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: isActive ? AppColors.primary : const Color(0xFFE0E0E0),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: isActive ? Colors.white : Colors.grey,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
            color: isActive ? AppColors.primary : Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusConnector({required bool isActive}) {
    return Expanded(
      child: Container(
        height: 2,
        color: isActive ? AppColors.primary : const Color(0xFFE0E0E0),
      ),
    );
  }

  int _getStatusIndex(OrderStatus status) {
    switch (status) {
      case OrderStatus.PENDING:
        return 0;
      case OrderStatus.CONFIRMED:
        return 1;
      case OrderStatus.READY:
        return 2;
      case OrderStatus.SHIPPED:
        return 3;
      case OrderStatus.DELIVERED:
        return 4;
      case OrderStatus.CANCELLED:
        return 0; // For cancelled orders, show as pending
    }
  }
}
