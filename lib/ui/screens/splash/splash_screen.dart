import 'dart:async';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_ecommerce/services/auth_service.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    // Start timer to navigate after 1 second
    Timer(const Duration(seconds: 1), () {
      _checkLoginStatus();
    });
  }

  Future<void> _updateFirebaseToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        debugPrint('Firebase token: $token');
        await _authService.updateFirebaseToken(token);
        debugPrint('Firebase token updated successfully');
      }
    } catch (e) {
      debugPrint('Error updating Firebase token: $e');
    }
  }

  Future<void> _checkLoginStatus() async {
    try {
      // Check if user is logged in using shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');
      final isWithinZone = prefs.getBool('isWithinZone') ?? false;

      if (mounted) {
        if (!isWithinZone) {
          // Still need to check zone before allowing access
          context.goNamed('zone-checker');
        } else {
          // If user is logged in, update Firebase token
          if (token != null && token.isNotEmpty) {
            await _updateFirebaseToken();
          }

          // Navigate to home regardless of login status
          if (mounted) {
            context.goNamed('home');
          }
        }
      }
    } catch (e) {
      debugPrint('Error checking login status: $e');
      // In case of error, default to location picker for now
      if (mounted) {
        context.goNamed('zone-checker');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        // decoration: BoxDecoration(
        //   // Light background with subtle pattern
        //   color: Colors.white,
        //   image: DecorationImage(
        //     image: const AssetImage('assets/images/logo.png'),
        //     fit: BoxFit.none,
        //     opacity: 0.05, // Very faint background pattern
        //     alignment: Alignment.center,
        //     scale: 0.5,
        //   ),
        // ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo image
              Image.asset(
                'assets/images/logo.png',
                width: 180,
                height: 180,
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }
}
