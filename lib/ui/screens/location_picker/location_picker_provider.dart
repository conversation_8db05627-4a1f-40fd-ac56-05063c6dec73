import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:vegmove_ecommerce/services/places_service.dart';
import 'package:vegmove_ecommerce/services/zone_service.dart';
import 'package:vegmove_ecommerce/network/dto/check_zone_response_dto.dart';

/// Provider for the location picker state
final locationPickerProvider =
    StateNotifierProvider<LocationPickerNotifier, LocationPickerState>((ref) {
  return LocationPickerNotifier();
});

/// State for the location picker
class LocationPickerState {
  final bool isLoading;
  final bool isSearching;
  final String? error;
  final LatLng? selectedLocation;
  final String? selectedAddress;
  final String? country;
  final String? state;
  final String? city;
  final String? street;
  final String? postalCode;
  final List<PlaceSearchResult> searchResults;
  final bool isWithinZone;
  final bool isCheckingZone;

  LocationPickerState({
    this.isLoading = false,
    this.isSearching = false,
    this.error,
    this.selectedLocation,
    this.selectedAddress,
    this.country,
    this.state,
    this.city,
    this.street,
    this.postalCode,
    this.searchResults = const [],
    this.isWithinZone = false,
    this.isCheckingZone = false,
  });

  LocationPickerState copyWith({
    bool? isLoading,
    bool? isSearching,
    String? error,
    LatLng? selectedLocation,
    String? selectedAddress,
    String? country,
    String? state,
    String? city,
    String? street,
    String? postalCode,
    List<PlaceSearchResult>? searchResults,
    bool? isWithinZone,
    bool? isCheckingZone,
  }) {
    return LocationPickerState(
      isLoading: isLoading ?? this.isLoading,
      isSearching: isSearching ?? this.isSearching,
      error: error,
      selectedLocation: selectedLocation ?? this.selectedLocation,
      selectedAddress: selectedAddress ?? this.selectedAddress,
      country: country ?? this.country,
      state: state ?? this.state,
      city: city ?? this.city,
      street: street ?? this.street,
      postalCode: postalCode ?? this.postalCode,
      searchResults: searchResults ?? this.searchResults,
      isWithinZone: isWithinZone ?? this.isWithinZone,
      isCheckingZone: isCheckingZone ?? this.isCheckingZone,
    );
  }
}

/// Model for place search results
class PlaceSearchResult {
  final String name;
  final String address;
  final LatLng location;

  PlaceSearchResult({
    required this.name,
    required this.address,
    required this.location,
  });
}

/// Notifier for the location picker
class LocationPickerNotifier extends StateNotifier<LocationPickerState> {
  final ZoneService _zoneService = ZoneService();
  final PlacesService _placesService = PlacesService();

  LocationPickerNotifier() : super(LocationPickerState());

  /// Set the selected location and check if it's within a zone
  Future<void> setSelectedLocation(LatLng location) async {
    state = state.copyWith(
      selectedLocation: location,
      isCheckingZone: true,
    );

    // Get address for the selected location
    await getAddressFromLocation(location);

    // Check if the location is within a zone
    await checkZone(location);
  }

  /// Get the address from a location
  Future<void> getAddressFromLocation(LatLng location) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String address = [
          place.street,
          place.subLocality,
          place.locality,
          place.postalCode,
          place.country,
        ].where((element) => element != null && element.isNotEmpty).join(', ');

        state = state.copyWith(
          selectedAddress: address,
          country: place.country,
          state: place.administrativeArea,
          city: place.locality,
          street: place.street,
          postalCode: place.postalCode,
        );
      }
    } catch (e) {
      debugPrint('Error getting address: $e');
      state = state.copyWith(
        error: 'Error getting address',
        selectedAddress: 'Unknown location',
      );
    }
  }

  /// Check if the location is within a zone
  Future<void> checkZone(LatLng location) async {
    try {
      CheckZoneResponseDto response = await _zoneService.checkZone(
        lat: location.latitude,
        long: location.longitude,
      );

      state = state.copyWith(
        isWithinZone: response.withinZone,
        isCheckingZone: false,
      );
    } catch (e) {
      debugPrint('Error checking zone: $e');

      // For demo purposes, we'll simulate zone checking
      // In a real app, you would handle the error properly
      // This is just to demonstrate the UI

      // Simulate that some locations are within zone and some are not
      // For example, locations with latitude > 0 are within zone
      final bool simulatedWithinZone = location.latitude > 0;

      state = state.copyWith(
        error: 'Error checking zone',
        isCheckingZone: false,
        isWithinZone: simulatedWithinZone,
      );
    }
  }

  /// Search for places by query using Google Places API
  Future<void> searchPlaces(String query) async {
    if (query.isEmpty) {
      state = state.copyWith(searchResults: []);
      return;
    }

    state = state.copyWith(isSearching: true);

    try {
      // Get place predictions from Google Places Autocomplete API
      final predictions = await _placesService.getPlaceAutocomplete(query);

      if (predictions.isNotEmpty) {
        List<PlaceSearchResult> results = [];

        // We'll limit to 5 results to avoid too many API calls
        final limitedPredictions = predictions.take(7).toList();

        for (var prediction in limitedPredictions) {
          // Get place details for each prediction
          final details =
              await _placesService.getPlaceDetails(prediction.placeId);

          if (details != null) {
            results.add(PlaceSearchResult(
              name: prediction.mainText,
              address: details.formattedAddress,
              location: details.location,
            ));
          }
        }

        state = state.copyWith(
          searchResults: results,
          isSearching: false,
        );
      } else {
        state = state.copyWith(
          searchResults: [],
          isSearching: false,
        );
      }
    } catch (e) {
      debugPrint('Error searching places: $e');
      state = state.copyWith(
        error: 'Error searching places',
        isSearching: false,
        searchResults: [],
      );
    }
  }

  /// Clear search results
  void clearSearchResults() {
    state = state.copyWith(searchResults: []);
  }

  /// Select a place from search results
  Future<void> selectPlace(PlaceSearchResult place) async {
    state = state.copyWith(
      selectedLocation: place.location,
      selectedAddress: place.address,
      searchResults: [],
      isCheckingZone: true,
    );

    // Get detailed address information
    await getAddressFromLocation(place.location);

    // Check if the location is within a zone
    await checkZone(place.location);
  }
}
