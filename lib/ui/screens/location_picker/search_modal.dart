import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/ui/screens/location_picker/location_picker_provider.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class SearchLocationModal extends ConsumerStatefulWidget {
  final TextEditingController controller;
  final Function(PlaceSearchResult) onPlaceSelected;

  const SearchLocationModal({
    super.key,
    required this.controller,
    required this.onPlaceSelected,
  });

  @override
  ConsumerState<SearchLocationModal> createState() =>
      _SearchLocationModalState();
}

class _SearchLocationModalState extends ConsumerState<SearchLocationModal> {
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Request focus when the modal is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(locationPickerProvider);

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Search field
          Container(
            height: 56,
            decoration: BoxDecoration(
              color: Colors.grey.shade200,
              borderRadius: BorderRadius.circular(28),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              children: [
                const SizedBox(width: 8),
                const Icon(Icons.search, color: Colors.grey, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: widget.controller,
                    focusNode: _searchFocusNode,
                    decoration: InputDecoration(
                      fillColor: Colors.transparent,
                      hintText: 'Search for your address...',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(vertical: 14),
                      focusColor: Colors.transparent,
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide.none,
                        borderRadius: BorderRadius.circular(0.0),
                      ),
                    ),
                    onChanged: (value) {
                      ref
                          .read(locationPickerProvider.notifier)
                          .searchPlaces(value);
                    },
                  ),
                ),
                if (widget.controller.text.isNotEmpty)
                  IconButton(
                    icon: const Icon(Icons.clear, color: Colors.grey),
                    onPressed: () {
                      widget.controller.clear();
                      ref
                          .read(locationPickerProvider.notifier)
                          .clearSearchResults();
                    },
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Search results
          Expanded(
            child: state.isSearching
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : state.searchResults.isEmpty
                    ? const Center(
                        child: Text(
                          'Search for a location',
                          style: TextStyle(color: Colors.grey),
                        ),
                      )
                    : ListView.separated(
                        padding: EdgeInsets.zero,
                        itemCount: state.searchResults.length,
                        separatorBuilder: (context, index) =>
                            const Divider(height: 1),
                        itemBuilder: (context, index) {
                          final place = state.searchResults[index];
                          return ListTile(
                            leading: const Icon(
                              Icons.location_on,
                              color: AppColors.primary,
                            ),
                            title: Text(
                              place.name,
                              style: AppTextStyles.bodyMedium.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            subtitle: Text(
                              place.address,
                              style: AppTextStyles.bodySmall,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            onTap: () {
                              // Hide keyboard
                              FocusScope.of(context).unfocus();

                              // Call the callback
                              widget.onPlaceSelected(place);
                            },
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }
}
