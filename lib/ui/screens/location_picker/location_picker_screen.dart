import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_ecommerce/providers/location_provider.dart';
import 'package:vegmove_ecommerce/ui/screens/location_picker/location_picker_provider.dart';
import 'package:vegmove_ecommerce/ui/screens/location_picker/search_modal.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';
import 'package:geolocator/geolocator.dart';

class LocationPickerResult {
  final bool isWithinZone;
  final String address;
  final LatLng location;
  final String? country;
  final String? state;
  final String? city;
  final String? street;
  final String? postalCode;

  LocationPickerResult({
    required this.isWithinZone,
    required this.address,
    required this.location,
    this.country,
    this.state,
    this.city,
    this.street,
    this.postalCode,
  });
}

class LocationPickerScreen extends ConsumerStatefulWidget {
  final Function(LocationPickerResult result)? onLocationSelected;

  const LocationPickerScreen({super.key, this.onLocationSelected});

  @override
  ConsumerState<LocationPickerScreen> createState() =>
      _LocationPickerScreenState();
}

class _LocationPickerScreenState extends ConsumerState<LocationPickerScreen> {
  final Completer<GoogleMapController> _controller =
      Completer<GoogleMapController>();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  // Default camera position (can be updated with user's location)
  static const CameraPosition _defaultPosition = CameraPosition(
    target: LatLng(22.5726, 88.3639), // Kolkata
    zoom: 14.0,
  );

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();

    // Listen for focus changes on the search field
    _searchFocusNode.addListener(() {
      if (!_searchFocusNode.hasFocus) {
        // Clear search results when focus is lost
        ref.read(locationPickerProvider.notifier).clearSearchResults();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  // Get the user's current location
  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return;
      }

      Position position = await Geolocator.getCurrentPosition();

      final GoogleMapController controller = await _controller.future;
      controller.animateCamera(CameraUpdate.newLatLng(
        LatLng(position.latitude, position.longitude),
      ));

      // Set the selected location
      ref.read(locationPickerProvider.notifier).setSelectedLocation(
            LatLng(position.latitude, position.longitude),
          );
    } catch (e) {
      debugPrint('Error getting current location: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(locationPickerProvider);

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: true,
      ),
      body: Stack(
        children: [
          // Google Map
          GoogleMap(
            mapType: MapType.normal,
            initialCameraPosition: _defaultPosition,
            markers: {}, // No markers as we'll use a fixed center marker
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: true, // Enable zoom controls
            onMapCreated: (GoogleMapController controller) {
              _controller.complete(controller);
            },
            onCameraIdle: () {
              // When camera stops moving, get the center position
              _controller.future.then((controller) async {
                final visibleRegion = await controller.getVisibleRegion();
                final center = LatLng(
                  (visibleRegion.northeast.latitude +
                          visibleRegion.southwest.latitude) /
                      2,
                  (visibleRegion.northeast.longitude +
                          visibleRegion.southwest.longitude) /
                      2,
                );
                // Update the selected location based on the center of the map
                ref
                    .read(locationPickerProvider.notifier)
                    .setSelectedLocation(center);
              });
            },
            // Disable tap to select location
            onTap: (_) {},
          ),

          // Fixed center marker
          Center(
            child: Padding(
              padding: const EdgeInsets.only(
                  bottom:
                      24), // Offset to account for the marker's anchor point
              child: SvgPicture.asset(
                'assets/images/map_pin.svg',
                height: 40,
              ),
            ),
          ),

          // Search bar
          Positioned(
            top: 80,
            left: 16,
            right: 16,
            child: GestureDetector(
              onTap: () {
                // Show the search modal
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.white,
                  shape: const RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  builder: (context) => DraggableScrollableSheet(
                    initialChildSize: 0.9,
                    minChildSize: 0.5,
                    maxChildSize: 0.9,
                    expand: false,
                    builder: (context, scrollController) => SearchLocationModal(
                      controller: _searchController,
                      onPlaceSelected: (place) {
                        // Select the place
                        ref
                            .read(locationPickerProvider.notifier)
                            .selectPlace(place);

                        // Move camera to the selected location
                        _controller.future.then((controller) {
                          controller.animateCamera(
                            CameraUpdate.newLatLng(place.location),
                          );
                        });

                        // Close the modal
                        Navigator.pop(context);
                      },
                    ),
                  ),
                );
              },
              child: Container(
                height: 56,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(28),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Icon(Icons.search, color: Colors.grey, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      'Search for your address...',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Bottom panel with location info and confirm button
          if (state.selectedLocation != null)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),

                    // Location info
                    Container(
                      padding: const EdgeInsets.all(16),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: state.isWithinZone
                                  ? AppColors.primary
                                  : Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              state.isWithinZone
                                  ? Icons.location_on
                                  : Icons.info,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: state.isWithinZone
                                ? Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        state.selectedAddress
                                                ?.split(',')
                                                .first ??
                                            'Selected Location',
                                        style:
                                            AppTextStyles.bodyMedium.copyWith(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      Text(
                                        state.selectedAddress
                                                ?.substring(
                                                  state.selectedAddress!
                                                          .indexOf(',') +
                                                      1,
                                                )
                                                .trim() ??
                                            '',
                                        style: const TextStyle(
                                          color: Colors.black87,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  )
                                : Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Vegmove is currently unavailable in your location',
                                        style:
                                            AppTextStyles.bodyMedium.copyWith(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                          ),
                        ],
                      ),
                    ),

                    // Confirm button
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: state.isWithinZone
                            ? () async {
                                // Save the selected location to SharedPreferences
                                final prefs =
                                    await SharedPreferences.getInstance();
                                await prefs.setDouble('selected_location_lat',
                                    state.selectedLocation!.latitude);
                                await prefs.setDouble('selected_location_lng',
                                    state.selectedLocation!.longitude);
                                await prefs.setString(
                                    'selected_location_address',
                                    state.selectedAddress ?? '');

                                // Update the location provider with the new location
                                ref.read(locationProvider.notifier).setLocation(
                                      state.selectedLocation!.latitude,
                                      state.selectedLocation!.longitude,
                                    );

                                // Return the selected location with detailed address info
                                if (widget.onLocationSelected != null) {
                                  widget
                                      .onLocationSelected!(LocationPickerResult(
                                    isWithinZone: true,
                                    location: state.selectedLocation!,
                                    address: state.selectedAddress!,
                                    country: state.country,
                                    state: state.state,
                                    city: state.city,
                                    street: state.street,
                                    postalCode: state.postalCode,
                                  ));
                                  if (mounted) {
                                    context.pop();
                                  }
                                } else {
                                  if (mounted) {
                                    context.pop(LocationPickerResult(
                                      isWithinZone: true,
                                      location: state.selectedLocation!,
                                      address: state.selectedAddress!,
                                      country: state.country,
                                      state: state.state,
                                      city: state.city,
                                      street: state.street,
                                      postalCode: state.postalCode,
                                    ));
                                  }
                                }
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          disabledBackgroundColor: Colors.grey,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(28),
                          ),
                        ),
                        child: const Text(
                          'Confirm & Continue',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Loading indicator
          if (state.isCheckingZone)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(bottom: 120),
        child: FloatingActionButton(
          onPressed: _getCurrentLocation,
          backgroundColor: AppColors.primary,
          child: const Icon(Icons.my_location),
        ),
      ),
    );
  }
}
