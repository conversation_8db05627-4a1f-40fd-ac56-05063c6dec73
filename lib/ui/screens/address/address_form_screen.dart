import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/address.dart';
import 'package:vegmove_ecommerce/model/country.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/providers/address_provider.dart';
import 'package:vegmove_ecommerce/services/country_service.dart';
import 'package:vegmove_ecommerce/ui/screens/location_picker/location_picker_screen.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

// Provider for countries
final countriesProvider =
    FutureProvider<List<Country>>((ref) => CountryService().getCountries());

// Provider for the selected country
final selectedCountryProvider = StateProvider<Country?>((ref) => null);

// Provider for the selected address type
final selectedAddressTypeProvider =
    StateProvider<AddressType>((ref) => AddressType.SHIPPING);

class AddressFormScreen extends ConsumerStatefulWidget {
  final int? addressId;

  const AddressFormScreen({super.key, this.addressId});

  @override
  ConsumerState<AddressFormScreen> createState() => _AddressFormScreenState();
}

class _AddressFormScreenState extends ConsumerState<AddressFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _apartmentController = TextEditingController();
  final _blockController = TextEditingController();
  final _streetNameController = TextEditingController();

  bool _isLoading = false;
  bool _isEditMode = false;
  Address? _addressToEdit;
  LocationPickerResult? locationPickerResult;

  @override
  void initState() {
    super.initState();
    _isEditMode = widget.addressId != null;

    if (_isEditMode) {
      // Load the address to edit
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadAddressToEdit();
      });
    }
  }

  Future<void> _loadAddressToEdit() async {
    if (widget.addressId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Find the address in the provider state
      final addresses = ref.read(addressProvider).addresses;
      _addressToEdit = addresses.firstWhere(
        (address) => address.id == widget.addressId,
      );

      // Populate the form fields
      ref.read(selectedAddressTypeProvider.notifier).state =
          _addressToEdit!.type;
      _apartmentController.text = _addressToEdit!.apartment;
      _blockController.text = _addressToEdit!.block;
      _streetNameController.text = _addressToEdit!.streetName;

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading address: $e')),
      );
    }
  }

  @override
  void dispose() {
    _apartmentController.dispose();
    _blockController.dispose();
    _streetNameController.dispose();
    super.dispose();
  }

  Future<void> _pickLocation() async {
    locationPickerResult = await context.pushNamed<LocationPickerResult>(
      'location-picker',
    );

    if (locationPickerResult != null) {
      setState(() {
        _streetNameController.text = locationPickerResult!.address;
      });
    }
  }

  Future<void> _saveAddress() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        if (_isEditMode && _addressToEdit != null) {
          // Create an updated address object
          final updatedAddress = _addressToEdit!.copyWith(
            type: ref.read(selectedAddressTypeProvider),
            streetName: _streetNameController.text,
            apartment: _apartmentController.text,
            block: _blockController.text,
            lat: locationPickerResult?.location.latitude.toString(),
            long: locationPickerResult?.location.longitude.toString(),
          );

          // Update the address
          final success = await ref
              .read(addressProvider.notifier)
              .updateAddress(updatedAddress);

          if (success) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Address updated successfully')),
              );
            }
          } else {
            throw Exception('Failed to update address');
          }
        } else {
          // Create a new address

          if (locationPickerResult == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                  content: Text('Please select your location first.')),
            );
            return;
          }
          final address =
              await ref.read(addressProvider.notifier).createAddress(
                    type: ref.read(selectedAddressTypeProvider),
                    streetName: _streetNameController.text,
                    city: locationPickerResult!.city ?? '',
                    state: locationPickerResult!.state ?? '',
                    countryId: 1,
                    zipCode: locationPickerResult!.postalCode ?? '',
                    lat: locationPickerResult!.location.latitude.toString(),
                    long: locationPickerResult!.location.longitude.toString(),
                    apartment: _apartmentController.text,
                    block: _blockController.text,
                  );

          if (address != null) {
            // Set as selected address if it's the first one
            final addresses = ref.read(addressProvider).addresses;
            if (addresses.length == 1) {
              ref.read(selectedAddressProvider.notifier).selectAddress(address);
            }

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Address added successfully')),
              );
            }
          }
        }

        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          context.pop();
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving address: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final streetAddress =
        locationPickerResult?.address ?? _addressToEdit?.streetName;
    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: Text(
          _isEditMode ? 'Edit Address' : 'Add New Address',
          style: const TextStyle(color: Colors.black),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => context.pop(),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Location picker button
                    GestureDetector(
                      onTap: _pickLocation,
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: _addressToEdit == null &&
                                    locationPickerResult == null
                                ? Colors.redAccent.withOpacity(0.5)
                                : const Color(0xFFE3E9E6),
                            width: _addressToEdit == null &&
                                    locationPickerResult == null
                                ? 2
                                : 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFE8F1ED),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: const Icon(
                                    Icons.location_on,
                                    color: AppColors.primary,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                const Expanded(
                                  child: Text(
                                    'Select Location',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                const Icon(
                                  Icons.arrow_forward_ios,
                                  size: 16,
                                  color: Colors.grey,
                                ),
                              ],
                            ),
                            if (streetAddress != null) ...[
                              const SizedBox(height: 16),
                              Text(
                                streetAddress,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                            ] else ...[
                              const SizedBox(height: 16),
                              Text(
                                'Please select a location to continue',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.redAccent.withOpacity(0.8),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text(
                      'Address Details',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Address type
                    _buildDropdown<AddressType>(
                      label: 'Address Type',
                      value: ref.watch(selectedAddressTypeProvider),
                      items: AddressType.values.map((type) {
                        return DropdownMenuItem<AddressType>(
                          value: type,
                          child: Text(
                            type == AddressType.BILLING
                                ? 'Billing'
                                : 'Shipping',
                            style: const TextStyle(fontSize: 16),
                          ),
                        );
                      }).toList(),
                      onChanged: (AddressType? value) {
                        if (value != null) {
                          ref.read(selectedAddressTypeProvider.notifier).state =
                              value;
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    // Apartment
                    _buildTextField(
                      controller: _apartmentController,
                      label: 'Flat/Apartment No.',
                      hint: 'e.g., 2B',
                    ),
                    const SizedBox(height: 16),
                    // Block
                    _buildTextField(
                      controller: _blockController,
                      label: 'Block',
                      hint: 'e.g., C',
                    ),
                    const SizedBox(height: 16),
                    // Street name
                    _buildTextField(
                      controller: _streetNameController,
                      label: 'Street Name',
                      hint: 'e.g., Ballygunge Circular Road',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter street name';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 32),
                    // Save button
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: streetAddress != null ? _saveAddress : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          disabledBackgroundColor: Colors.grey,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(28),
                          ),
                        ),
                        child: Text(
                          _isEditMode ? 'Update Address' : 'Save Address',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFFE3E9E6)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFFE3E9E6)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
          ),
          validator: validator,
        ),
      ],
    );
  }

  Widget _buildDropdown<T>({
    required String label,
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFFE3E9E6)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<T>(
              value: value,
              isExpanded: true,
              hint: const Text('Select a country'),
              items: items,
              onChanged: onChanged,
            ),
          ),
        ),
      ],
    );
  }
}
