import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/address.dart';
import 'package:vegmove_ecommerce/providers/address_provider.dart';
import 'package:vegmove_ecommerce/ui/components/slidable_address_card.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class SavedAddressScreen extends ConsumerWidget {
  const SavedAddressScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final addressState = ref.watch(addressProvider);
    final selectedAddress = ref.watch(selectedAddressProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: const Text(
          'Saved Address',
          style: TextStyle(color: Colors.black),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => context.pop(),
        ),
      ),
      body: addressState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : addressState.error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error loading addresses',
                        style: TextStyle(color: Colors.red),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          ref.read(addressProvider.notifier).loadAddresses();
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : addressState.addresses.isEmpty
                  ? _buildEmptyState(context)
                  : _buildAddressList(
                      context, addressState.addresses, selectedAddress, ref),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.pushNamed('add-address');
        },
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.location_off,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'No saved addresses',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Add a new address to get started',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              context.pushNamed('add-address');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(100),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
            child: const Text(
              'Add New Address',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressList(
    BuildContext context,
    List<Address> addresses,
    Address? selectedAddress,
    WidgetRef ref,
  ) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: addresses.length,
              itemBuilder: (context, index) {
                final address = addresses[index];
                final isSelected = selectedAddress?.id == address.id;

                return SlidableAddressCard(
                  address: address,
                  isSelected: isSelected,
                  onTap: () {
                    ref
                        .read(selectedAddressProvider.notifier)
                        .selectAddress(address);
                  },
                  onEdit: () {
                    context.pushNamed(
                      'edit-address',
                      pathParameters: {'addressId': address.id.toString()},
                    );
                  },
                  onDelete: () {
                    _showDeleteConfirmation(context, address, ref);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    Address address,
    WidgetRef ref,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Address'),
        content: const Text(
          'Are you sure you want to delete this address? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(addressProvider.notifier).deleteAddress(address.id);

              // If the deleted address was selected, clear the selection
              final selectedAddress = ref.read(selectedAddressProvider);
              if (selectedAddress?.id == address.id) {
                // If there are other addresses, select the first one
                final addresses = ref.read(addressProvider).addresses;
                if (addresses.isNotEmpty && addresses.length > 1) {
                  final newSelectedAddress = addresses.firstWhere(
                    (a) => a.id != address.id,
                    orElse: () => addresses.first,
                  );
                  ref
                      .read(selectedAddressProvider.notifier)
                      .selectAddress(newSelectedAddress);
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
