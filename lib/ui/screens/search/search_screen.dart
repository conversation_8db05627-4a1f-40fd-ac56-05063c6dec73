import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/providers/search_provider.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class SearchScreen extends ConsumerStatefulWidget {
  const SearchScreen({super.key});

  @override
  ConsumerState<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends ConsumerState<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Set focus to search field when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      ref.read(searchProvider.notifier).loadMore();
    }
  }

  void _onSearch(String query) {
    // Only search if query is not empty after trimming
    final trimmedQuery = query.trim();
    if (trimmedQuery.isNotEmpty) {
      ref.read(searchProvider.notifier).search(trimmedQuery);
    } else {
      // If query is empty after trimming, clear the search
      _clearSearch();
    }
  }

  void _clearSearch() {
    _searchController.clear();
    ref.read(searchProvider.notifier).clear();
  }

  @override
  Widget build(BuildContext context) {
    final searchState = ref.watch(searchProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Search bar
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(100),
                child: Container(
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(100),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    focusNode: _searchFocusNode,
                    textInputAction: TextInputAction.search,
                    decoration: InputDecoration(
                      hintText: 'Bucket',
                      hintStyle: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 16,
                      ),
                      prefixIcon: Padding(
                        padding: const EdgeInsets.only(left: 16.0, right: 8.0),
                        child: Icon(
                          Icons.search,
                          color: Colors.grey.shade600,
                          size: 24,
                        ),
                      ),
                      prefixIconConstraints: const BoxConstraints(
                        minWidth: 40,
                        minHeight: 40,
                      ),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? Padding(
                              padding: const EdgeInsets.only(right: 16.0),
                              child: GestureDetector(
                                onTap: _clearSearch,
                                child: Icon(
                                  Icons.close,
                                  color: Colors.grey.shade600,
                                  size: 24,
                                ),
                              ),
                            )
                          : null,
                      suffixIconConstraints: const BoxConstraints(
                        minWidth: 40,
                        minHeight: 40,
                      ),
                      border: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      errorBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {}); // Update UI to show/hide clear button

                      // Debounce search to avoid too many API calls
                      if (_debounce?.isActive ?? false) _debounce?.cancel();
                      _debounce = Timer(const Duration(milliseconds: 500), () {
                        final trimmedValue = value.trim();
                        if (trimmedValue.isNotEmpty) {
                          _onSearch(trimmedValue);
                        } else {
                          ref.read(searchProvider.notifier).clear();
                        }
                      });
                    },
                    onSubmitted: _onSearch,
                  ),
                ),
              ),
            ),

            // Search results
            Expanded(
              child: _buildSearchResults(searchState),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults(SearchState state) {
    if (state.query.isEmpty) {
      return const Center(
        child: Text(
          'Search for products',
          style: AppTextStyles.bodyLarge,
        ),
      );
    }

    if (state.error != null) {
      return Center(
        child: Text(
          'Error: ${state.error}',
          style: AppTextStyles.bodyLarge,
        ),
      );
    }

    if (state.products.isEmpty && !state.isLoading) {
      return const Center(
        child: Text(
          'No products found',
          style: AppTextStyles.bodyLarge,
        ),
      );
    }

    return ListView.separated(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: state.products.length + (state.isLoading ? 1 : 0),
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        thickness: 1,
        color: Color(0xFFE3E9E6), // Light grey divider
      ),
      itemBuilder: (context, index) {
        if (index >= state.products.length) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
          );
        }

        final product = state.products[index];
        return _buildSearchResultItem(product);
      },
    );
  }

  Widget _buildSearchResultItem(product) {
    return GestureDetector(
      onTap: () {
        context.goNamed(
          'product-details',
          pathParameters: {
            'productId': product.productId.toString(),
            'slug': product.slug!
          },
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Product image in a circular container
            Container(
              width: 48,
              height: 48,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Color(0xFFE8F1ED), // Green background
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(
                    24), // Half of width/height for circle
                child: product.thumbnailUrl != null
                    ? Image.network(
                        product.thumbnailUrl!,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) =>
                            const Icon(
                          Icons.image,
                          size: 24,
                          color: Colors.grey,
                        ),
                      )
                    : const Icon(
                        Icons.image,
                        size: 24,
                        color: Colors.grey,
                      ),
              ),
            ),
            const SizedBox(width: 12),
            // Product details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product name with "Bucket" in bold
                  RichText(
                    text: TextSpan(
                      children: _highlightSearchTerm(
                        product.name,
                        _searchController.text,
                        AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                        AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w800,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    'In ${product.categoryName}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to highlight search term in product name
  List<TextSpan> _highlightSearchTerm(
    String text,
    String? searchTerm,
    TextStyle normalStyle,
    TextStyle highlightStyle,
  ) {
    // Handle null or empty search term
    if (searchTerm == null || searchTerm.trim().isEmpty) {
      return [TextSpan(text: text, style: normalStyle)];
    }

    final List<TextSpan> spans = [];
    final String lowercaseText = text.toLowerCase();
    final String lowercaseSearchTerm = searchTerm.trim().toLowerCase();

    int start = 0;
    int indexOfMatch;

    while (true) {
      indexOfMatch = lowercaseText.indexOf(lowercaseSearchTerm, start);
      if (indexOfMatch < 0) {
        // No more matches
        if (start < text.length) {
          spans.add(TextSpan(text: text.substring(start), style: normalStyle));
        }
        break;
      }

      if (indexOfMatch > start) {
        // Add text before match
        spans.add(TextSpan(
          text: text.substring(start, indexOfMatch),
          style: normalStyle,
        ));
      }

      // Add highlighted match
      spans.add(TextSpan(
        text: text.substring(
            indexOfMatch, indexOfMatch + lowercaseSearchTerm.length),
        style: highlightStyle,
      ));

      // Move start to end of match
      start = indexOfMatch + lowercaseSearchTerm.length;
    }

    return spans;
  }
}
