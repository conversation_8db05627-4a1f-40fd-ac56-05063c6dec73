import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/model/cart_item.dart';
import 'package:vegmove_ecommerce/providers/cart_provider.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class CartItemCard extends ConsumerWidget {
  final CartItem item;

  const CartItemCard({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasDiscount =
        item.product.discountValue != null && item.product.discountValue! > 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: const Color(0xFFE3E9E6)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product image
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE8F1ED),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      item.product.media.isNotEmpty
                          ? item.product.media.first.url
                          : 'https://via.placeholder.com/80',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Center(
                          child: Icon(Icons.image_not_supported,
                              color: Colors.grey),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // Product details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.product.name,
                        style: AppTextStyles.bodyLarge.copyWith(
                          fontWeight: FontWeight.w700,
                          fontSize: 15,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (item.variation != null) ...[
                        const SizedBox(height: 4),
                        // Display variation options
                        Wrap(
                          spacing: 8,
                          children: item.variation!.options.map((option) {
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFE8F1ED),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '${option.option.attribute?.name ?? "Option"}: ${option.option.name}',
                                style: AppTextStyles.caption.copyWith(
                                  color: const Color(0xFF6C7073),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ],
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            'Quantity: ${item.quantity} pc',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: const Color(0xFF6C7073),
                              fontWeight: FontWeight.w500,
                              fontSize: 15,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      // Price section
                      Text(
                        '₹${item.itemTotal}',
                        style: AppTextStyles.h3.copyWith(
                          fontWeight: FontWeight.w700,
                          fontSize: 22,
                        ),
                      ),
                      if (hasDiscount)
                        Row(
                          children: [
                            Text(
                              '₹${item.originalTotal}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF6C7073).withOpacity(0.4),
                                decoration: TextDecoration.lineThrough,
                                decorationColor: const Color(0xFF6C7073),
                                decorationThickness: 1,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const Divider(color: Color(0xFFDFE9E4), height: 1),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Remove button
                GestureDetector(
                  onTap: () {
                    ref.read(cartProvider.notifier).removeFromCart(item.id);
                  },
                  child: Text(
                    'Remove',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.error,
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ),
                // Quantity selector
                Row(
                  children: [
                    // Decrease button
                    GestureDetector(
                      onTap: () {
                        if (item.quantity > 1) {
                          ref.read(cartProvider.notifier).updateQuantity(
                                item.productId,
                                item.quantity - 1,
                                variationId: item.variationId,
                              );
                        } else {
                          ref.read(cartProvider.notifier).removeFromCart(
                                item.id,
                              );
                        }
                      },
                      child: Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          border: Border.all(color: const Color(0xFFE3E9E6)),
                          borderRadius: BorderRadius.circular(100),
                        ),
                        child: const Icon(
                          Icons.remove,
                          size: 16,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    // Quantity display
                    Container(
                      width: 36,
                      height: 36,
                      margin: const EdgeInsets.symmetric(horizontal: 10),
                      decoration: BoxDecoration(
                        color: const Color(0xFFE3E9E6),
                        borderRadius: BorderRadius.circular(100),
                      ),
                      child: Center(
                        child: Text(
                          '${item.quantity}',
                          style: AppTextStyles.bodyLarge.copyWith(
                            fontWeight: FontWeight.w600,
                            fontSize: 17,
                          ),
                        ),
                      ),
                    ),
                    // Increase button
                    GestureDetector(
                      onTap: () {
                        ref.read(cartProvider.notifier).updateQuantity(
                              item.productId,
                              item.quantity + 1,
                              variationId: item.variationId,
                            );
                      },
                      child: Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          border: Border.all(color: const Color(0xFFE3E9E6)),
                          borderRadius: BorderRadius.circular(100),
                        ),
                        child: const Icon(
                          Icons.add,
                          size: 16,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
