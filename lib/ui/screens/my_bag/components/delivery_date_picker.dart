import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:vegmove_ecommerce/providers/delivery_provider.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class DeliveryDatePicker extends ConsumerWidget {
  const DeliveryDatePicker({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = ref.watch(selectedDeliveryDateProvider);
    final dateFormat = DateFormat('EEE, MMM d, yyyy');
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery Date',
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () => _selectDate(context, ref),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: const Color(0xFFF2F4F3),
              borderRadius: BorderRadius.circular(100),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  selectedDate != null 
                      ? dateFormat.format(selectedDate)
                      : 'Choose delivery date',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: selectedDate != null 
                        ? Colors.black 
                        : const Color(0xFF6C7073),
                  ),
                ),
                const Icon(
                  Icons.calendar_today,
                  size: 20,
                  color: Color(0xFF6C7073),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, WidgetRef ref) async {
    final selectedDate = ref.read(selectedDeliveryDateProvider) ?? DateTime.now().add(const Duration(days: 1));
    
    // Set minimum date to tomorrow
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final minDate = DateTime(tomorrow.year, tomorrow.month, tomorrow.day);
    
    // Set maximum date to 14 days from now
    final maxDate = DateTime.now().add(const Duration(days: 14));
    
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: minDate,
      lastDate: maxDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null && picked != selectedDate) {
      ref.read(selectedDeliveryDateProvider.notifier).state = picked;
      // Reset the selected delivery slot when date changes
      ref.read(selectedDeliverySlotProvider.notifier).state = null;
    }
  }
}
