import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class CouponInput extends ConsumerStatefulWidget {
  final Function(String) onApplyCoupon;
  final bool couponValid;
  final String? couponCode;

  const CouponInput({
    super.key,
    required this.onApplyCoupon,
    required this.couponValid,
    this.couponCode,
  });

  @override
  ConsumerState<CouponInput> createState() => _CouponInputState();
}

class _CouponInputState extends ConsumerState<CouponInput> {
  final TextEditingController _couponController = TextEditingController();
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    if (widget.couponCode != null) {
      _couponController.text = widget.couponCode!;
    }
  }

  @override
  void didUpdateWidget(CouponInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.couponCode != oldWidget.couponCode &&
        widget.couponCode != null) {
      _couponController.text = widget.couponCode!;
    }
  }

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Coupon Code',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.black,
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(100),
                    border: Border.all(
                      color:
                          widget.couponValid || _couponController.text.isEmpty
                              ? const Color(0xFFE3E9E6)
                              : AppColors.error,
                    ),
                  ),
                  child: TextField(
                    controller: _couponController,
                    decoration: InputDecoration(
                      hintText: 'Enter coupon code',
                      hintStyle: AppTextStyles.bodyMedium.copyWith(
                        color: const Color(0xFF6C7073).withOpacity(0.5),
                      ),
                      contentPadding:
                          const EdgeInsets.symmetric(horizontal: 16),
                      border: InputBorder.none,
                      suffixIcon:
                          _couponController.text.isNotEmpty && !_isEditing
                              ? IconButton(
                                  icon: const Icon(Icons.close, size: 18),
                                  onPressed: () {
                                    setState(() {
                                      _couponController.clear();
                                      _isEditing = false;
                                    });
                                    widget.onApplyCoupon('');
                                  },
                                )
                              : null,
                    ),
                    onChanged: (value) {
                      setState(() {
                        _isEditing = true;
                      });
                    },
                  ),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isEditing = false;
                  });
                  widget.onApplyCoupon(_couponController.text);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100),
                  ),
                  minimumSize: const Size(100, 48),
                ),
                child: const Text(
                  'Apply',
                  style: TextStyle(
                    fontFamily: 'RedHatDisplay',
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                  ),
                ),
              ),
            ],
          ),
          if (!widget.couponValid &&
              _couponController.text.isNotEmpty &&
              !_isEditing)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                'Invalid coupon code',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.error,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
