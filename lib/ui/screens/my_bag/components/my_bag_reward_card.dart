import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/model/user.dart';
import 'package:vegmove_ecommerce/providers/cart_provider.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class MyBagRewardCard extends ConsumerWidget {
  final User user;
  final bool useRewardPoints;
  final VoidCallback onToggleRewardPoints;

  const MyBagRewardCard({
    super.key,
    required this.user,
    required this.useRewardPoints,
    required this.onToggleRewardPoints,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartState = ref.watch(cartProvider);
    final cart = cartState.cart;
    
    if (cart == null || user.rewardPoints <= 0 || user.rewardTier == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: const Color(0xFFE3E9E6)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.monetization_on,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${user.rewardPoints} Points Available',
                      style: AppTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Use your points to get discount',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: const Color(0xFF6C7073),
                        fontSize: 15,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                user.rewardTier?.name ?? 'Standard',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: onToggleRewardPoints,
            style: ElevatedButton.styleFrom(
              backgroundColor: useRewardPoints ? AppColors.primary : Colors.white,
              foregroundColor: useRewardPoints ? Colors.white : AppColors.primary,
              side: const BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(100),
              ),
              minimumSize: const Size(double.infinity, 48),
            ),
            child: Text(
              useRewardPoints ? 'Points Applied' : 'Use Points and Save',
              style: TextStyle(
                fontFamily: 'RedHatDisplay',
                fontWeight: FontWeight.w600,
                fontSize: 15,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
