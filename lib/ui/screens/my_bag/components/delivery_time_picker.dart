import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:vegmove_ecommerce/model/delivery_slot.dart';
import 'package:vegmove_ecommerce/providers/delivery_provider.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class DeliveryTimePicker extends ConsumerWidget {
  const DeliveryTimePicker({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDate = ref.watch(selectedDeliveryDateProvider);
    final selectedSlot = ref.watch(selectedDeliverySlotProvider);
    final slotsAsync = ref.watch(deliverySlotsProvider);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery Time',
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFFF2F4F3),
            borderRadius: BorderRadius.circular(100),
          ),
          child: slotsAsync.when(
            data: (slots) {
              if (selectedDate == null) {
                return _buildPlaceholder('Please select a delivery date first');
              }
              
              if (slots.isEmpty) {
                return _buildPlaceholder('No delivery slots available for selected date');
              }
              
              return DropdownButtonHideUnderline(
                child: DropdownButton<DeliverySlot>(
                  value: selectedSlot,
                  isExpanded: true,
                  icon: const Icon(
                    Icons.access_time,
                    size: 20,
                    color: Color(0xFF6C7073),
                  ),
                  hint: Text(
                    'Choose suitable time',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: const Color(0xFF6C7073),
                    ),
                  ),
                  items: slots.map((slot) {
                    return DropdownMenuItem<DeliverySlot>(
                      value: slot,
                      child: Text(
                        '${slot.startTime} - ${slot.endTime}',
                        style: AppTextStyles.bodyMedium,
                      ),
                    );
                  }).toList(),
                  onChanged: (DeliverySlot? newValue) {
                    if (newValue != null) {
                      ref.read(selectedDeliverySlotProvider.notifier).state = newValue;
                    }
                  },
                ),
              );
            },
            loading: () => _buildPlaceholder('Loading available time slots...'),
            error: (_, __) => _buildPlaceholder('Error loading time slots'),
          ),
        ),
      ],
    );
  }
  
  Widget _buildPlaceholder(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            text,
            style: AppTextStyles.bodyMedium.copyWith(
              color: const Color(0xFF6C7073),
            ),
          ),
          const Icon(
            Icons.access_time,
            size: 20,
            color: Color(0xFF6C7073),
          ),
        ],
      ),
    );
  }
}
