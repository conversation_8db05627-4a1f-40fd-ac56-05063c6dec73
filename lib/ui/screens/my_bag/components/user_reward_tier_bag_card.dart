import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vegmove_ecommerce/model/reward_tier.dart';
import 'package:vegmove_ecommerce/model/user.dart';
import 'package:vegmove_ecommerce/services/reward_tier_service.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

/// Provider to fetch all reward tiers for computing progress
final allRewardTiersProvider = FutureProvider<List<RewardTier>>(
  (ref) => RewardTierService().getAllRewardTiers(),
);

/// Provider to fetch all reward tiers for computing progress
final rollingSpendProvider = FutureProvider<double>(
  (ref) => RewardTierService().getRollingSpend(),
);

class UserRewardTierBagCard extends ConsumerWidget {
  final User user;
  final bool useRewardPoints;
  final VoidCallback onToggleRewardPoints;

  const UserRewardTierBagCard({
    super.key,
    required this.user,
    required this.useRewardPoints,
    required this.onToggleRewardPoints,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tiersAsync = ref.watch(allRewardTiersProvider);
    final rollingSpendAsync = ref.watch(rollingSpendProvider);

    return rollingSpendAsync.when(
      data: (rollingSpend) {
        return tiersAsync.when(
          data: (tiers) {
            final currentTier = user.rewardTier;
            if (currentTier == null) return const SizedBox.shrink();

            return Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFFE8F1ED),
                    const Color(0xFFD1E3DB),
                  ],
                ),
              ),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // Background SVG
                  Positioned.fill(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: SvgPicture.asset(
                        'assets/images/${currentTier.type.toLowerCase()}_background.svg',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Top row: name, badge, icon
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                user.name,
                                style: const TextStyle(
                                  fontSize: 20,
                                  color: AppColors.black,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.3),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                currentTier.name,
                                style:
                                    const TextStyle(color: AppColors.darkGrey),
                              ),
                            ),
                            const SizedBox(width: 8),
                            // Tier icon in top-right
                            SvgPicture.asset(
                              'assets/images/${currentTier.type.toLowerCase()}_icon.svg',
                              width: 32,
                              height: 32,
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        // Points
                        const Text(
                          'Available Points',
                          style: TextStyle(color: AppColors.black),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Image.asset(
                              'assets/images/ic_points.png',
                              width: 24,
                              height: 24,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${user.rewardPoints.toInt()}',
                              style: const TextStyle(
                                fontSize: 32,
                                color: AppColors.black,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        // Use Points button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: onToggleRewardPoints,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: useRewardPoints
                                  ? Colors.white
                                  : AppColors.primary,
                              foregroundColor: useRewardPoints
                                  ? AppColors.primary
                                  : Colors.white,
                              side: const BorderSide(color: AppColors.primary),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(100),
                              ),
                              minimumSize: const Size(double.infinity, 48),
                            ),
                            child: Text(
                              useRewardPoints
                                  ? 'Points Applied'
                                  : 'Use Points and Save',
                              style: const TextStyle(
                                fontFamily: 'RedHatDisplay',
                                fontWeight: FontWeight.w600,
                                fontSize: 15,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
          loading: () => const SizedBox(
            height: 200,
            child: Center(child: CircularProgressIndicator()),
          ),
          error: (e, st) => const SizedBox.shrink(),
        );
      },
      loading: () => const CircularProgressIndicator(),
      error: (error, stackTrace) => Text('Error: $error'),
    );
  }
}
