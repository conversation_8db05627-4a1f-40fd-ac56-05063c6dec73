import 'package:flutter/material.dart';
import 'package:vegmove_ecommerce/model/cart.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class OrderSummary extends StatelessWidget {
  final Cart cart;

  const OrderSummary({
    super.key,
    required this.cart,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: const Color(0xFFE3E9E6)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Summary',
            style: AppTextStyles.h4.copyWith(
              fontWeight: FontWeight.w700,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 30),
          // Item Total & GST
          _buildSummaryRow(
            label: 'Item Total & GST',
            value: '₹${cart.subtotal}',
          ),
          const SizedBox(height: 30),
          // Handling Charge
          _buildSummaryRow(
            label: 'Handling Charge',
            value: '₹${cart.handlingCharge}',
          ),
          const SizedBox(height: 30),
          // Delivery Fee
          _buildSummaryRow(
            label: 'Delivery Fee',
            value: double.parse(cart.deliveryFee) > 0
                ? '₹${cart.deliveryFee}'
                : '₹0 (Free)',
          ),
          const SizedBox(height: 30),
          // Coupon Discount
          _buildSummaryRow(
            label: 'Coupon Discount',
            value: double.parse(cart.couponDiscount) > 0
                ? '-₹${cart.couponDiscount}'
                : '₹0',
          ),
          // Reward Points Discount (if used)
          if (cart.useRewardPoints && double.parse(cart.rewardDiscount) > 0) ...[
            const SizedBox(height: 30),
            _buildSummaryRow(
              label: 'Reward Points Discount',
              value: '-₹${cart.rewardDiscount}',
            ),
          ],
          const SizedBox(height: 30),
          const Divider(color: Color(0xFFDFE9E4), height: 1),
          const SizedBox(height: 30),
          // Total
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'To Pay',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              Text(
                '₹${cart.total}',
                style: AppTextStyles.h3.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 20,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow({required String label, required String value}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: const Color(0xFF6C7073),
            fontWeight: FontWeight.w500,
            fontSize: 15,
          ),
        ),
        Text(
          value,
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 15,
          ),
        ),
      ],
    );
  }
}
