import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/address.dart';
import 'package:vegmove_ecommerce/model/cart.dart';
import 'package:vegmove_ecommerce/model/user.dart';
import 'package:vegmove_ecommerce/providers/address_provider.dart';
import 'package:vegmove_ecommerce/providers/cart_provider.dart';
import 'package:vegmove_ecommerce/providers/delivery_provider.dart';
import 'package:vegmove_ecommerce/providers/order_provider.dart';
import 'package:vegmove_ecommerce/ui/screens/my_bag/components/coupon_input.dart';
import 'package:vegmove_ecommerce/ui/screens/my_bag/components/delivery_date_picker.dart';
import 'package:vegmove_ecommerce/ui/screens/my_bag/components/delivery_time_picker.dart';
import 'package:vegmove_ecommerce/ui/screens/my_bag/components/user_reward_tier_bag_card.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class OrderDetailsCard extends ConsumerStatefulWidget {
  final Cart cart;
  final User user;
  final String? couponCode;
  final bool useRewardPoints;
  final Function(String) onApplyCoupon;
  final VoidCallback onToggleRewardPoints;

  const OrderDetailsCard({
    super.key,
    required this.cart,
    required this.user,
    required this.couponCode,
    required this.useRewardPoints,
    required this.onApplyCoupon,
    required this.onToggleRewardPoints,
  });

  @override
  ConsumerState<OrderDetailsCard> createState() => _OrderDetailsCardState();
}

class _OrderDetailsCardState extends ConsumerState<OrderDetailsCard> {
  bool _isPlacingOrder = false;

  /// Format the address for display
  String _formatAddress(Address address) {
    final parts = [
      address.streetName,
      if (address.apartment.isNotEmpty) 'Flat No. ${address.apartment}',
      if (address.block.isNotEmpty) 'Block ${address.block}',
      address.city,
      address.state,
      address.zipCode,
    ];
    return parts.join(', ');
  }

  Future<void> _placeOrder() async {
    if (_isPlacingOrder) return;

    setState(() {
      _isPlacingOrder = true;
    });

    try {
      final orderData = {
        'couponCode': widget.couponCode,
        'useRewardPoints': widget.useRewardPoints,
        'note': '',
      };

      final orderResult = await ref.read(placeOrderProvider(orderData).future);

      if (orderResult != null) {
        // Schedule the provider update for the next frame to avoid modifying during build
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            // Set the current order in the provider
            ref.read(currentOrderProvider.notifier).state = orderResult;

            // Refresh the cart
            ref.read(cartProvider.notifier).fetchCart();
          }
        });

        // Navigate to order details
        if (mounted) {
          context.pushNamed('order-details',
              pathParameters: {'orderId': orderResult.id.toString()});
        }
      } else {
        // Show error
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to place order. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Show error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPlacingOrder = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedAddress = ref.watch(selectedAddressProvider);
    final deliveryInfo = ref.watch(deliveryInfoProvider);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: const Color(0xFFE3E9E6)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Details Header
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'Order Details',
              style: AppTextStyles.h4.copyWith(fontWeight: FontWeight.w700),
            ),
          ),
          const Divider(color: Color(0xFFDFE9E4), height: 1),

          // Location
          GestureDetector(
            onTap: () => context.pushNamed('saved-address'),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Color(0xFFE3E9E6),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: const BoxDecoration(
                        color: Color(0xFF4CAF50),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.location_on,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (selectedAddress != null) ...[
                            Text(
                              _formatAddress(selectedAddress),
                              style: AppTextStyles.bodyLarge.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              'Tap to change delivery address',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                          ] else ...[
                            const Text(
                              'No delivery address selected',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.redAccent,
                              ),
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              'Tap to add a delivery address',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: const Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Delivery Date
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: const DeliveryDatePicker(),
          ),

          // Delivery Time
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: const DeliveryTimePicker(),
          ),

          // Reward Tier Card
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: UserRewardTierBagCard(
              user: widget.user,
              useRewardPoints: widget.useRewardPoints,
              onToggleRewardPoints: widget.onToggleRewardPoints,
            ),
          ),

          // Coupon Input
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: CouponInput(
              onApplyCoupon: widget.onApplyCoupon,
              couponValid: widget.cart.couponValid,
              couponCode: widget.couponCode,
            ),
          ),

          // Order Summary
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Item Total & GST',
                      style: AppTextStyles.bodyMedium,
                    ),
                    Text(
                      '₹${widget.cart.subtotal}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Handling Charge',
                      style: AppTextStyles.bodyMedium,
                    ),
                    Text(
                      '₹${widget.cart.handlingCharge}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Delivery Fee',
                      style: AppTextStyles.bodyMedium,
                    ),
                    Text(
                      double.parse(widget.cart.deliveryFee) > 0
                          ? '₹${widget.cart.deliveryFee}'
                          : '₹0 (Free)',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Coupon Discount',
                      style: AppTextStyles.bodyMedium,
                    ),
                    Text(
                      '₹${widget.cart.couponDiscount}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Reward Point Discount',
                      style: AppTextStyles.bodyMedium,
                    ),
                    Text(
                      '₹${widget.cart.rewardDiscount}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Divider(color: Color(0xFFDFE9E4), height: 1),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'To Pay',
                      style: AppTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Text(
                      '₹${widget.cart.total}',
                      style: AppTextStyles.h3.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Place Order Button
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: (widget.cart.canOrder &&
                        selectedAddress != null &&
                        deliveryInfo.isComplete &&
                        !_isPlacingOrder)
                    ? _placeOrder
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  disabledBackgroundColor: AppColors.grey,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100),
                  ),
                ),
                child: _isPlacingOrder
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : Text(
                        'Place Order',
                        style: AppTextStyles.buttonLarge.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w700,
                          fontSize: 15,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
