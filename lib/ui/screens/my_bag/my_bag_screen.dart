import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/providers/cart_provider.dart';
import 'package:vegmove_ecommerce/providers/user_provider.dart';
import 'package:vegmove_ecommerce/ui/components/vegmove_appbar.dart';
import 'package:vegmove_ecommerce/ui/screens/my_bag/components/cart_item_card.dart';
import 'package:vegmove_ecommerce/ui/screens/my_bag/components/empty_cart.dart';
import 'package:vegmove_ecommerce/ui/screens/my_bag/components/order_details_card.dart';
import 'package:vegmove_ecommerce/ui/screens/navigation/navigation_screen.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

// Provider for coupon code
final couponCodeProvider = StateProvider<String?>((ref) => null);

// Provider for using reward points
final useRewardPointsProvider = StateProvider<bool>((ref) => false);

class MyBagScreen extends ConsumerStatefulWidget {
  const MyBagScreen({super.key});

  @override
  ConsumerState<MyBagScreen> createState() => _MyBagScreenState();
}

class _MyBagScreenState extends ConsumerState<MyBagScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Refresh cart when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(cartProvider.notifier).fetchCart();
    });
  }

  Future<void> _applyCoupon(String code) async {
    if (code.isEmpty) {
      ref.read(couponCodeProvider.notifier).state = null;
      // ref.read(cartProvider.notifier).fetchCart();
      return;
    }

    setState(() {
      _isLoading = true;
    });
    ref.read(couponCodeProvider.notifier).state = code;
    // ref.read(cartProvider.notifier).fetchCart();

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _toggleRewardPoints() async {
    final currentValue = ref.read(useRewardPointsProvider);
    ref.read(useRewardPointsProvider.notifier).state = !currentValue;
    // ref.read(cartProvider.notifier).fetchCart();
  }

  @override
  Widget build(BuildContext context) {
    final cartState = ref.watch(cartProvider);
    final userState = ref.watch(userProvider);
    final couponCode = ref.watch(couponCodeProvider);
    final useRewardPoints = ref.watch(useRewardPointsProvider);

    // Check if user is logged in based on the user state
    final isLoggedIn = userState.isLoggedIn;

    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: const PreferredSize(
        preferredSize: Size.fromHeight(80),
        child: VegmoveAppBar(),
      ),
      body: !isLoggedIn
          ? _buildLoginPrompt(context)
          : _isLoading || cartState.isLoading
              ? const Center(child: CircularProgressIndicator())
              : cartState.cart == null || cartState.cart!.items.isEmpty
                  ? const EmptyCart()
                  : RefreshIndicator(
                      onRefresh: ref.read(cartProvider.notifier).fetchCart,
                      child: ListView(
                        padding: const EdgeInsets.all(16),
                        children: [
                          // My Bag Title
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16.0),
                            child: Text(
                              'My Bag',
                              style: AppTextStyles.h4
                                  .copyWith(fontWeight: FontWeight.w700),
                            ),
                          ),

                          // Cart items
                          ...cartState.cart!.items
                              .map((item) => CartItemCard(item: item)),

                          // Order Details Card
                          userState.user != null
                              ? OrderDetailsCard(
                                  cart: cartState.cart!,
                                  user: userState.user!,
                                  couponCode: couponCode,
                                  useRewardPoints: useRewardPoints,
                                  onApplyCoupon: _applyCoupon,
                                  onToggleRewardPoints: _toggleRewardPoints,
                                )
                              : const Center(
                                  child: CircularProgressIndicator()),

                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
    );
  }

  // Widget to show when user is not logged in
  Widget _buildLoginPrompt(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.shopping_bag_outlined,
              size: 80,
              color: AppColors.grey,
            ),
            const SizedBox(height: 24),
            Text(
              'Please login to view your bag',
              style: AppTextStyles.h3,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'You need to be logged in to add items to your bag and complete purchases.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.darkGrey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => context.pushNamed('login'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(100),
                ),
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: const Text(
                'Login',
                style: TextStyle(
                  fontFamily: 'RedHatDisplay',
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () {
                // Navigate to home screen
                ref.read(currentTabProvider.notifier).state = 0;
                context.go('/home');
              },
              child: const Text(
                'Continue Shopping',
                style: TextStyle(
                  fontFamily: 'RedHatDisplay',
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
