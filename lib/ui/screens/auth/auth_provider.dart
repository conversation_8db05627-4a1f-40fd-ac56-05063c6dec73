import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_ecommerce/providers/user_provider.dart';
import 'package:vegmove_ecommerce/services/auth_service.dart';

// Auth state
enum AuthMethod { phone, email }

class AuthState {
  final bool isLoading;
  final String? error;
  final AuthMethod authMethod;
  final String? phoneNumber;
  final String? email;
  final String? otp;
  final bool isOtpSent;
  final bool isOtpVerified;

  AuthState({
    this.isLoading = false,
    this.error,
    this.authMethod = AuthMethod.phone,
    this.phoneNumber,
    this.email,
    this.otp,
    this.isOtpSent = false,
    this.isOtpVerified = false,
  });

  AuthState copyWith({
    bool? isLoading,
    String? error,
    AuthMethod? authMethod,
    String? phoneNumber,
    String? email,
    String? otp,
    bool? isOtpSent,
    bool? isOtpVerified,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      authMethod: authMethod ?? this.authMethod,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      otp: otp ?? this.otp,
      isOtpSent: isOtpSent ?? this.isOtpSent,
      isOtpVerified: isOtpVerified ?? this.isOtpVerified,
    );
  }
}

// Auth notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  AuthNotifier(this._authService) : super(AuthState());

  Future<void> updateFirebaseToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        debugPrint('Firebase token: $token');
        await _authService.updateFirebaseToken(token);
        debugPrint('Firebase token updated successfully');
      }
    } catch (e) {
      debugPrint('Error updating Firebase token: $e');
    }
  }

  void setAuthMethod(AuthMethod method) {
    state = state.copyWith(authMethod: method, error: null);
  }

  void setPhoneNumber(String phoneNumber) {
    state = state.copyWith(phoneNumber: phoneNumber, error: null);
  }

  void setEmail(String email) {
    state = state.copyWith(email: email, error: null);
  }

  void setOtp(String otp) {
    state = state.copyWith(otp: otp, error: null);
  }

  Future<bool> validateReferralCode(String code) async {
    if (code.isEmpty) return false;
    try {
      return await _authService.validateReferralCode(code);
    } catch (e) {
      return false;
    }
  }

  Future<void> sendOtp() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // TODO: Implement OTP sending logic
      // For now, just simulate a successful OTP send
      await Future.delayed(const Duration(seconds: 1));
      state = state.copyWith(isLoading: false, isOtpSent: true);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> verifyOtp() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // TODO: Implement OTP verification logic
      // For now, just simulate a successful verification
      await Future.delayed(const Duration(seconds: 1));

      // Update Firebase token after successful verification
      await updateFirebaseToken();

      state = state.copyWith(isLoading: false, isOtpVerified: true);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<bool> loginWithEmail(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _authService.login(email, password);

      // Update Firebase token after successful login
      await updateFirebaseToken();

      // Refresh the user provider
      final container = ProviderContainer();
      container.read(userProvider.notifier).loadUserData();

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  Future<bool> registerWithEmail(
    String name,
    String email,
    String password,
    int countryId, {
    String? referralCode,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _authService.register(
        name,
        email,
        password,
        countryId,
        referralCode: referralCode,
      );

      // Update Firebase token after successful registration
      await updateFirebaseToken();

      // Refresh the user provider
      final container = ProviderContainer();
      container.read(userProvider.notifier).loadUserData();

      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  Future<void> logout(BuildContext context) async {
    state = state.copyWith(isLoading: true);

    try {
      await _authService.logout();
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('token');

      // Refresh the user provider
      final container = ProviderContainer();
      container.read(userProvider.notifier).checkLoginStatus();

      state = AuthState(); // Reset state

      if (context.mounted) {
        context.goNamed('login');
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }
}

// Providers
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthNotifier(authService);
});
