import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/country.dart';
import 'package:vegmove_ecommerce/services/country_service.dart';
import 'package:vegmove_ecommerce/ui/screens/auth/auth_provider.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

// Provider for countries
final countriesProvider = FutureProvider<List<Country>>((ref) async {
  final countryService = CountryService();
  return await countryService.getCountries();
});

// Provider for selected country
final selectedCountryProvider = StateProvider<Country?>((ref) => null);

class PhoneLoginTab extends ConsumerStatefulWidget {
  const PhoneLoginTab({super.key});

  @override
  ConsumerState<PhoneLoginTab> createState() => _PhoneLoginTabState();
}

class _PhoneLoginTabState extends ConsumerState<PhoneLoginTab> {
  final TextEditingController _phoneController = TextEditingController();

  List<Country> countries = [];

  @override
  void initState() {
    super.initState();
    // Initialize with India as default country
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  void _handleLogin() {
    final phoneNumber = _phoneController.text.trim();
    if (phoneNumber.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your phone number')),
      );
      return;
    }

    final country = ref.read(selectedCountryProvider);
    if (country == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select your country')),
      );
      return;
    }

    // Set phone number in auth provider
    final fullPhoneNumber = '${country.dialCode}$phoneNumber';
    ref.read(authProvider.notifier).setPhoneNumber(fullPhoneNumber);

    // TODO: Implement phone login logic
    // For now, just navigate to OTP screen
    if (context.mounted) {
      context.goNamed('otp-verification');
    }
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final selectedCountry = ref.watch(selectedCountryProvider);

    ref.listen<AsyncValue<List<Country>>>(
      countriesProvider,
      (previous, next) {
        next.whenData((loadedCountries) {
          setState(() {
            countries = loadedCountries;
          });

          // pick India (id == 1) or fallback to first
          final defaultCountry = loadedCountries.firstWhere(
            (c) => c.id == 1,
            orElse: () => loadedCountries.first,
          );
          ref.read(selectedCountryProvider.notifier).state = defaultCountry;
        });
      },
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Phone input with country code
          Row(
            children: [
              // Country selector
              Container(
                height: 56,
                decoration: BoxDecoration(
                  color: const Color(0xFFE8E8E8),
                  borderRadius: BorderRadius.circular(28),
                  border: Border.all(color: Colors.transparent),
                ),
                child: InkWell(
                  onTap: () {
                    // Show country picker
                    showModalBottomSheet(
                      context: context,
                      builder: (context) => CountryPickerBottomSheet(
                        countries: countries,
                        onCountrySelected: (country) {
                          ref.read(selectedCountryProvider.notifier).state =
                              country;
                          Navigator.pop(context);
                        },
                      ),
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      children: [
                        if (selectedCountry != null) ...[
                          // Flag
                          Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              image: DecorationImage(
                                image: selectedCountry.flagUrl.isNotEmpty
                                    ? NetworkImage(selectedCountry.flagUrl)
                                    : const AssetImage('assets/images/logo.png')
                                        as ImageProvider,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          // Dial code
                          Text(
                            selectedCountry.dialCode,
                            style: AppTextStyles.bodyMedium,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 8),

              // Phone number input
              Expanded(
                child: Container(
                  height: 56,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE8E8E8),
                    borderRadius: BorderRadius.circular(28),
                    border: Border.all(color: Colors.transparent),
                  ),
                  child: TextField(
                    controller: _phoneController,
                    keyboardType: TextInputType.phone,
                    decoration: const InputDecoration(
                      hintText: 'Phone number',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 16),
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      errorBorder: InputBorder.none,
                      disabledBorder: InputBorder.none,
                      fillColor: Colors.transparent,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const Spacer(),

          // Login button
          SizedBox(
            height: 56,
            child: ElevatedButton(
              onPressed: authState.isLoading ? null : _handleLogin,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
              ),
              child: authState.isLoading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(
                      'Log In',
                      style: AppTextStyles.buttonLarge,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}

// Country picker bottom sheet
class CountryPickerBottomSheet extends StatelessWidget {
  final List<Country> countries;
  final Function(Country) onCountrySelected;

  const CountryPickerBottomSheet({
    super.key,
    required this.countries,
    required this.onCountrySelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Select Country',
              style: AppTextStyles.h3,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: countries.length,
              itemBuilder: (context, index) {
                final country = countries[index];
                return ListTile(
                  leading: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      image: DecorationImage(
                        image: country.flagUrl.isNotEmpty
                            ? NetworkImage(country.flagUrl)
                            : const AssetImage('assets/images/logo.png')
                                as ImageProvider,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  title: Text(country.name),
                  subtitle: Text(country.dialCode),
                  onTap: () => onCountrySelected(country),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
