import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/providers/user_provider.dart';
import 'package:vegmove_ecommerce/ui/screens/auth/auth_provider.dart';
import 'package:vegmove_ecommerce/ui/screens/navigation/navigation_screen.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class EmailLoginTab extends ConsumerStatefulWidget {
  const EmailLoginTab({super.key});

  @override
  ConsumerState<EmailLoginTab> createState() => _EmailLoginTabState();
}

class _EmailLoginTabState extends ConsumerState<EmailLoginTab> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _handleLogin() async {
    final email = _emailController.text.trim();
    final password = _passwordController.text;

    if (email.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your email')),
      );
      return;
    }

    if (password.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your password')),
      );
      return;
    }

    // Set email in auth provider
    ref.read(authProvider.notifier).setEmail(email);

    // Login with email and password
    final success =
        await ref.read(authProvider.notifier).loginWithEmail(email, password);

    if (success && mounted) {
      // Refresh the user provider
      ref.read(userProvider.notifier).loadUserData();

      if (context.mounted) {
        // Reset the current tab to Home (0) before navigating
        ref.read(currentTabProvider.notifier).state = 0;

        // Navigate to home
        context.goNamed('home');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Set auth method to email in build method instead of initState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(authProvider.notifier).setAuthMethod(AuthMethod.email);
    });

    final authState = ref.watch(authProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Email input
          Container(
            height: 56,
            decoration: BoxDecoration(
              color: const Color(0xFFE8E8E8),
              borderRadius: BorderRadius.circular(28),
              border: Border.all(color: Colors.transparent),
            ),
            child: TextField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: const InputDecoration(
                hintText: 'Email address',
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 16),
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                fillColor: Colors.transparent,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Password input
          Container(
            height: 56,
            decoration: BoxDecoration(
              color: const Color(0xFFE8E8E8),
              borderRadius: BorderRadius.circular(28),
              border: Border.all(color: Colors.transparent),
            ),
            child: TextField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              textAlignVertical: TextAlignVertical.center,
              decoration: InputDecoration(
                hintText: 'Enter password',
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                errorBorder: InputBorder.none,
                disabledBorder: InputBorder.none,
                fillColor: Colors.transparent,
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility_off : Icons.visibility,
                    color: AppColors.darkGrey,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscurePassword = !_obscurePassword;
                    });
                  },
                ),
              ),
            ),
          ),

          // Forgot password link
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: () {
                // Navigate to forgot password screen
                // context.goNamed('forgot-password');
              },
              child: const Text(
                'Forgot password?',
                style: TextStyle(
                  color: Colors.black,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Error message if any
          if (authState.error != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Text(
                authState.error!,
                style: const TextStyle(color: AppColors.error),
                textAlign: TextAlign.center,
              ),
            ),

          // Login button
          SizedBox(
            height: 56,
            child: ElevatedButton(
              onPressed: authState.isLoading ? null : _handleLogin,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
              ),
              child: authState.isLoading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(
                      'Log In',
                      style: AppTextStyles.buttonLarge,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
