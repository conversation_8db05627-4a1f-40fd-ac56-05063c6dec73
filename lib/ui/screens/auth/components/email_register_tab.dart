import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/ui/screens/auth/auth_provider.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class EmailRegisterTab extends ConsumerStatefulWidget {
  const EmailRegisterTab({super.key});

  @override
  ConsumerState<EmailRegisterTab> createState() => _EmailRegisterTabState();
}

class _EmailRegisterTabState extends ConsumerState<EmailRegisterTab> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final TextEditingController _referralController = TextEditingController();

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  // Password validation flags
  bool _hasMinLength = false;
  bool _hasUpperAndLowerCase = false;
  bool _hasNumber = false;

  // Referral validation
  bool? _isReferralValid;
  bool _isValidatingReferral = false;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _nameController.addListener(_updateFormState);
    _emailController.addListener(_updateFormState);
    _passwordController.addListener(_validatePassword);
    _confirmPasswordController.addListener(_updateFormState);
    _referralController.addListener(_validateReferralCode);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _referralController.dispose();
    super.dispose();
  }

  void _updateFormState() {
    setState(() {
      // This will trigger a rebuild to update button state
    });
  }

  void _validatePassword() {
    final password = _passwordController.text;

    setState(() {
      _hasMinLength = password.length >= 8;
      _hasUpperAndLowerCase = password.contains(RegExp(r'[A-Z]')) &&
          password.contains(RegExp(r'[a-z]'));
      _hasNumber = password.contains(RegExp(r'[0-9]'));
    });
  }

  void _validateReferralCode() {
    final code = _referralController.text.trim();

    // Cancel previous timer
    _debounceTimer?.cancel();

    if (code.isEmpty) {
      setState(() {
        _isReferralValid = null;
        _isValidatingReferral = false;
      });
      return;
    }

    // Set validating state immediately
    setState(() {
      _isValidatingReferral = true;
    });

    // Start new timer with 500ms delay
    _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
      try {
        final isValid =
            await ref.read(authProvider.notifier).validateReferralCode(code);
        if (mounted) {
          setState(() {
            _isReferralValid = isValid;
            _isValidatingReferral = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isReferralValid = false;
            _isValidatingReferral = false;
          });
        }
      }
    });
  }

  bool get _isFormValid {
    final name = _nameController.text.trim();
    final email = _emailController.text.trim();
    final password = _passwordController.text;
    final confirmPassword = _confirmPasswordController.text;
    final referralCode = _referralController.text.trim();

    // Check basic fields
    if (name.isEmpty || email.isEmpty || password.isEmpty) {
      return false;
    }

    // Check password match
    if (password != confirmPassword) {
      return false;
    }

    // Check password requirements
    if (!_hasMinLength || !_hasUpperAndLowerCase || !_hasNumber) {
      return false;
    }

    // Check referral code if provided
    if (referralCode.isNotEmpty) {
      // If still validating or invalid, form is not valid
      if (_isValidatingReferral || _isReferralValid != true) {
        return false;
      }
    }

    return true;
  }

  void _handleRegister() async {
    final name = _nameController.text.trim();
    final email = _emailController.text.trim();
    final password = _passwordController.text;
    final confirmPassword = _confirmPasswordController.text;
    final referralCode = _referralController.text.trim();

    // Validate inputs
    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your name')),
      );
      return;
    }

    if (email.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your email')),
      );
      return;
    }

    if (password.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a password')),
      );
      return;
    }

    if (password != confirmPassword) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Passwords do not match')),
      );
      return;
    }

    if (!_hasMinLength || !_hasUpperAndLowerCase || !_hasNumber) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Password does not meet requirements')),
      );
      return;
    }

    // Validate referral code if provided
    if (referralCode.isNotEmpty && _isReferralValid != true) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content:
                Text('Please enter a valid referral code or leave it empty')),
      );
      return;
    }

    // Set email in auth provider
    ref.read(authProvider.notifier).setEmail(email);

    // Register with email and password
    final success = await ref.read(authProvider.notifier).registerWithEmail(
          name,
          email,
          password,
          1, // countryId as 1 as specified
          referralCode: referralCode.isNotEmpty ? referralCode : null,
        );

    if (success && mounted) {
      if (context.mounted) {
        context.goNamed('home');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Set auth method to email in build method instead of initState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(authProvider.notifier).setAuthMethod(AuthMethod.email);
    });

    final authState = ref.watch(authProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Name input
          Container(
            height: 56,
            decoration: BoxDecoration(
              color: const Color(0xFFE8E8E8),
              borderRadius: BorderRadius.circular(28),
              border: Border.all(color: Colors.transparent),
            ),
            child: Center(
              child: TextField(
                controller: _nameController,
                keyboardType: TextInputType.name,
                decoration: const InputDecoration(
                  hintText: 'Full name',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 16),
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  fillColor: Colors.transparent,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Email input
          Container(
            height: 56,
            decoration: BoxDecoration(
              color: const Color(0xFFE8E8E8),
              borderRadius: BorderRadius.circular(28),
              border: Border.all(color: Colors.transparent),
            ),
            child: Center(
              child: TextField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  hintText: 'Email address',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 16),
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  fillColor: Colors.transparent,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Password input
          Container(
            height: 56,
            decoration: BoxDecoration(
              color: const Color(0xFFE8E8E8),
              borderRadius: BorderRadius.circular(28),
              border: Border.all(color: Colors.transparent),
            ),
            child: Center(
              child: TextField(
                controller: _passwordController,
                obscureText: _obscurePassword,
                textAlignVertical: TextAlignVertical.center,
                decoration: InputDecoration(
                  hintText: 'Enter password',
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  fillColor: Colors.transparent,
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility_off
                          : Icons.visibility,
                      color: AppColors.darkGrey,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Confirm Password input
          Container(
            height: 56,
            decoration: BoxDecoration(
              color: const Color(0xFFE8E8E8),
              borderRadius: BorderRadius.circular(28),
              border: Border.all(color: Colors.transparent),
            ),
            child: Center(
              child: TextField(
                controller: _confirmPasswordController,
                obscureText: _obscureConfirmPassword,
                textAlignVertical: TextAlignVertical.center,
                decoration: InputDecoration(
                  hintText: 'Confirm Password',
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  fillColor: Colors.transparent,
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureConfirmPassword
                          ? Icons.visibility_off
                          : Icons.visibility,
                      color: AppColors.darkGrey,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureConfirmPassword = !_obscureConfirmPassword;
                      });
                    },
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Referral code input (optional)
          Container(
            height: 56,
            decoration: BoxDecoration(
              color: const Color(0xFFE8E8E8),
              borderRadius: BorderRadius.circular(28),
              border: Border.all(color: Colors.transparent),
            ),
            child: Center(
              child: TextField(
                textAlignVertical: TextAlignVertical.center, // ← Add this line
                controller: _referralController,
                decoration: InputDecoration(
                  hintText: 'Referral code (optional)',
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  fillColor: Colors.transparent,
                  suffixIcon: _buildReferralValidationIcon(),
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Password requirements
          _buildPasswordRequirement(
            'Use 8 or more characters',
            _hasMinLength,
          ),
          const SizedBox(height: 8),
          _buildPasswordRequirement(
            'Include uppercase & lowercase letters',
            _hasUpperAndLowerCase,
          ),
          const SizedBox(height: 8),
          _buildPasswordRequirement(
            'Add at least one number',
            _hasNumber,
          ),

          const SizedBox(height: 24),

          // Register button
          SizedBox(
            height: 56,
            child: ElevatedButton(
              onPressed: (authState.isLoading || !_isFormValid)
                  ? null
                  : _handleRegister,
              style: ElevatedButton.styleFrom(
                backgroundColor: _isFormValid ? AppColors.primary : Colors.grey,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
              ),
              child: authState.isLoading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(
                      'Register',
                      style: AppTextStyles.buttonLarge,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordRequirement(String text, bool isMet) {
    return Row(
      children: [
        Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isMet ? AppColors.primary : Colors.grey.shade300,
          ),
          child: Icon(
            Icons.check,
            size: 14,
            color: Colors.white,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          text,
          style: AppTextStyles.bodySmall.copyWith(
            color: isMet ? AppColors.primary : AppColors.darkGrey,
          ),
        ),
      ],
    );
  }

  Widget? _buildReferralValidationIcon() {
    final code = _referralController.text.trim();

    if (code.isEmpty) {
      return null;
    }

    if (_isValidatingReferral) {
      return const Padding(
        padding: EdgeInsets.all(12.0),
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: AppColors.darkGrey,
          ),
        ),
      );
    }

    if (_isReferralValid == true) {
      return const Icon(
        Icons.check_circle,
        color: Colors.green,
        size: 24,
      );
    }

    if (_isReferralValid == false) {
      return const Icon(
        Icons.error,
        color: Colors.red,
        size: 24,
      );
    }

    return null;
  }
}
