import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/providers/user_provider.dart';
import 'package:vegmove_ecommerce/ui/screens/auth/auth_provider.dart';
import 'package:vegmove_ecommerce/ui/screens/navigation/navigation_screen.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class OtpVerificationScreen extends ConsumerStatefulWidget {
  const OtpVerificationScreen({super.key});

  @override
  ConsumerState<OtpVerificationScreen> createState() =>
      _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends ConsumerState<OtpVerificationScreen> {
  final List<TextEditingController> _controllers =
      List.generate(6, (_) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(6, (_) => FocusNode());

  int _resendSeconds = 30;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
  }

  void _startResendTimer() {
    _resendSeconds = 30;
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendSeconds > 0) {
        setState(() {
          _resendSeconds--;
        });
      } else {
        _timer?.cancel();
      }
    });
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var node in _focusNodes) {
      node.dispose();
    }
    _timer?.cancel();
    super.dispose();
  }

  void _handleVerify() async {
    // Combine all digits
    final otp = _controllers.map((c) => c.text).join();

    if (otp.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter the complete 6-digit code')),
      );
      return;
    }

    // Set OTP in auth provider
    ref.read(authProvider.notifier).setOtp(otp);

    // Verify OTP
    await ref.read(authProvider.notifier).verifyOtp();

    // Navigate to home on success
    if (ref.read(authProvider).isOtpVerified && mounted) {
      // Refresh the user provider
      ref.read(userProvider.notifier).loadUserData();

      if (context.mounted) {
        // Reset the current tab to Home (0) before navigating
        ref.read(currentTabProvider.notifier).state = 0;

        // Navigate to home
        context.goNamed('home');
      }
    }
  }

  void _handleResend() {
    if (_resendSeconds > 0) return;

    // Resend OTP
    ref.read(authProvider.notifier).sendOtp();

    // Reset timer
    _startResendTimer();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final phoneNumber = authState.phoneNumber ?? '';

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            children: [
              const SizedBox(height: 40),

              // Logo
              Image.asset(
                'assets/images/logo.png',
                width: 120,
                height: 120,
              ),

              const SizedBox(height: 24),

              // Title
              const Text(
                'OTP Verification',
                style: AppTextStyles.h2,
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 8),

              // Subtitle with phone number
              Text(
                'Enter the 6-digit code sent to your phone number:\n$phoneNumber',
                style: AppTextStyles.bodyMedium
                    .copyWith(color: AppColors.darkGrey),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 32),

              // OTP input fields
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(6, (index) {
                  return Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: index == 0
                          ? AppColors.primary.withOpacity(0.1)
                          : AppColors.lightGrey.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                      border: index == 0
                          ? Border.all(color: AppColors.primary, width: 1)
                          : null,
                    ),
                    child: TextField(
                      controller: _controllers[index],
                      focusNode: _focusNodes[index],
                      textAlign: TextAlign.center,
                      keyboardType: TextInputType.number,
                      maxLength: 1,
                      decoration: const InputDecoration(
                        counterText: '',
                        border: InputBorder.none,
                      ),
                      style: AppTextStyles.h3,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      onChanged: (value) {
                        if (value.isNotEmpty && index < 5) {
                          _focusNodes[index + 1].requestFocus();
                        }
                      },
                    ),
                  );
                }),
              ),

              const SizedBox(height: 32),

              // Submit button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: authState.isLoading ? null : _handleVerify,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                  ),
                  child: authState.isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Text(
                          'Submit',
                          style: AppTextStyles.buttonLarge,
                        ),
                ),
              ),

              const SizedBox(height: 16),

              // Resend code
              TextButton(
                onPressed: _resendSeconds > 0 ? null : _handleResend,
                child: Text(
                  _resendSeconds > 0
                      ? 'Resend Code (${_resendSeconds}s)'
                      : 'Resend Code',
                  style: TextStyle(
                    color: _resendSeconds > 0
                        ? AppColors.darkGrey
                        : AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Keyboard for OTP input
              const Spacer(),
              Container(
                color: AppColors.lightGrey.withOpacity(0.5),
                child: Column(
                  children: [
                    // Message indicator
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      color: AppColors.lightGrey,
                      child: const Center(
                        child: Text(
                          'From Messages',
                          style: TextStyle(
                            color: AppColors.darkGrey,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),

                    // Sample OTP
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      color: AppColors.lightGrey,
                      child: const Center(
                        child: Text(
                          '123 456',
                          style: TextStyle(
                            color: AppColors.black,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                    // Number pad
                    const NumPad(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Number pad widget
class NumPad extends StatelessWidget {
  const NumPad({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            _buildNumKey('1'),
            _buildNumKey('2', letters: 'ABC'),
            _buildNumKey('3', letters: 'DEF'),
          ],
        ),
        Row(
          children: [
            _buildNumKey('4', letters: 'GHI'),
            _buildNumKey('5', letters: 'JKL'),
            _buildNumKey('6', letters: 'MNO'),
          ],
        ),
        Row(
          children: [
            _buildNumKey('7', letters: 'PQRS'),
            _buildNumKey('8', letters: 'TUV'),
            _buildNumKey('9', letters: 'WXYZ'),
          ],
        ),
        Row(
          children: [
            const Expanded(child: SizedBox()),
            _buildNumKey('0'),
            Expanded(
              child: Container(
                height: 60,
                color: Colors.white,
                child: const Center(
                  child: Icon(Icons.backspace, color: Colors.black),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNumKey(String number, {String letters = ''}) {
    return Expanded(
      child: Container(
        height: 60,
        color: Colors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                number,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (letters.isNotEmpty)
                Text(
                  letters,
                  style: const TextStyle(
                    fontSize: 10,
                    color: AppColors.darkGrey,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
