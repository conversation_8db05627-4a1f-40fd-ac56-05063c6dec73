import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:vegmove_ecommerce/model/reward_tier.dart';
import 'package:vegmove_ecommerce/model/user.dart';
import 'package:vegmove_ecommerce/providers/user_provider.dart';
import 'package:vegmove_ecommerce/services/reward_tier_service.dart';

// Provider for all reward tiers
final allRewardTiersProvider = FutureProvider<List<RewardTier>>(
  (ref) => RewardTierService().getAllRewardTiers(),
);

// Provider for user's rolling spend
final rollingSpendProvider = FutureProvider<double>(
  (ref) => RewardTierService().getRollingSpend(),
);

class RewardTiersScreen extends ConsumerWidget {
  const RewardTiersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userState = ref.watch(userProvider);
    final tiersAsync = ref.watch(allRewardTiersProvider);
    final rollingSpendAsync = ref.watch(rollingSpendProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Rewards Level'),
        centerTitle: true,
      ),
      body: userState.user == null
          ? const Center(child: Text('Please log in to view reward tiers'))
          : _buildBody(context, userState.user!, tiersAsync, rollingSpendAsync),
    );
  }

  Widget _buildBody(
    BuildContext context,
    User user,
    AsyncValue<List<RewardTier>> tiersAsync,
    AsyncValue<double> rollingSpendAsync,
  ) {
    return tiersAsync.when(
      data: (tiers) {
        return rollingSpendAsync.when(
          data: (rollingSpend) {
            // Filter out NONE type and sort tiers by required spending (ascending)
            final sortedTiers = List<RewardTier>.from(tiers)
              ..removeWhere((tier) => tier.type.toLowerCase() == 'none')
              ..sort((a, b) => int.parse(a.requiredRollingSpend)
                  .compareTo(int.parse(b.requiredRollingSpend)));

            // Find current tier index
            final currentTierIndex = user.rewardTier != null
                ? sortedTiers.indexWhere((t) => t.id == user.rewardTier!.id)
                : -1;

            return ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Build tier cards
                ...sortedTiers.asMap().entries.map((entry) {
                  final index = entry.key;
                  final tier = entry.value;

                  // Determine tier status
                  TierStatus status;
                  if (currentTierIndex == index) {
                    status = TierStatus.current;
                  } else if (currentTierIndex > index) {
                    status = TierStatus.achieved;
                  } else {
                    status = TierStatus.next;
                  }

                  return Padding(
                    // Increase bottom padding to accommodate the overflowing chip
                    padding: const EdgeInsets.only(bottom: 48),
                    child: _buildTierCard(
                      context,
                      tier,
                      status,
                      rollingSpend,
                      index < sortedTiers.length - 1
                          ? sortedTiers[index + 1]
                          : null,
                    ),
                  );
                }),
              ],
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (e, st) => Center(child: Text('Error: $e')),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, st) => Center(child: Text('Error: $e')),
    );
  }

  Widget _buildTierCard(
    BuildContext context,
    RewardTier tier,
    TierStatus status,
    double rollingSpend,
    RewardTier? nextTier,
  ) {
    // Determine chip color based on tier type
    Color chipColor;
    switch (tier.type.toLowerCase()) {
      case 'bronze':
        chipColor = const Color(0xFFCD7F32);
        break;
      case 'silver':
        chipColor = const Color(0xFFC0C0C0);
        break;
      case 'gold':
        chipColor = const Color(0xFFFFD700);
        break;
      case 'platinum':
        chipColor = const Color(0xFFE5E4E2);
        break;
      case 'diamond':
        chipColor = const Color(0xFFB9F2FF);
        break;
      default:
        chipColor = Colors.grey;
    }

    // We don't need progress calculation anymore as we're not showing progress bar

    // Determine status text and message
    String statusText = '';
    String statusMessage = '';
    String pointsText = '';

    switch (status) {
      case TierStatus.achieved:
        statusText = 'Achieved';
        statusMessage = 'You\'ve already unlocked this level';
        pointsText = '${tier.earnPercentage}% points with every order';
        break;
      case TierStatus.current:
        statusText = 'Current Level';
        statusMessage = nextTier != null
            ? 'Keep shopping to reach ${nextTier.name}'
            : 'You\'ve reached the highest Reward!';
        pointsText = '${tier.earnPercentage}% points on every order';
        break;
      case TierStatus.next:
        statusText = '₹${tier.requiredRollingSpend}';
        statusMessage = 'Spend more to unlock next level';
        pointsText = '${tier.earnPercentage}% points on every purchase';
        break;
    }

    return SizedBox(
      width: double.infinity,
      height: 180, // Increased height to accommodate the chip
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Background SVG
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: SvgPicture.asset(
                'assets/images/${tier.type.toLowerCase()}_background.svg',
                fit: BoxFit.cover,
              ),
            ),
          ),

          // Card content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Points percentage text
                Text(
                  pointsText,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),

                // Tier name pill
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    tier.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Status text
                if (status == TierStatus.next) ...[
                  // For next tiers, show the price amount
                  Text(
                    '₹${tier.requiredRollingSpend}',
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ] else ...[
                  // For achieved and current tiers
                  Text(
                    statusText,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ],
                const SizedBox(height: 4),
                Text(
                  statusMessage,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),

          // Tier icon
          Positioned(
            top: 16,
            right: 16,
            child: SvgPicture.asset(
              'assets/images/${tier.type.toLowerCase()}_icon.svg',
              width: 48,
              height: 48,
            ),
          ),

          // Spending requirement chip - positioned at the bottom
          Positioned(
            bottom: -20,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: chipColor.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  'Spend at least ₹${tier.requiredRollingSpend} per month',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

enum TierStatus {
  achieved,
  current,
  next,
}
