import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:vegmove_ecommerce/model/reward_point_transaction.dart';
import 'package:vegmove_ecommerce/services/reward_point_service.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

// Provider for reward point transactions
final rewardTransactionsProvider = FutureProvider<List<RewardPointTransaction>>(
  (ref) => RewardPointService().getUserTransactions(),
);

class RewardHistoryScreen extends ConsumerWidget {
  const RewardHistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionsAsync = ref.watch(rewardTransactionsProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        title: const Text(
          'Rewards History',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: GestureDetector(
          onTap: () => Navigator.pop(context),
          child: Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFEEEEEE),
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: Icon(
                Icons.arrow_back_ios,
                color: Colors.black,
                size: 18,
              ),
            ),
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.refresh(rewardTransactionsProvider.future),
        child: transactionsAsync.when(
          data: (transactions) {
            if (transactions.isEmpty) {
              return _buildEmptyState(context);
            }
            return _buildTransactionsList(transactions);
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error loading transactions: $error',
                    style: const TextStyle(fontSize: 16)),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => ref.refresh(rewardTransactionsProvider),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(100),
                    ),
                  ),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        SizedBox(height: MediaQuery.of(context).size.height * 0.2),
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.monetization_on,
                size: 80,
                color: Colors.grey,
              ),
              const SizedBox(height: 24),
              const Text(
                'No reward transactions yet',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Your reward points history will appear here',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionsList(List<RewardPointTransaction> transactions) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          itemCount: transactions.length,
          separatorBuilder: (context, index) => const Divider(
            height: 1,
            indent: 16,
            endIndent: 16,
          ),
          itemBuilder: (context, index) {
            final transaction = transactions[index];
            return _buildTransactionItem(transaction);
          },
        ),
      ),
    );
  }

  Widget _buildTransactionItem(RewardPointTransaction transaction) {
    // Determine if this is a credit (positive) or debit (negative) transaction
    final isCredit = transaction.type == 'CREDIT';
    final pointsText =
        isCredit ? '+${transaction.amount}' : '-${transaction.amount}';
    final pointsColor = isCredit ? AppColors.primary : Colors.red;

    // Format the date
    final formattedDate =
        DateFormat('MMM dd, yyyy').format(transaction.createdAt);

    // Determine the icon to show based on the transaction type or note
    IconData iconData;

    if (transaction.note?.toLowerCase().contains('order') ?? false) {
      iconData = Icons.shopping_bag_outlined;
    } else if (transaction.note?.toLowerCase().contains('delivery') ?? false) {
      iconData = Icons.local_shipping_outlined;
    } else if ((transaction.note?.toLowerCase().contains('bucket') ?? false) ||
        (transaction.note?.toLowerCase().contains('clean') ?? false)) {
      iconData = Icons.cleaning_services_outlined;
    } else if (transaction.note?.toLowerCase().contains('fruit') ?? false) {
      iconData = Icons.shopping_basket_outlined;
    } else {
      iconData = Icons.monetization_on_outlined;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      child: Row(
        children: [
          // Icon
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color:
                  isCredit ? const Color(0xFFE8F1ED) : const Color(0xFFFEEAEA),
              shape: BoxShape.circle,
            ),
            child: Icon(
              iconData,
              color: isCredit ? AppColors.primary : Colors.red,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          // Transaction details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  formattedDate,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  transaction.note ?? 'Reward Points Transaction',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          // Points
          Text(
            pointsText,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: pointsColor,
            ),
          ),
        ],
      ),
    );
  }
}
