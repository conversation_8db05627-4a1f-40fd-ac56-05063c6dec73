// CATEGORY_COLLECTION_SECTION_WIDGET.DART
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/providers/language_provider.dart';
import 'package:vegmove_ecommerce/providers/location_provider.dart';
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/services/product_service.dart';
import 'package:vegmove_ecommerce/ui/components/product_card.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';
import 'package:vegmove_ecommerce/providers/warehouse_provider.dart';

class CategoryCollectionSectionWidget extends ConsumerStatefulWidget {
  final Category category;
  final String? title;
  final bool showSegmentIcon;

  const CategoryCollectionSectionWidget({
    super.key,
    required this.category,
    this.title,
    this.showSegmentIcon = false,
  });

  @override
  ConsumerState<CategoryCollectionSectionWidget> createState() =>
      _CategoryCollectionSectionWidgetState();
}

class _CategoryCollectionSectionWidgetState
    extends ConsumerState<CategoryCollectionSectionWidget> {
  int? selectedSegmentId;

  @override
  Widget build(BuildContext context) {
    final segments = widget.category.segments ?? [];
    final location = ref.watch(locationProvider);
    final warehouseType = ref.watch(warehouseTypeProvider).type;
    final language = ref.watch(languageProvider);

    final targetCategoryId = selectedSegmentId ?? widget.category.id;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(widget.title ?? 'Best Selling', style: AppTextStyles.h3),
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              GestureDetector(
                onTap: () => setState(() => selectedSegmentId = null),
                child:
                    _buildTab('All Products', selectedSegmentId == null, null),
              ),
              ...segments.map((segment) {
                final isSelected = segment.id == selectedSegmentId;
                return GestureDetector(
                  onTap: () => setState(() => selectedSegmentId = segment.id),
                  child: _buildTab(segment.name, isSelected,
                      widget.showSegmentIcon ? segment.iconUrl : null),
                );
              }),
            ],
          ),
        ),
        const SizedBox(height: 16),
        FutureBuilder(
          future: ProductService().getProducts(
            lat: location.lat,
            long: location.long,
            warehouseType: warehouseType,
            page: 1,
            pageSize: 10,
            categoryId: targetCategoryId,
            languageCode: language.code,
          ),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return Center(child: Text('Error: ${snapshot.error}'));
            } else if (!snapshot.hasData || snapshot.data!.data.isEmpty) {
              return const Center(child: Text('No products found'));
            }

            final products = snapshot.data!.data;

            return Padding(
              padding: const EdgeInsets.all(8),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio:
                      0.48, // Further reduced to make grid items even taller
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: products.length,
                itemBuilder: (context, index) {
                  final product = products[index];
                  return ProductCard(
                    product: product,
                    onTap: () {
                      context.goNamed(
                        'product-details',
                        pathParameters: {
                          'productId': product.id.toString(),
                          'slug': product.slug!
                        },
                      );
                    },
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildTab(String name, bool isSelected, String? iconUrl) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primaryLight : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected ? AppColors.primary : Colors.grey[300]!,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: AppColors.primary.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ]
            : [],
      ),
      child: Row(
        children: [
          if (iconUrl != null)
            Container(
              width: 32,
              height: 32,
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  iconUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) =>
                      const Icon(Icons.category, size: 20, color: Colors.grey),
                ),
              ),
            ),
          Text(
            name,
            style: TextStyle(
              color: isSelected ? AppColors.primary : Colors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
