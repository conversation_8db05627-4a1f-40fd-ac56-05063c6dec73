// CATEGORY_CATEGORY_SECTION_WIDGET.DART
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/model/product.dart';
import 'package:vegmove_ecommerce/providers/language_provider.dart';
import 'package:vegmove_ecommerce/providers/location_provider.dart';
import 'package:vegmove_ecommerce/providers/warehouse_provider.dart';
import 'package:vegmove_ecommerce/services/product_service.dart';
import 'package:vegmove_ecommerce/ui/components/product_card.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class CategoryCategorySectionWidget extends ConsumerStatefulWidget {
  final Category category;
  final String? title;

  const CategoryCategorySectionWidget({
    super.key,
    required this.category,
    this.title,
  });

  @override
  ConsumerState<CategoryCategorySectionWidget> createState() =>
      _CategoryCategorySectionWidgetState();
}

class _CategoryCategorySectionWidgetState
    extends ConsumerState<CategoryCategorySectionWidget> {
  int? selectedSegmentId;
  final ScrollController _scrollController = ScrollController();

  List<Product> products = [];
  bool isLoading = false;
  String? error;
  int currentPage = 1;
  int totalPages = 1;
  bool hasMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Load products when the widget is first displayed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadProducts(refresh: true);
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMore();
    }
  }

  Future<void> _loadProducts({bool refresh = false, int? segmentId}) async {
    if (isLoading) return;

    // Handle the 'All' selection case
    // If segmentId is explicitly passed as null and refresh is true, we want to use null (for 'All')
    // Otherwise, use the provided segmentId or fall back to the current selectedSegmentId
    final targetSegmentId =
        segmentId == null && refresh ? null : (segmentId ?? selectedSegmentId);
    final targetCategoryId = targetSegmentId ?? widget.category.id;

    setState(() {
      if (refresh) {
        products = [];
        currentPage = 1;
      }
      isLoading = true;
      error = null;
    });

    try {
      final location = ref.read(locationProvider);
      final warehouseType = ref.read(warehouseTypeProvider).type;
      final language = ref.read(languageProvider);

      final response = await ProductService().getProducts(
        lat: location.lat,
        long: location.long,
        warehouseType: warehouseType,
        page: refresh ? 1 : currentPage,
        pageSize: 10,
        categoryId: targetCategoryId,
        languageCode: language.code,
      );

      setState(() {
        if (refresh) {
          products = response.data;
        } else {
          products = [...products, ...response.data];
        }
        currentPage = response.page + 1;
        totalPages = response.totalPages;
        hasMore = response.page < response.totalPages;
        isLoading = false;
        selectedSegmentId = targetSegmentId;
      });
    } catch (e) {
      setState(() {
        error = 'Error loading products: $e';
        isLoading = false;
      });
    }
  }

  Future<void> _loadMore() async {
    if (!isLoading && hasMore) {
      await _loadProducts();
    }
  }

  @override
  Widget build(BuildContext context) {
    final segments = widget.category.segments ?? [];

    return Expanded(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Segment list on the left side
          SizedBox(
            width: 100,
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
              child: Column(
                children: [
                  _buildSegmentItem(
                    name: 'All',
                    isSelected: selectedSegmentId == null,
                    iconUrl: null,
                    onTap: () => _loadProducts(refresh: true, segmentId: null),
                  ),
                  ...segments.map((segment) {
                    final isSelected = segment.id == selectedSegmentId;
                    return _buildSegmentItem(
                      name: segment.name,
                      isSelected: isSelected,
                      iconUrl: segment.iconUrl,
                      onTap: () =>
                          _loadProducts(refresh: true, segmentId: segment.id),
                    );
                  }),
                ],
              ),
            ),
          ),

          // Product grid on the right side
          Expanded(
            child: error != null
                ? Center(child: Text(error!))
                : RefreshIndicator(
                    onRefresh: () => _loadProducts(refresh: true),
                    child: products.isEmpty && !isLoading
                        ? const Center(child: Text('No products found'))
                        : CustomScrollView(
                            controller: _scrollController,
                            slivers: [
                              SliverPadding(
                                padding: const EdgeInsets.all(8),
                                sliver: SliverGrid(
                                  gridDelegate:
                                      const SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 2,
                                    childAspectRatio:
                                        0.42, // Further reduced to make grid items even taller
                                    crossAxisSpacing: 8,
                                    mainAxisSpacing: 8,
                                  ),
                                  delegate: SliverChildBuilderDelegate(
                                    (context, index) {
                                      final product = products[index];
                                      return ProductCard(
                                        product: product,
                                        onTap: () {
                                          context.goNamed(
                                            'product-details',
                                            pathParameters: {
                                              'productId':
                                                  product.id.toString(),
                                              'slug': product.slug!
                                            },
                                          );
                                        },
                                      );
                                    },
                                    childCount: products.length,
                                  ),
                                ),
                              ),
                              if (isLoading)
                                const SliverToBoxAdapter(
                                  child: Center(
                                    child: Padding(
                                      padding: EdgeInsets.all(16.0),
                                      child: CircularProgressIndicator(),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSegmentItem({
    required String name,
    required bool isSelected,
    String? iconUrl,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFE8F1ED) : Colors.transparent,
          borderRadius: BorderRadius.circular(16), // More rounded corners
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 50,
                height: 50,
                margin: const EdgeInsets.only(bottom: 4),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected
                      ? Colors.white
                      : const Color(
                          0xFFE8F1ED), // White background when selected
                ),
                child: Center(
                  child: iconUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(25),
                          child: Image.network(
                            iconUrl,
                            width: 30,
                            height: 30,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                const Icon(Icons.category,
                                    size: 20, color: Colors.grey),
                          ),
                        )
                      : const Icon(Icons.category,
                          size: 20, color: Colors.grey),
                ),
              ),
              Text(
                name,
                style: TextStyle(
                  color: isSelected ? AppColors.primary : Colors.black,
                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
