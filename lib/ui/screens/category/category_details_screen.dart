import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/providers/category_provider.dart';
import 'package:vegmove_ecommerce/ui/components/vegmove_appbar.dart';
import 'package:vegmove_ecommerce/ui/screens/category/category_category_screen.dart';
import 'package:vegmove_ecommerce/ui/screens/category/category_collection_screen.dart';

class CategoryDetailsScreen extends ConsumerWidget {
  final int categoryId;

  const CategoryDetailsScreen({super.key, required this.categoryId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoryDetails = ref.watch(categoryDetailsProvider(categoryId));

    return categoryDetails.when(
      loading: () =>
          const Scaffold(body: Center(child: CircularProgressIndicator())),
      error: (error, stackTrace) => Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(56),
          child: VegmoveAppBar(),
        ),
        body: Center(child: Text('Error loading category: $error')),
      ),
      data: (category) {
        if (category.type == CategoryType.SEGMENT && category.parent != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.pushReplacementNamed(
              'category-details',
              pathParameters: {'categoryId': category.parent!.id.toString()},
            );
          });
          return const Scaffold(
              body: Center(child: CircularProgressIndicator()));
        }

        switch (category.type) {
          case CategoryType.COLLECTION:
            return CategoryCollectionScreen(collection: category);
          case CategoryType.CATEGORY:
            return CategoryCategoryScreen(category: category);
          case CategoryType.SEGMENT:
            return const SizedBox.shrink();
        }
      },
    );
  }
}
