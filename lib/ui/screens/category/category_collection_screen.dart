import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/providers/category_provider.dart';
import 'package:vegmove_ecommerce/ui/components/vegmove_appbar.dart';
import 'package:vegmove_ecommerce/ui/screens/category/category_collection_section_widget.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class CategoryCollectionScreen extends ConsumerWidget {
  final Category collection;

  const CategoryCollectionScreen({super.key, required this.collection});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (collection.categories == null || collection.categories!.isEmpty) {
      return Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(56),
          child: VegmoveAppBar(),
        ),
        body:
            const Center(child: Text('No categories found in this collection')),
      );
    }

    final firstCategoryDetails =
        ref.watch(firstCategoryDetailsProvider(collection.id));

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(56),
        child: VegmoveAppBar(),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: _buildBreadcrumb(context, collection),
            ),
            if (collection.bannerUrl != null)
              Container(
                width: double.infinity,
                height: 180,
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  image: DecorationImage(
                    image: NetworkImage(collection.bannerUrl!),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text('Shop by Category',
                  style:
                      AppTextStyles.h2.copyWith(fontWeight: FontWeight.bold)),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 0.75,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: collection.categories!.length,
                itemBuilder: (context, index) {
                  final category = collection.categories![index];
                  return GestureDetector(
                    onTap: () => GoRouter.of(context).pushNamed(
                      'category-details',
                      pathParameters: {'categoryId': category.id.toString()},
                    ),
                    child: Column(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              // Only use background color for fallback icon
                              color: category.iconUrl == null
                                  ? AppColors.categoryCardBackground
                                  : null,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Center(
                              child: category.iconUrl != null
                                  ? ClipRRect(
                                      borderRadius: BorderRadius.circular(16),
                                      child: Image.network(
                                        category.iconUrl!,
                                        width: double.infinity, // Full width
                                        height: double.infinity, // Full height
                                        fit: BoxFit
                                            .cover, // Cover the entire container
                                        loadingBuilder:
                                            (context, child, loadingProgress) {
                                          if (loadingProgress == null)
                                            return child;
                                          return Container(
                                            color: AppColors
                                                .categoryCardBackground,
                                            child: Center(
                                              child: CircularProgressIndicator(
                                                color: AppColors.primary,
                                                value: loadingProgress
                                                            .expectedTotalBytes !=
                                                        null
                                                    ? loadingProgress
                                                            .cumulativeBytesLoaded /
                                                        loadingProgress
                                                            .expectedTotalBytes!
                                                    : null,
                                              ),
                                            ),
                                          );
                                        },
                                        errorBuilder:
                                            (context, error, stackTrace) =>
                                                Container(
                                          color:
                                              AppColors.categoryCardBackground,
                                          child: const Icon(
                                            Icons.category,
                                            size: 40,
                                            color: AppColors.primary,
                                          ),
                                        ),
                                      ),
                                    )
                                  : const Icon(Icons.category,
                                      size: 40, color: AppColors.primary),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          category.name,
                          textAlign: TextAlign.center,
                          style: AppTextStyles.caption.copyWith(
                              fontWeight: FontWeight.w600, fontSize: 13),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 24),
            firstCategoryDetails.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => Center(child: Text('Error: $error')),
              data: (firstCategory) =>
                  CategoryCollectionSectionWidget(category: firstCategory),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBreadcrumb(BuildContext context, Category category) {
    final crumbs = <Widget>[];

    crumbs.add(_breadcrumbLink('Home', () => context.goNamed('home')));
    crumbs.add(_breadcrumbArrow());

    if (category.parent != null) {
      if (category.parent!.parent != null) {
        crumbs.add(_breadcrumbLink(
          category.parent!.parent!.name,
          () => context.goNamed('category-details', pathParameters: {
            'categoryId': category.parent!.parent!.id.toString()
          }),
        ));
        crumbs.add(_breadcrumbArrow());
      }
      crumbs.add(_breadcrumbLink(
        category.parent!.name,
        () => context.goNamed('category-details',
            pathParameters: {'categoryId': category.parent!.id.toString()}),
      ));
      crumbs.add(_breadcrumbArrow());
    }

    crumbs.add(Text(
      category.name,
      style: const TextStyle(
          color: AppColors.primary, fontSize: 14, fontWeight: FontWeight.bold),
    ));

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(children: crumbs),
    );
  }

  Widget _breadcrumbLink(String label, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child:
          Text(label, style: const TextStyle(color: Colors.grey, fontSize: 14)),
    );
  }

  Widget _breadcrumbArrow() =>
      const Icon(Icons.chevron_right, size: 16, color: Colors.grey);
}
