// CATEGORY_CATEGORY_SCREEN.DART
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/model/category.dart';
import 'package:vegmove_ecommerce/ui/components/vegmove_appbar.dart';
import 'package:vegmove_ecommerce/ui/screens/category/category_category_section_widget.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class CategoryCategoryScreen extends StatelessWidget {
  final Category category;

  const CategoryCategoryScreen({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(56),
        child: VegmoveAppBar(),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 16,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            child: _buildBreadcrumb(context, category),
          ),
          const Sized<PERSON>ox(height: 8),
          CategoryCategorySectionWidget(
            title: category.name,
            category: category,
          ),
        ],
      ),
    );
  }

  Widget _buildBreadcrumb(BuildContext context, Category category) {
    final crumbs = <Widget>[];

    crumbs.add(_breadcrumbLink('Home', () => context.goNamed('home')));
    crumbs.add(_breadcrumbArrow());

    if (category.parent != null) {
      if (category.parent!.parent != null) {
        crumbs.add(_breadcrumbLink(
          category.parent!.parent!.name,
          () => context.goNamed('category-details', pathParameters: {
            'categoryId': category.parent!.parent!.id.toString()
          }),
        ));
        crumbs.add(_breadcrumbArrow());
      }
      crumbs.add(_breadcrumbLink(
        category.parent!.name,
        () => context.goNamed('category-details',
            pathParameters: {'categoryId': category.parent!.id.toString()}),
      ));
      crumbs.add(_breadcrumbArrow());
    }

    crumbs.add(Text(
      category.name,
      style: const TextStyle(
          color: AppColors.primary, fontSize: 14, fontWeight: FontWeight.bold),
    ));

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(children: crumbs),
    );
  }

  Widget _breadcrumbLink(String label, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child:
          Text(label, style: const TextStyle(color: Colors.grey, fontSize: 14)),
    );
  }

  Widget _breadcrumbArrow() =>
      const Icon(Icons.chevron_right, size: 16, color: Colors.grey);
}
