// settings_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:vegmove_ecommerce/network/dto/expiring_points_response_dto.dart';
import 'package:vegmove_ecommerce/network/dto/user_tier_response_dto.dart';
import 'package:vegmove_ecommerce/providers/language_provider.dart';
import 'package:vegmove_ecommerce/providers/user_provider.dart';
import 'package:vegmove_ecommerce/services/reward_point_service.dart';
import 'package:vegmove_ecommerce/services/reward_tier_service.dart';
import 'package:vegmove_ecommerce/ui/components/user_reward_tier_card.dart';
import 'package:vegmove_ecommerce/ui/screens/auth/auth_provider.dart';

/// Providers
final userTierProvider = FutureProvider<UserTierResponseDto>(
    (ref) => RewardTierService().getUserTier());
final userPointsProvider =
    FutureProvider<double>((ref) => RewardPointService().getUserRewardPoints());
final expiringPointsProvider = FutureProvider<ExpiringPointsResponseDto>(
    (ref) => RewardPointService().getExpiringPoints());

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userState = ref.watch(userProvider);
    final expiringAsync = ref.watch(expiringPointsProvider);
    final language = ref.watch(languageProvider);

    // Check if user is logged in based on the user state
    final isLoggedIn = userState.isLoggedIn;

    return Scaffold(
      backgroundColor: const Color(0xFFF7F8FA),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: const Text('Settings', style: TextStyle(color: Colors.black)),
        actions: [
          if (isLoggedIn && userState.user != null)
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: CircleAvatar(
                radius: 18,
                backgroundImage: userState.user!.profilePicture != null
                    ? NetworkImage(userState.user!.profilePicture!)
                    : null,
                child: userState.user!.profilePicture == null
                    ? const Icon(Icons.person)
                    : null,
              ),
            ),
          if (isLoggedIn && userState.isLoading)
            const Padding(
              padding: EdgeInsets.only(right: 16),
              child: SizedBox(
                  width: 24, height: 24, child: CircularProgressIndicator()),
            ),
        ],
      ),
      body: isLoggedIn
          ? _buildLoggedInView(context, ref, userState, expiringAsync, language)
          : _buildLoggedOutView(context, ref, language),
    );
  }

  // Widget for logged-in user view
  Widget _buildLoggedInView(
      BuildContext context,
      WidgetRef ref,
      UserState userState,
      AsyncValue<ExpiringPointsResponseDto> expiringAsync,
      LanguageNotifier language) {
    final user = userState.user;

    if (user == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Reward Tier Card
          UserRewardTierCard(
            user: user,
          ),
          const SizedBox(height: 32),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => context.pushNamed('reward-history'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4CAF50), // green
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100)),
              ),
              child: const Text('View Reward History',
                  style: TextStyle(fontSize: 16)),
            ),
          ),
          const SizedBox(height: 16),
          // Expiring Points Card
          expiringAsync.when(
            data: (exp) {
              if (exp.expiryDate == null) {
                return const SizedBox.shrink();
              }

              final days =
                  exp.expiryDate?.difference(DateTime.now()).inDays ?? 0;
              return Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFE3E9E6),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: const Color(0xFF00AB55),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: SizedBox(
                          width: 24,
                          height: 24,
                          child: Icon(
                            Icons.access_time,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${exp.expiringPoints.toInt()} points will expire in $days days',
                            style: const TextStyle(
                                fontWeight: FontWeight.w500, fontSize: 14),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Expires on: ${DateFormat.yMMMMd().format(exp.expiryDate!)}',
                            style: const TextStyle(
                                fontSize: 12, color: Colors.black54),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (_, __) => const SizedBox.shrink(),
          ),
          const SizedBox(height: 24),
          // Settings List
          _buildSection(
            items: [
              _SettingsItem(
                  icon: Icons.shopping_bag,
                  title: 'My Orders',
                  onTap: () => context.pushNamed('orders')),
              _SettingsItem(
                  icon: Icons.location_on,
                  title: 'Saved Address',
                  onTap: () => context.pushNamed('saved-address')),
              _SettingsItem(
                  icon: Icons.person,
                  title: 'Profile Settings',
                  onTap: () => context.pushNamed('profile-settings')),
              _SettingsItem(
                  icon: Icons.language,
                  title:
                      'Language • ${language.code == 'en' ? 'English' : 'বাংলা'}',
                  onTap: () => context.pushNamed('language')),
              _SettingsItem(
                  icon: Icons.support_agent,
                  title: 'Support Center',
                  onTap: () {}),
              _SettingsItem(
                icon: Icons.system_update,
                title: 'Update Available',
                onTap: () {},
                trailing: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFF3CD),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text('v1.3.7',
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87)),
                ),
              ),
            ],
          ),
          // Log Out
          GestureDetector(
            onTap: () {
              // Show confirmation dialog
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Logout'),
                  content: const Text('Are you sure you want to logout?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        // Call the logout method from auth provider
                        ref.read(authProvider.notifier).logout(context);
                      },
                      child: const Text('Logout'),
                    ),
                  ],
                ),
              );
            },
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(vertical: 16),
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.redAccent),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: const [
                  Icon(Icons.logout, color: Colors.redAccent),
                  SizedBox(width: 12),
                  Text('Log Out',
                      style: TextStyle(
                          color: Colors.redAccent,
                          fontWeight: FontWeight.w600,
                          fontSize: 16)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Widget for logged-out user view
  Widget _buildLoggedOutView(
      BuildContext context, WidgetRef ref, LanguageNotifier language) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Login prompt
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            margin: const EdgeInsets.only(bottom: 24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Icon(
                  Icons.account_circle,
                  size: 80,
                  color: Color(0xFF4CAF50),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Welcome to Vegmove',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Login to access your orders, saved addresses, and reward points',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => context.pushNamed('login'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4CAF50),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(100),
                      ),
                    ),
                    child: const Text(
                      'Login',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                TextButton(
                  onPressed: () => context.pushNamed('register'),
                  child: const Text(
                    'Create an Account',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF4CAF50),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Limited settings list for non-logged in users
          _buildSection(
            items: [
              _SettingsItem(
                  icon: Icons.language,
                  title:
                      'Language • ${language.code == 'en' ? 'English' : 'বাংলা'}',
                  onTap: () => context.pushNamed('language')),
              _SettingsItem(
                  icon: Icons.support_agent,
                  title: 'Support Center',
                  onTap: () {}),
              _SettingsItem(
                icon: Icons.system_update,
                title: 'Update Available',
                onTap: () {},
                trailing: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFF3CD),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text('v1.3.7',
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87)),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection({required List<_SettingsItem> items}) {
    return Column(
      children: items.map((item) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: ListTile(
            leading: Container(
              height: 40,
              width: 40,
              decoration: BoxDecoration(
                color: Color(0xFFE8F1ED),
                borderRadius: BorderRadius.circular(100),
              ),
              child: Center(
                child: SizedBox(
                  height: 24,
                  width: 24,
                  child: Icon(
                    item.icon,
                    color: Colors.black54,
                    size: 24,
                  ),
                ),
              ),
            ),
            title: Text(item.title, style: const TextStyle(fontSize: 16)),
            trailing: item.trailing != null
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      item.trailing!,
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_forward_ios,
                          size: 16, color: Colors.black38)
                    ],
                  )
                : Icon(Icons.arrow_forward_ios,
                    size: 16, color: Colors.black38),
            onTap: item.onTap,
          ),
        );
      }).toList(),
    );
  }
}

class _SettingsItem {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final Widget? trailing;

  _SettingsItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.trailing,
  });
}
