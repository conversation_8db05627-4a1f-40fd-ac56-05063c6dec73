import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:vegmove_ecommerce/providers/language_provider.dart';
import 'package:vegmove_ecommerce/providers/location_provider.dart';
import 'package:vegmove_ecommerce/model/banner.dart' as app_banner;
import 'package:vegmove_ecommerce/model/home_section.dart';
import 'package:vegmove_ecommerce/services/banner_service.dart';
import 'package:vegmove_ecommerce/services/home_section_service.dart';
import 'package:vegmove_ecommerce/ui/components/home_section_widget.dart';
import 'package:vegmove_ecommerce/ui/components/vegmove_appbar.dart';
import 'package:vegmove_ecommerce/ui/components/warehouse_switch.dart';
import 'package:vegmove_ecommerce/ui/screens/navigation/navigation_screen.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';
import 'package:vegmove_ecommerce/providers/warehouse_provider.dart';

// Provider for banners
final bannersProvider = FutureProvider<List<app_banner.Banner>>((ref) async {
  try {
    final bannerService = BannerService();
    final location = ref.watch(locationProvider);
    final languageCode = ref.watch(languageProvider);

    return await bannerService.getBanners(
      lat: location.lat,
      long: location.long,
      languageCode: languageCode.code,
    );
  } catch (e) {
    debugPrint('Error fetching banners: $e');
    // Return empty list on error
    return [];
  }
});

// Provider for home sections
final homeSectionsProvider = FutureProvider<List<HomeSection>>((ref) async {
  try {
    final warehouseType = ref.watch(warehouseTypeProvider).type;
    final homeSectionService = HomeSectionService();
    final location = ref.watch(locationProvider);
    final languageCode = ref.watch(languageProvider);

    return await homeSectionService.getHomeSections(
      lat: location.lat,
      long: location.long,
      warehouseType: warehouseType,
      languageCode: languageCode.code,
    );
  } catch (e) {
    // Return empty list on error
    return [];
  }
});

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  int _currentBannerIndex = 0;

  @override
  Widget build(BuildContext context) {
    final homeSectionsAsync = ref.watch(homeSectionsProvider);
    final bannersAsync = ref.watch(bannersProvider);
    // Watch warehouse provider to rebuild when it changes
    ref.watch(warehouseTypeProvider);
    ref.watch(locationProvider);

    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(56),
        child: VegmoveAppBar(),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(bannersProvider);
          return ref.refresh(homeSectionsProvider.future);
        },
        child: ListView(
          padding: const EdgeInsets.only(bottom: 16),
          children: [
            // Search bar and warehouse type switch
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  // Search bar
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        // Update the current tab to search (index 1) and let the navigation happen through the tab bar
                        ref.read(currentTabProvider.notifier).state = 1;
                        context.go('/search');
                      },
                      child: Container(
                        height: 48,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(100),
                          // border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.search,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Search your need...',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Warehouse type switch
                  const WarehouseSwitch(),
                ],
              ),
            ),

            // Banner section
            bannersAsync.when(
              data: (banners) {
                if (banners.isEmpty) {
                  return const SizedBox.shrink();
                }

                return Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: CarouselSlider(
                        options: CarouselOptions(
                          height: 180,
                          viewportFraction: 1.0,
                          enlargeCenterPage: false,
                          autoPlay: true,
                          autoPlayInterval: const Duration(seconds: 3),
                          autoPlayAnimationDuration:
                              const Duration(milliseconds: 800),
                          onPageChanged: (index, reason) {
                            setState(() {
                              _currentBannerIndex = index;
                            });
                          },
                        ),
                        items: banners.map((banner) {
                          return Builder(
                            builder: (BuildContext context) {
                              return Container(
                                width: MediaQuery.of(context).size.width,
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 5.0),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  color: const Color(0xFFE8F1ED),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    banner.media.url,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return const Center(
                                        child: Icon(
                                          Icons.image_not_supported,
                                          size: 50,
                                          color: Colors.grey,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              );
                            },
                          );
                        }).toList(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Indicators
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: banners.asMap().entries.map((entry) {
                        return GestureDetector(
                          onTap: () {
                            // We can't control the carousel directly without a controller
                            // but we can update the indicator
                            setState(() {
                              _currentBannerIndex = entry.key;
                            });
                          },
                          child: Container(
                            width: 8.0,
                            height: 8.0,
                            margin: const EdgeInsets.symmetric(horizontal: 4.0),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _currentBannerIndex == entry.key
                                  ? AppColors.primary
                                  : Colors.grey.shade300,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                );
              },
              loading: () => const SizedBox(
                height: 180,
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
              error: (error, stackTrace) => const SizedBox.shrink(),
            ),
            const SizedBox(height: 16),
            homeSectionsAsync.when(
              data: (sections) {
                return Column(
                  children: [
                    if (sections.isEmpty)
                      const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Center(
                          child: Text('No products available'),
                        ),
                      )
                    else
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: Column(
                          children: sections.map((section) {
                            return HomeSectionWidget(section: section);
                          }).toList(),
                        ),
                      ),
                  ],
                );
              },
              loading: () => Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Text('Error: ${error.toString()}'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
