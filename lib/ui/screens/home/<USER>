import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';

class SectionDetailsScreen extends ConsumerWidget {
  final String sectionId;
  
  const SectionDetailsScreen({
    super.key,
    required this.sectionId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Section Details'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go('/home');
          },
        ),
      ),
      body: Center(
        child: Text(
          'Section Details Screen (ID: $sectionId)',
          style: AppTextStyles.h3,
        ),
      ),
    );
  }
}
