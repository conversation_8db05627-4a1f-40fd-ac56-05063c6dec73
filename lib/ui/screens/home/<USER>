import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';
import 'package:vegmove_ecommerce/model/enums.dart';
import 'package:vegmove_ecommerce/model/media.dart';
import 'package:vegmove_ecommerce/model/product.dart';
import 'package:vegmove_ecommerce/model/product_attribute.dart';
import 'package:vegmove_ecommerce/model/product_attribute_option.dart';
import 'package:vegmove_ecommerce/providers/cart_provider.dart';
import 'package:vegmove_ecommerce/providers/product_provider.dart';
import 'package:vegmove_ecommerce/providers/related_products_provider.dart';
import 'package:vegmove_ecommerce/ui/components/product_card.dart';
import 'package:vegmove_ecommerce/ui/components/product_policy_list.dart';
import 'package:vegmove_ecommerce/ui/components/vegmove_appbar.dart';
import 'package:vegmove_ecommerce/ui/theme/app_theme.dart';
import 'package:vegmove_ecommerce/util.dart';

class ProductDetailsScreen extends ConsumerStatefulWidget {
  final String productId;
  final String slug;

  const ProductDetailsScreen({
    super.key,
    required this.productId,
    required this.slug,
  });

  @override
  ConsumerState<ProductDetailsScreen> createState() =>
      _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends ConsumerState<ProductDetailsScreen> {
  // Selected media index for the image carousel
  int _selectedMediaIndex = 0;
  // Map to store selected option IDs for each attribute
  final Map<int, int> _selectedOptions = {};
  // Track the previous variation ID to detect changes
  int? _previousVariationId;

  @override
  void initState() {
    super.initState();
    // Initialize the product details with the current product ID
    // Use Future.microtask to ensure it runs after the current build cycle
    Future.microtask(() {
      if (mounted) {
        ref.read(productDetailsProvider.notifier).updateOptions(
              widget.slug,
              null, // Start with no options selected
            );
        // Fetch related products
        ref.read(relatedProductsProvider.notifier).fetchRelatedProducts(
              int.parse(widget.productId),
            );
      }
    });
  }

  @override
  void didUpdateWidget(ProductDetailsScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the product ID changed, reset everything
    if (oldWidget.slug != widget.slug) {
      setState(() {
        _selectedMediaIndex = 0;
        _selectedOptions.clear();
      });

      Future.microtask(() {
        if (mounted) {
          ref.read(productDetailsProvider.notifier).updateOptions(
                widget.slug,
                null, // Start with no options selected
              );
          // Fetch related products for the new product
          ref.read(relatedProductsProvider.notifier).fetchRelatedProducts(
                int.parse(widget.productId),
              );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch the product state
    final productState = ref.watch(productDetailsProvider);

    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(56),
        child: VegmoveAppBar(),
      ),
      body: _buildBody(context, productState),
    );
  }

  Widget _buildBody(BuildContext context, ProductState state) {
    if (state.isLoading && state.product == null) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null && state.product == null) {
      return Center(
        child: Text('Error loading product: ${state.error}'),
      );
    }

    if (state.product == null) {
      return const Center(
        child: Text('Product not found'),
      );
    }

    return _buildProductDetails(context, state.product!);
  }

  // Initialize selected options if not already set
  void _initializeSelectedOptions(List<ProductAttribute> attributes) {
    bool optionsChanged = false;

    for (final attribute in attributes) {
      if (attribute.options.isNotEmpty &&
          !_selectedOptions.containsKey(attribute.id)) {
        _selectedOptions[attribute.id] = attribute.options.first.id;
        optionsChanged = true;
      }
    }

    // If we've initialized any new options, update the product
    // But do it after the current build cycle using a post-frame callback
    if (optionsChanged) {
      // Use Future.microtask to schedule the update after the build is complete
      Future.microtask(() {
        if (mounted) {
          final selectedOptionIds = _selectedOptions.values.toList();
          ref.read(productDetailsProvider.notifier).updateOptions(
                widget.slug,
                selectedOptionIds.isEmpty ? null : selectedOptionIds,
              );
        }
      });
    }
  }

  // Handle option selection
  void _selectOption(int attributeId, int optionId) {
    // Only update if the option is actually changing
    if (_selectedOptions[attributeId] != optionId) {
      setState(() {
        _selectedOptions[attributeId] = optionId;
        // Reset media index when a new option is selected
        // This will be updated properly when the product data is refreshed
        _selectedMediaIndex = 0;
      });

      // Update the product with new options after the current build cycle
      Future.microtask(() {
        if (mounted) {
          final selectedOptionIds = _selectedOptions.values.toList();
          ref.read(productDetailsProvider.notifier).updateOptions(
                widget.slug,
                selectedOptionIds.isEmpty ? null : selectedOptionIds,
              );
        }
      });
    }
  }

  // Update selected media index when a variation changes
  void _updateSelectedMediaForVariation(Product product) {
    // Get current variation ID
    final currentVariationId = product.selectedVariation?.id;

    // Check if variation has changed
    if (currentVariationId != null &&
        currentVariationId != _previousVariationId &&
        product.selectedVariation!.media.isNotEmpty) {
      // Update the previous variation ID
      _previousVariationId = currentVariationId;

      // Use post-frame callback to avoid setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            // Reset to show the first image of the variation
            _selectedMediaIndex = 0;
          });
        }
      });
    } else if (currentVariationId != _previousVariationId) {
      // Just update the tracking ID even if there's no media
      _previousVariationId = currentVariationId;
    }
  }

  Widget _buildProductDetails(BuildContext context, Product product) {
    // Initialize selected options if not already set
    if (product.attributes != null && product.attributes!.isNotEmpty) {
      _initializeSelectedOptions(product.attributes!);
    }

    // Update selected media when variation changes
    _updateSelectedMediaForVariation(product);

    // Check if product has a discount
    final hasDiscount =
        product.discountValue != null && product.discountValue! > 0;

    // Get cart state to check if product is in cart
    final cartState = ref.watch(cartProvider);
    final variationId = product.selectedVariation?.id;
    final isInCart = cartState.isProductInCart(product.id, variationId);
    final quantity =
        isInCart ? cartState.getProductQuantity(product.id, variationId) : 0;

    // Calculate discount percentage if applicable
    String? discountPercentage;
    if (hasDiscount && product.discountType == AmountType.PERCENTAGE) {
      discountPercentage = '${product.discountValue!.toInt()}% OFF';
    } else if (hasDiscount) {
      // Calculate percentage for flat discount
      final originalPrice = double.parse(product.originalPrice);
      final currentPrice = double.parse(product.price);
      if (originalPrice > 0) {
        final percentage =
            ((originalPrice - currentPrice) / originalPrice * 100).round();
        discountPercentage = '$percentage% OFF';
      }
    }

    // Collect and organize media
    List<Media> allMedia = [];

    // If product has a thumbnail, add it first
    if (product.thumbnail != null) {
      allMedia.add(product.thumbnail!);
    }

    // If a variation is selected and has media, prioritize it
    if (product.selectedVariation != null &&
        product.selectedVariation!.media.isNotEmpty) {
      // Put the selected variation's media next
      allMedia.addAll(product.selectedVariation!.media);

      // Then add the base product media (excluding thumbnail if it exists)
      if (product.thumbnail != null) {
        // Add all media except the thumbnail (which we already added)
        allMedia.addAll(
            product.media.where((media) => media.id != product.thumbnail!.id));
      } else {
        // Add all media
        allMedia.addAll(product.media);
      }

      // Then add other variations' media (excluding the selected variation)
      if (product.variations != null) {
        for (final variation in product.variations!) {
          if (variation.id != product.selectedVariation!.id) {
            allMedia.addAll(variation.media);
          }
        }
      }
    } else {
      // No variation selected or selected variation has no media

      // Add base product media (excluding thumbnail if it exists)
      if (product.thumbnail != null) {
        // Add all media except the thumbnail (which we already added)
        allMedia.addAll(
            product.media.where((media) => media.id != product.thumbnail!.id));
      } else {
        // Add all media
        allMedia.addAll(product.media);
      }

      // Then add all variations' media
      if (product.variations != null) {
        for (final variation in product.variations!) {
          allMedia.addAll(variation.media);
        }
      }
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Breadcrumb navigation
          _buildBreadcrumb(context, product),

          // Product image
          Padding(
            padding: const EdgeInsets.all(16),
            child: _buildProductImage(product, allMedia),
          ),
          // Product details
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product name
                Text(
                  product.name,
                  style: AppTextStyles.h3.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),

                const SizedBox(height: 8),

                // Price section with discount
                hasDiscount
                    ? _buildDiscountedPriceSection(product, discountPercentage)
                    : _buildRegularPriceSection(product),

                const SizedBox(height: 16),

                // Product attributes and options
                if (product.hasVariations && product.attributes != null)
                  _buildAttributeOptions(product),

                const SizedBox(height: 16),

                // Add to bag button or quantity selector
                product.inStock
                    ? isInCart
                        ? _buildQuantitySelector(context, product, quantity)
                        : _buildAddToBagButton(context, product)
                    : _buildOutOfStockButton(),

                const SizedBox(height: 24),

                // Product policies section
                if (product.productPolicies.isNotEmpty)
                  ProductPolicyList(policies: product.productPolicies),

                const SizedBox(height: 16),

                // Highlights section
                _buildHighlightsSection(product),

                const SizedBox(height: 16),

                // Information section
                _buildInformationSection(product),

                const SizedBox(height: 32),

                // Related products section
                _buildRelatedProductsSection(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build attribute options selection UI
  Widget _buildAttributeOptions(Product product) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: product.attributes!.map((attribute) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Attribute name with label (e.g., "Choose Color:")
            Text(
              'Choose ${attribute.name}:',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 12),

            // Options for this attribute
            _buildOptionsForAttribute(attribute),
            const SizedBox(height: 20),
          ],
        );
      }).toList(),
    );
  }

  // Build options based on attribute type
  Widget _buildOptionsForAttribute(ProductAttribute attribute) {
    // Check if this is a color attribute
    final hasColorOptions = attribute.options.any(
        (option) => option.colorCode != null && option.colorCode!.isNotEmpty);

    // Check if this is an image attribute
    final hasImageOptions = attribute.options.any(
        (option) => option.imageUrl != null && option.imageUrl!.isNotEmpty);

    if (hasColorOptions) {
      // For color attributes, show color circles in a row
      return Row(
        children: attribute.options.map((option) {
          final isSelected = _selectedOptions[attribute.id] == option.id;
          return Padding(
            padding: const EdgeInsets.only(right: 12.0),
            child: _buildColorOption(attribute, option, isSelected),
          );
        }).toList(),
      );
    } else if (hasImageOptions) {
      // For image attributes, show small thumbnails in a row
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: attribute.options.map((option) {
            final isSelected = _selectedOptions[attribute.id] == option.id;
            return Padding(
              padding: const EdgeInsets.only(right: 12.0),
              child: _buildImageOption(attribute, option, isSelected),
            );
          }).toList(),
        ),
      );
    } else {
      // For text attributes, show buttons in a wrap
      return Wrap(
        spacing: 12,
        runSpacing: 12,
        children: attribute.options.map((option) {
          final isSelected = _selectedOptions[attribute.id] == option.id;
          return _buildTextOption(attribute, option, isSelected);
        }).toList(),
      );
    }
  }

  // Build color option circle
  Widget _buildColorOption(ProductAttribute attribute,
      ProductAttributeOption option, bool isSelected) {
    // Parse color from hex code
    final color = option.colorCode != null && option.colorCode!.isNotEmpty
        ? Color(int.parse('0xFF${option.colorCode!.substring(1)}'))
        : Colors.grey;

    return GestureDetector(
      onTap: () => _selectOption(attribute.id, option.id),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.transparent,
            width: 2,
          ),
          // Add outer white ring for selected items
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Colors.white,
                    spreadRadius: 2,
                    blurRadius: 0,
                  ),
                ]
              : null,
        ),
      ),
    );
  }

  // Build image option thumbnail
  Widget _buildImageOption(ProductAttribute attribute,
      ProductAttributeOption option, bool isSelected) {
    return GestureDetector(
      onTap: () => _selectOption(attribute.id, option.id),
      child: Container(
        width: 70,
        height: 70,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade200,
            width: isSelected ? 2 : 1,
          ),
          color: const Color(0xFFE8F1ED), // Light green background
        ),
        padding: const EdgeInsets.all(4),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: Image.network(
            option.imageUrl!,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) => const Icon(
              Icons.image,
              size: 24,
              color: Colors.grey,
            ),
          ),
        ),
      ),
    );
  }

  // Build text option button
  Widget _buildTextOption(ProductAttribute attribute,
      ProductAttributeOption option, bool isSelected) {
    return GestureDetector(
      onTap: () => _selectOption(attribute.id, option.id),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          option.name,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildBreadcrumb(BuildContext context, Product product) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Breadcrumb navigation
          Expanded(
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => context.go('/home'),
                  child: Text(
                    'Home',
                    style: AppTextStyles.caption.copyWith(
                      color: Colors.grey,
                    ),
                  ),
                ),
                const Icon(
                  Icons.chevron_right,
                  size: 16,
                  color: Colors.grey,
                ),
                GestureDetector(
                  onTap: () {},
                  child: Text(
                    product.category.name,
                    style: AppTextStyles.caption.copyWith(
                      color: Colors.grey,
                    ),
                  ),
                ),
                const Icon(
                  Icons.chevron_right,
                  size: 16,
                  color: Colors.grey,
                ),
                Expanded(
                  child: Text(
                    product.name,
                    style: AppTextStyles.caption.copyWith(
                      color: Colors.grey,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          // Share button
          GestureDetector(
            onTap: () {
              // Create the share URL with the product ID
              final shareUrl = '${Util.productShareBaseUrl}${product.id}';

              // Open the share dialog
              Share.share(
                'Check out this product: $shareUrl',
                subject: product.name,
              );
            },
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFFE8F1ED),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.share_outlined,
                color: AppColors.primary,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductImage(Product product, List<Media> allMedia) {
    // If we have a selected variation with media, make sure we're showing its first image
    if (product.selectedVariation != null &&
        product.selectedVariation!.media.isNotEmpty &&
        _selectedMediaIndex >= allMedia.length) {
      // Reset to a valid index
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _selectedMediaIndex = 0;
          });
        }
      });
    }

    // Ensure we have a valid index
    final safeIndex = allMedia.isNotEmpty
        ? (_selectedMediaIndex < allMedia.length ? _selectedMediaIndex : 0)
        : 0;

    return Column(
      children: [
        // Main image with padding
        ClipRRect(
          borderRadius: BorderRadius.all(Radius.circular(16)),
          child: Container(
            height: 250,
            width: double.infinity,
            color: const Color(0xFFE8F1ED), // Light green background
            padding: const EdgeInsets.all(16),
            child: allMedia.isNotEmpty
                ? SizedBox(
                    height: 250,
                    width: double.infinity,
                    child: Image.network(
                      allMedia[safeIndex].url,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => const Icon(
                        Icons.image,
                        size: 80,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : const Icon(
                    Icons.image,
                    size: 80,
                    color: Colors.grey,
                  ),
          ),
        ),

        // Media thumbnails row
        if (allMedia.length > 1)
          Container(
            height: 80,
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: allMedia.length,
              itemBuilder: (context, index) {
                // Check if this is the first image of the selected variation
                // (We're not using this variable yet, but it could be used to add special styling)
                // bool isVariationFirstImage = product.selectedVariation != null &&
                //     product.selectedVariation!.media.isNotEmpty &&
                //     index == 0 &&
                //     allMedia[0].url == product.selectedVariation!.media[0].url;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedMediaIndex = index;
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE8F1ED),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: index == safeIndex
                            ? AppColors.primary
                            : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: Image.network(
                        allMedia[index].url,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) =>
                            const Icon(
                          Icons.image,
                          size: 24,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildDiscountedPriceSection(
      Product product, String? discountPercentage) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Discount percentage badge
        if (discountPercentage != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              discountPercentage,
              style: AppTextStyles.caption.copyWith(
                color: Colors.green.shade700,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

        const SizedBox(height: 8),

        // Price row
        Row(
          children: [
            // Current price
            Text(
              '₹${product.price}',
              style: AppTextStyles.h3.copyWith(
                fontWeight: FontWeight.w700,
                color: Colors.red.shade700,
                fontSize: 24,
              ),
            ),
            const SizedBox(width: 8),
            // Original price with strikethrough
            Text(
              '₹${product.originalPrice}',
              style: AppTextStyles.bodyMedium.copyWith(
                decoration: TextDecoration.lineThrough,
                decorationColor: Colors.grey,
                decorationThickness: 1.5,
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRegularPriceSection(Product product) {
    return Text(
      '₹${product.price}',
      style: AppTextStyles.h3.copyWith(
        fontWeight: FontWeight.w700,
        fontSize: 24,
      ),
    );
  }

  Widget _buildAddToBagButton(BuildContext context, Product product) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () async {
          // Check if user is logged in
          final isLoggedIn = await Util.isUserLoggedIn();
          if (isLoggedIn) {
            ref.read(cartProvider.notifier).addToCart(product);
          } else if (context.mounted) {
            // Navigate to login screen
            context.pushNamed('login');
          }
        },
        icon: const Icon(
          Icons.shopping_bag_outlined,
          color: Color(0xFF1A1A1A),
        ),
        label: const Text(
          'Add to Bag',
          style: TextStyle(
            color: Color(0xFF1A1A1A),
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100),
            side: const BorderSide(color: Color(0xFF1A1A1A), width: 1),
          ),
        ),
      ),
    );
  }

  Widget _buildQuantitySelector(
      BuildContext context, Product product, int quantity) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Minus button
        InkWell(
          onTap: () async {
            // Check if user is logged in
            final isLoggedIn = await Util.isUserLoggedIn();
            if (isLoggedIn) {
              ref.read(cartProvider.notifier).updateQuantity(
                    product.id,
                    quantity - 1,
                    variationId: product.selectedVariation?.id,
                  );
            } else if (context.mounted) {
              // Navigate to login screen
              context.pushNamed('login');
            }
          },
          child: Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(color: Color(0xFFE3E9E6)),
            ),
            child: const Icon(
              Icons.remove,
              color: Colors.black,
              size: 24,
            ),
          ),
        ),

        // Quantity display
        Container(
          width: 160,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(25),
          ),
          alignment: Alignment.center,
          child: Text(
            quantity.toString(),
            style: AppTextStyles.h4,
          ),
        ),

        // Plus button
        InkWell(
          onTap: () async {
            // Check if user is logged in
            final isLoggedIn = await Util.isUserLoggedIn();
            if (isLoggedIn) {
              if (product.maxCartQuantity != null &&
                  quantity >= product.maxCartQuantity!) {
                Util.showErrorToast(
                    'Maximum ${product.maxCartQuantity} items allowed in cart for this product');
                return;
              }
              ref.read(cartProvider.notifier).updateQuantity(
                    product.id,
                    quantity + 1,
                    variationId: product.selectedVariation?.id,
                  );
            } else if (context.mounted) {
              // Navigate to login screen
              context.pushNamed('login');
            }
          },
          child: Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(color: Color(0xFFE3E9E6)),
            ),
            child: const Icon(
              Icons.add,
              color: Colors.black,
              size: 24,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOutOfStockButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: null, // Disabled button
        icon: const Icon(
          Icons.shopping_bag_outlined,
          color: Colors.red,
        ),
        label: const Text(
          'Out of Stock',
          style: TextStyle(
            color: Colors.red,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100),
            side: const BorderSide(color: Colors.red, width: 1),
          ),
        ),
      ),
    );
  }

  Widget _buildHighlightsSection(Product product) {
    if (product.highlights == null || product.highlights!.isEmpty) {
      return const SizedBox();
    }

    return Builder(
      builder: (context) => Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          // No border to avoid the black line
        ),
        child: Theme(
          data: Theme.of(context).copyWith(
            dividerColor: Colors.transparent, // Remove the divider line
          ),
          child: ExpansionTile(
            title: Text(
              'Highlights',
              style: AppTextStyles.h4,
            ),
            initiallyExpanded: true,
            tilePadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            childrenPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            children: [
              Column(
                children: product.highlights!.entries.map((entry) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 120,
                          child: Text(
                            entry.key,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            entry.value.toString(),
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInformationSection(Product product) {
    if (product.information == null || product.information!.isEmpty) {
      return const SizedBox();
    }

    return Builder(
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          // No border to avoid the black line
        ),
        child: Theme(
          data: Theme.of(context).copyWith(
            dividerColor: Colors.transparent, // Remove the divider line
          ),
          child: ExpansionTile(
            title: Text(
              'Information',
              style: AppTextStyles.h4,
            ),
            initiallyExpanded: false,
            tilePadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            childrenPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            children: [
              Column(
                children: product.information!.entries.map((entry) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 120,
                          child: Text(
                            entry.key,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            entry.value.toString(),
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRelatedProductsSection() {
    final relatedProductsState = ref.watch(relatedProductsProvider);

    // Don't show the section if there are no related products and not loading
    if (relatedProductsState.products.isEmpty &&
        !relatedProductsState.isLoading &&
        relatedProductsState.error == null) {
      return const SizedBox();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Align(
          alignment: Alignment.center,
          child: Text(
            'Related Products',
            style: AppTextStyles.h2.copyWith(
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 16),

        // Loading state
        if (relatedProductsState.isLoading)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32.0),
              child: CircularProgressIndicator(),
            ),
          )
        // Error state
        else if (relatedProductsState.error != null)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Failed to load related products',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ),
          )
        // Products grid
        else if (relatedProductsState.products.isNotEmpty)
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.48, // Same as used in category screens
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: relatedProductsState.products.length,
            itemBuilder: (context, index) {
              final product = relatedProductsState.products[index];
              return ProductCard(
                product: product,
                onTap: () {
                  context.goNamed(
                    'product-details',
                    pathParameters: {
                      'productId': product.id.toString(),
                      'slug': product.slug!
                    },
                  );
                },
              );
            },
          ),
      ],
    );
  }
}
