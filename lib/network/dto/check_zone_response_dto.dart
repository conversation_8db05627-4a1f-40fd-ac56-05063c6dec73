// To parse this JSON data, do
//
//     final checkZoneResponse = checkZoneResponseFromJson(jsonString);

import 'dart:convert';

CheckZoneResponseDto checkZoneResponseFromJson(String str) =>
    CheckZoneResponseDto.fromJson(json.decode(str));

String checkZoneResponseToJson(CheckZoneResponseDto data) =>
    json.encode(data.toJson());

/// Response DTO for the zone check endpoint
class CheckZoneResponseDto {
  final bool withinZone;

  CheckZoneResponseDto({
    required this.withinZone,
  });

  CheckZoneResponseDto copyWith({
    bool? withinZone,
  }) =>
      CheckZoneResponseDto(
        withinZone: withinZone ?? this.withinZone,
      );

  factory CheckZoneResponseDto.fromJson(Map<String, dynamic> json) =>
      CheckZoneResponseDto(
        withinZone: json['withinZone'],
      );

  Map<String, dynamic> toJson() => {
        'withinZone': withinZone,
      };

  @override
  String toString() => 'CheckZoneResponseDto(withinZone: $withinZone)';
}
