// To parse this JSON data, do
//
//     final errorResponseDto = errorResponseDtoFromJson(jsonString);

import 'dart:convert';

ErrorResponseDto errorResponseDtoFromJson(String str) =>
    ErrorResponseDto.fromJson(json.decode(str));

String errorResponseDtoToJson(ErrorResponseDto data) => json.encode(data.toJson());

class ErrorResponseDto {
  final int statusCode;
  final String message;
  final List<ErrorDetail> errors;

  ErrorResponseDto({
    required this.statusCode,
    required this.message,
    required this.errors,
  });

  ErrorResponseDto copyWith({
    int? statusCode,
    String? message,
    List<ErrorDetail>? errors,
  }) =>
      ErrorResponseDto(
        statusCode: statusCode ?? this.statusCode,
        message: message ?? this.message,
        errors: errors ?? this.errors,
      );

  factory ErrorResponseDto.fromJson(Map<String, dynamic> json) => ErrorResponseDto(
        statusCode: json["statusCode"],
        message: json["message"],
        errors: json["errors"] != null
            ? List<ErrorDetail>.from(
                json["errors"].map((x) => ErrorDetail.fromJson(x)))
            : [],
      );

  Map<String, dynamic> toJson() => {
        "statusCode": statusCode,
        "message": message,
        "errors": List<dynamic>.from(errors.map((x) => x.toJson())),
      };
}

class ErrorDetail {
  final String code;
  final String message;
  final List<String> path;

  ErrorDetail({
    required this.code,
    required this.message,
    required this.path,
  });

  ErrorDetail copyWith({
    String? code,
    String? message,
    List<String>? path,
  }) =>
      ErrorDetail(
        code: code ?? this.code,
        message: message ?? this.message,
        path: path ?? this.path,
      );

  factory ErrorDetail.fromJson(Map<String, dynamic> json) => ErrorDetail(
        code: json["code"],
        message: json["message"],
        path: List<String>.from(json["path"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "message": message,
        "path": List<dynamic>.from(path.map((x) => x)),
      };
}
