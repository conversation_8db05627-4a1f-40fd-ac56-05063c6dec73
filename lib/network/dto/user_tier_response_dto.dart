// To parse this JSON data, do
//
//     final userTierResponseDto = userTierResponseDtoFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/reward_tier.dart';

UserTierResponseDto userTierResponseDtoFromJson(String str) =>
    UserTierResponseDto.fromJson(json.decode(str));

String userTierResponseDtoToJson(UserTierResponseDto data) =>
    json.encode(data.toJson());

class UserTierResponseDto {
  final RewardTier? rewardTier;
  final DateTime? tierExpiresAt;

  UserTierResponseDto({
    this.rewardTier,
    this.tierExpiresAt,
  });

  UserTierResponseDto copyWith({
    RewardTier? rewardTier,
    DateTime? tierExpiresAt,
  }) =>
      UserTierResponseDto(
        rewardTier: rewardTier ?? this.rewardTier,
        tierExpiresAt: tierExpiresAt ?? this.tierExpiresAt,
      );

  factory UserTierResponseDto.fromJson(Map<String, dynamic> json) =>
      UserTierResponseDto(
        rewardTier: json['rewardTier'] != null
            ? RewardTier.fromJson(json['rewardTier'])
            : null,
        tierExpiresAt: json['tierExpiresAt'] != null
            ? DateTime.parse(json['tierExpiresAt'])
            : null,
      );

  Map<String, dynamic> toJson() => {
        'rewardTier': rewardTier?.toJson(),
        'tierExpiresAt': tierExpiresAt?.toIso8601String(),
      };
}
