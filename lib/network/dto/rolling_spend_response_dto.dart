// To parse this JSON data, do
//
//     final rollingSpendResponseDto = rollingSpendResponseDtoFromJson(jsonString);

import 'dart:convert';

RollingSpendResponseDto rollingSpendResponseDtoFromJson(String str) =>
    RollingSpendResponseDto.fromJson(json.decode(str));

String rollingSpendResponseDtoToJson(RollingSpendResponseDto data) =>
    json.encode(data.toJson());

class RollingSpendResponseDto {
  final double spending;

  RollingSpendResponseDto({
    required this.spending,
  });

  RollingSpendResponseDto copyWith({
    double? spending,
  }) =>
      RollingSpendResponseDto(
        spending: spending ?? this.spending,
      );

  factory RollingSpendResponseDto.fromJson(Map<String, dynamic> json) =>
      RollingSpendResponseDto(
        spending: json['spending'] is int
            ? (json['spending'] as int).toDouble()
            : json['spending'],
      );

  Map<String, dynamic> toJson() => {
        'spending': spending,
      };
}
