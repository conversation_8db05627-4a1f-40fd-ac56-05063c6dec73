// To parse this JSON data, do
//
//     final expiringPointsResponseDto = expiringPointsResponseDtoFromJson(jsonString);

import 'dart:convert';

ExpiringPointsResponseDto expiringPointsResponseDtoFromJson(String str) =>
    ExpiringPointsResponseDto.fromJson(json.decode(str));

String expiringPointsResponseDtoToJson(ExpiringPointsResponseDto data) =>
    json.encode(data.toJson());

class ExpiringPointsResponseDto {
  final DateTime? expiryDate;
  final double expiringPoints;

  ExpiringPointsResponseDto({
    this.expiryDate,
    required this.expiringPoints,
  });

  ExpiringPointsResponseDto copyWith({
    DateTime? expiryDate,
    double? expiringPoints,
  }) =>
      ExpiringPointsResponseDto(
        expiryDate: expiryDate ?? this.expiryDate,
        expiringPoints: expiringPoints ?? this.expiringPoints,
      );

  factory ExpiringPointsResponseDto.fromJson(Map<String, dynamic> json) =>
      ExpiringPointsResponseDto(
        expiryDate: json['expiryDate'] != null
            ? DateTime.parse(json['expiryDate'])
            : null,
        expiringPoints: json['expiringPoints'] is int
            ? (json['expiringPoints'] as int).toDouble()
            : json['expiringPoints'],
      );

  Map<String, dynamic> toJson() => {
        'expiryDate': expiryDate?.toIso8601String(),
        'expiringPoints': expiringPoints,
      };
}
