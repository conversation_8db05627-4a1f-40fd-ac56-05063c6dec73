import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:vegmove_ecommerce/model/algolia_product.dart';

SearchResponseDto searchResponseFromJson(String str) =>
    SearchResponseDto.fromJson(json.decode(str));

String searchResponseToJson(SearchResponseDto data) =>
    json.encode(data.toJson());

class SearchResponseDto {
  final List<AlgoliaProduct> hits;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;
  final int processingTimeMS;

  SearchResponseDto({
    required this.hits,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.processingTimeMS,
  });

  SearchResponseDto copyWith({
    List<AlgoliaProduct>? hits,
    int? total,
    int? page,
    int? pageSize,
    int? totalPages,
    int? processingTimeMS,
  }) =>
      SearchResponseDto(
        hits: hits ?? this.hits,
        total: total ?? this.total,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize,
        totalPages: totalPages ?? this.totalPages,
        processingTimeMS: processingTimeMS ?? this.processingTimeMS,
      );

  factory SearchResponseDto.fromJson(Map<String, dynamic> json) {
    try {
      debugPrint('SearchResponseDto.fromJson: ${json.keys}');

      // Validate required fields
      if (json["hits"] == null) {
        debugPrint('Error: "hits" field is null in response');
        throw FormatException('Missing "hits" field in response');
      }

      if (!(json["hits"] is List)) {
        debugPrint(
            'Error: "hits" field is not a List, it is: ${json["hits"].runtimeType}');
        throw FormatException('Invalid "hits" field type in response');
      }

      return SearchResponseDto(
        hits: List<AlgoliaProduct>.from(
            json["hits"].map((x) => AlgoliaProduct.fromJson(x))),
        total: json["total"] ?? 0,
        page: json["page"] ?? 0,
        pageSize: json["pageSize"] ?? 0,
        totalPages: json["totalPages"] ?? 0,
        processingTimeMS: json["processingTimeMS"] ?? 0,
      );
    } catch (e, stackTrace) {
      debugPrint('Error parsing SearchResponseDto: $e');
      debugPrint('Stack trace: $stackTrace');
      debugPrint('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => {
        "hits": List<dynamic>.from(hits.map((x) => x.toJson())),
        "total": total,
        "page": page,
        "pageSize": pageSize,
        "totalPages": totalPages,
        "processingTimeMS": processingTimeMS,
      };
}
