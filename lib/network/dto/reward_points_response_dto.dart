// To parse this JSON data, do
//
//     final rewardPointsResponseDto = rewardPointsResponseDtoFromJson(jsonString);

import 'dart:convert';

RewardPointsResponseDto rewardPointsResponseDtoFromJson(String str) =>
    RewardPointsResponseDto.fromJson(json.decode(str));

String rewardPointsResponseDtoToJson(RewardPointsResponseDto data) =>
    json.encode(data.toJson());

class RewardPointsResponseDto {
  final double points;

  RewardPointsResponseDto({
    required this.points,
  });

  RewardPointsResponseDto copyWith({
    double? points,
  }) =>
      RewardPointsResponseDto(
        points: points ?? this.points,
      );

  factory RewardPointsResponseDto.fromJson(Map<String, dynamic> json) =>
      RewardPointsResponseDto(
        points: json['points'] is int
            ? (json['points'] as int).toDouble()
            : json['points'],
      );

  Map<String, dynamic> toJson() => {
        'points': points,
      };
}
