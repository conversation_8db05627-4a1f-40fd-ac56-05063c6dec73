// To parse this JSON data, do
//
//     final productPaginatedResponse = productPaginatedResponseFromJson(jsonString);

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:vegmove_ecommerce/model/product.dart';

ProductPaginatedResponseDto productPaginatedResponseFromJson(String str) =>
    ProductPaginatedResponseDto.fromJson(json.decode(str));

String productPaginatedResponseToJson(ProductPaginatedResponseDto data) =>
    json.encode(data.toJson());

class ProductPaginatedResponseDto {
  final List<Product> data;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  ProductPaginatedResponseDto({
    required this.data,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  ProductPaginatedResponseDto copyWith({
    List<Product>? data,
    int? total,
    int? page,
    int? pageSize,
    int? totalPages,
  }) =>
      ProductPaginatedResponseDto(
        data: data ?? this.data,
        total: total ?? this.total,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize,
        totalPages: totalPages ?? this.totalPages,
      );

  factory ProductPaginatedResponseDto.fromJson(Map<String, dynamic> json) {
    try {
      debugPrint('ProductPaginatedResponseDto.fromJson: ${json.keys}');

      // Validate required fields
      if (json["data"] == null) {
        debugPrint('Error: "data" field is null in response');
        throw FormatException('Missing "data" field in response');
      }

      if (!(json["data"] is List)) {
        debugPrint(
            'Error: "data" field is not a List, it is: ${json["data"].runtimeType}');
        throw FormatException('Invalid "data" field type in response');
      }

      return ProductPaginatedResponseDto(
        data: List<Product>.from(json["data"].map((x) => Product.fromJson(x))),
        total: json["total"],
        page: json["page"],
        pageSize: json["pageSize"],
        totalPages: json["totalPages"],
      );
    } catch (e, stackTrace) {
      debugPrint('Error parsing ProductPaginatedResponseDto: $e');
      debugPrint('Stack trace: $stackTrace');
      debugPrint('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => {
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
        "total": total,
        "page": page,
        "pageSize": pageSize,
        "totalPages": totalPages,
      };
}
