// To parse this JSON data, do
//
//     final authResponseDto = authResponseDtoFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/user.dart';

AuthResponseDto authResponseDtoFromJson(String str) =>
    AuthResponseDto.fromJson(json.decode(str));

String authResponseDtoToJson(AuthResponseDto data) =>
    json.encode(data.toJson());

class AuthResponseDto {
  final String token;
  final User? user;

  AuthResponseDto({
    required this.token,
    this.user,
  });

  AuthResponseDto copyWith({
    String? token,
    User? user,
  }) =>
      AuthResponseDto(
        token: token ?? this.token,
        user: user ?? this.user,
      );

  factory AuthResponseDto.fromJson(Map<String, dynamic> json) =>
      AuthResponseDto(
        token: json['token'],
        user: json['user'] != null ? User.fromJson(json['user']) : null,
      );

  Map<String, dynamic> toJson() => {
        'token': token,
        'user': user?.toJson(),
      };
}
