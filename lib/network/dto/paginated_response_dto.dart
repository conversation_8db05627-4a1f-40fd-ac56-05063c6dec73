// To parse this JSON data, do
//
//     final paginatedResponse = paginatedResponseFromJson(jsonString);

/// Generic paginated response DTO that can be used with any data type
class PaginatedResponseDto<T> {
  final List<T> data;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  PaginatedResponseDto({
    required this.data,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  PaginatedResponseDto<T> copyWith({
    List<T>? data,
    int? total,
    int? page,
    int? pageSize,
    int? totalPages,
  }) =>
      PaginatedResponseDto<T>(
        data: data ?? this.data,
        total: total ?? this.total,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize,
        totalPages: totalPages ?? this.totalPages,
      );

  factory PaginatedResponseDto.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) =>
      PaginatedResponseDto<T>(
        data: List<T>.from(json["data"].map((x) => fromJsonT(x))),
        total: json["total"],
        page: json["page"],
        pageSize: json["pageSize"],
        totalPages: json["totalPages"],
      );

  Map<String, dynamic> toJson(Object? Function(T) toJsonT) => {
        "data": List<dynamic>.from(data.map((x) => toJsonT(x))),
        "total": total,
        "page": page,
        "pageSize": pageSize,
        "totalPages": totalPages,
      };
}
