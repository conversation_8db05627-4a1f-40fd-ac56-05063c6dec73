// To parse this JSON data, do
//
//     final orderPaginatedResponse = orderPaginatedResponseFromJson(jsonString);

import 'dart:convert';
import 'package:vegmove_ecommerce/model/order.dart';

OrderPaginatedResponseDto orderPaginatedResponseFromJson(String str) =>
    OrderPaginatedResponseDto.fromJson(json.decode(str));

String orderPaginatedResponseToJson(OrderPaginatedResponseDto data) =>
    json.encode(data.toJson());

class OrderPaginatedResponseDto {
  final List<Order> data;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  OrderPaginatedResponseDto({
    required this.data,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  OrderPaginatedResponseDto copyWith({
    List<Order>? data,
    int? total,
    int? page,
    int? pageSize,
    int? totalPages,
  }) =>
      OrderPaginatedResponseDto(
        data: data ?? this.data,
        total: total ?? this.total,
        page: page ?? this.page,
        pageSize: pageSize ?? this.pageSize,
        totalPages: totalPages ?? this.totalPages,
      );

  factory OrderPaginatedResponseDto.fromJson(Map<String, dynamic> json) =>
      OrderPaginatedResponseDto(
        data: List<Order>.from(json['data'].map((x) => Order.fromJson(x))),
        total: json['total'],
        page: json['page'],
        pageSize: json['pageSize'],
        totalPages: json['totalPages'],
      );

  Map<String, dynamic> toJson() => {
        'data': List<dynamic>.from(data.map((x) => x.toJson())),
        'total': total,
        'page': page,
        'pageSize': pageSize,
        'totalPages': totalPages,
      };
}
