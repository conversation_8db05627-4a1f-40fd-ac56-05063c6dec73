import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vegmove_ecommerce/network/dto/error_response_dto.dart';
import 'package:vegmove_ecommerce/network/api_exception.dart';

class ApiClient {
  static final ApiClient _instance = ApiClient._internal();
  late final Dio _dio;
  late SharedPreferences _sharedPreferences;

  factory ApiClient() {
    return _instance;
  }

  ApiClient._internal() {
    SharedPreferences.getInstance().then((value) {
      _sharedPreferences = value;
    });
    var baseUrl = '';

    if (Platform.isAndroid) {
      baseUrl = 'http://10.0.2.2:3000/';
    } else {
      baseUrl = 'http://127.0.0.1:3000/';
    }

    baseUrl = 'https://vegmove-backend.shq.su/';

    final options = BaseOptions(
      baseUrl: baseUrl,
      responseType: ResponseType.plain,
      validateStatus: (status) {
        return true; // All status codes are considered valid.
      },
    );
    _dio = Dio(options);
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          final token = _sharedPreferences.getString('token');
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }

          return handler.next(options);
        },
      ),
    );

    _dio.interceptors.add(LogInterceptor(
      responseBody: true,
      requestBody: true,
    ));
  }

  Future<T> _request<T>(
    String method,
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    dynamic data,
    required T Function(dynamic json) parser,
  }) async {
    try {
      final response = await _dio.request<String>(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: Options(method: method),
      );

      if (response.data == null) {
        throw Exception('Empty response');
      }

      final jsonBody = jsonDecode(response.data!);
      final status = response.statusCode ?? 0;

      if (status < 200 || status >= 300) {
        // parse error response
        final err = ErrorResponseDto.fromJson(jsonBody as Map<String, dynamic>);

        // Check for invalid token error
        if (err.statusCode == 400 && err.message == 'Invalid token provided.') {
          // Remove the token from shared preferences
          debugPrint(
              'Invalid token detected. Removing token from shared preferences.');
          await _sharedPreferences.remove('token');
        }

        throw ApiException(err);
      }

      return parser(jsonBody);
    } catch (e) {
      log('API Error: $e');
      rethrow;
    }
  }

  Future<T> get<T>(
    String endpoint, {
    Map<String, dynamic>? queries,
    required T Function(dynamic json) parser,
  }) =>
      _request<T>(
        'GET',
        endpoint,
        queryParameters: queries,
        parser: parser,
      );

  Future<T> post<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    FormData? formData,
    Map<String, dynamic>? queries,
    required T Function(dynamic json) parser,
  }) =>
      _request<T>(
        'POST',
        endpoint,
        data: data ?? formData,
        queryParameters: queries,
        parser: parser,
      );

  Future<T> patch<T>(
    String endpoint, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? queries,
    required T Function(dynamic json) parser,
  }) =>
      _request<T>(
        'PATCH',
        endpoint,
        data: data,
        queryParameters: queries,
        parser: parser,
      );

  Future<T> delete<T>(
    String endpoint, {
    Map<String, dynamic>? queries,
    Map<String, dynamic>? data,
    required T Function(dynamic json) parser,
  }) =>
      _request<T>(
        'DELETE',
        endpoint,
        queryParameters: queries,
        data: data,
        parser: parser,
      );
}
