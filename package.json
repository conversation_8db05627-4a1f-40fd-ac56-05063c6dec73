{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "algolia:seed": "tsx ./prisma/seed-algolia.ts", "dev-email": "email dev"}, "prisma": {"seed": "tsx ./prisma/seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.806.0", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/platform-express": "^11.1.0", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@prisma/client": "^6.0.1", "@react-email/components": "^0.0.41", "@react-email/img": "0.0.11", "@react-email/render": "^1.1.2", "@react-pdf/renderer": "^4.3.0", "algoliasearch": "^5.25.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "better-auth": "^1.2.7", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "exceljs": "^4.4.0", "firebase-admin": "^13.3.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "multer": "^1.4.5-lts.2", "nestjs-zod": "^4.3.1", "node-geometry-library": "^1.2.6", "pdfkit": "^0.17.0", "react": "19.1.0", "react-dom": "19.1.0", "reflect-metadata": "^0.2.0", "resend": "^4.5.1", "rxjs": "^7.8.1", "uuid": "^11.1.0", "zod": "^3.24.0"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.0", "@types/algoliasearch": "^3.34.11", "@types/exceljs": "^0.5.3", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.17.9", "@types/react": "^19.1.0", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^6.0.1", "react-email": "4.0.15", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.2", "typescript": "^5.7.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=20.18.0"}}