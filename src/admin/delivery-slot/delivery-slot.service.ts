import { Injectable } from '@nestjs/common';
import { DeliverySlot, Weekday } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
import moment from 'moment';
import { CreateHolidayDto, CreateSlotDto } from './delivery-slot.schema';

@Injectable()
export class DeliverySlotService {
  constructor(private prisma: PrismaService) {}

  async getSlotsForDate(date: Date): Promise<DeliverySlot[]> {
    // 1. Check for holiday
    const isHoliday = await this.prisma.deliverySlot.findFirst({
      where: {
        type: 'HOLIDAY',
        date: {
          gte: moment(date).startOf('day').toDate(),
          lt: moment(date).endOf('day').toDate(),
        },
      },
    });

    if (isHoliday) return [];

    // 2. Date-specific slots
    const dateSlots = await this.prisma.deliverySlot.findMany({
      where: {
        type: 'REGULAR',
        date: {
          gte: moment(date).startOf('day').toDate(),
          lt: moment(date).endOf('day').toDate(),
        },
      },
      orderBy: { startTime: 'asc' },
    });

    if (dateSlots.length > 0) return this.filterExpiredIfToday(date, dateSlots);

    // 3. Weekday slots
    const weekday = date.getUTCDay();
    const weekdayEnum = Object.values(Weekday)[weekday];

    const weekdaySlots = await this.prisma.deliverySlot.findMany({
      where: {
        type: 'REGULAR',
        weekday: weekdayEnum,
      },
      orderBy: { startTime: 'asc' },
    });

    if (weekdaySlots.length > 0)
      return this.filterExpiredIfToday(date, weekdaySlots);

    // 4. Global default slots
    const defaultSlots = await this.prisma.deliverySlot.findMany({
      where: {
        type: 'REGULAR',
        date: null,
        weekday: null,
      },
      orderBy: { startTime: 'asc' },
    });

    return this.filterExpiredIfToday(date, defaultSlots);
  }

  private filterExpiredIfToday(
    date: Date,
    slots: DeliverySlot[],
  ): DeliverySlot[] {
    const now = new Date();
    if (now.toDateString() !== date.toDateString()) return slots;

    const currentTime = now.toTimeString().slice(0, 5); // "HH:mm"
    return slots.filter((s) => s.startTime && s.startTime > currentTime);
  }

  async createSlot(createSlotDto: CreateSlotDto) {
    return this.prisma.deliverySlot.create({
      data: {
        type: 'REGULAR',
        startTime: createSlotDto.startTime,
        endTime: createSlotDto.endTime,
        date: createSlotDto.date
          ? moment(createSlotDto.date).startOf('day').toDate()
          : undefined,
        weekday: createSlotDto.weekday ?? null,
      },
    });
  }

  async createHoliday(createHolidayDto: CreateHolidayDto) {
    return this.prisma.deliverySlot.create({
      data: {
        type: 'HOLIDAY',
        date: moment(createHolidayDto.date).startOf('day').toDate(),
        reason: createHolidayDto.reason,
      },
    });
  }

  async deleteSlot(id: number) {
    return this.prisma.deliverySlot.delete({ where: { id } });
  }

  async getAllSlots() {
    return this.prisma.deliverySlot.findMany({
      orderBy: [{ date: 'asc' }, { weekday: 'asc' }, { startTime: 'asc' }],
    });
  }

  async getWeekdaySlots(weekday: Weekday) {
    return this.prisma.deliverySlot.findMany({
      where: {
        type: 'REGULAR',
        weekday,
      },
      orderBy: { startTime: 'asc' },
    });
  }

  async getRawDateSlots(date: Date) {
    return this.prisma.deliverySlot.findMany({
      where: {
        type: 'REGULAR',
        date: {
          gte: moment(date).startOf('day').toDate(),
          lt: moment(date).endOf('day').toDate(),
        },
      },
      orderBy: { startTime: 'asc' },
    });
  }

  async getHoliday(date: Date) {
    return this.prisma.deliverySlot.findFirst({
      where: {
        type: 'HOLIDAY',
        date: {
          gte: moment(date).startOf('day').toDate(),
          lt: moment(date).endOf('day').toDate(),
        },
      },
    });
  }
}
