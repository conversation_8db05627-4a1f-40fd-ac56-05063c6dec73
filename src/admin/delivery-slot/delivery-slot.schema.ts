import { Weekday } from '@prisma/client';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;

const createSlotSchema = z.object({
  startTime: z.string().refine((value) => timeRegex.test(value), {
    message: 'startTime must be in HH:mm format (00:00 - 23:59)',
  }),
  endTime: z.string().refine((value) => timeRegex.test(value), {
    message: 'endTime must be in HH:mm format (00:00 - 23:59)',
  }),
  date: z.coerce.date().optional().nullable(),
  weekday: z.nativeEnum(Weekday).optional().nullable(),
});

export class CreateSlotDto extends createZodDto(createSlotSchema) {}

const createHolidaySchema = z.object({
  date: z.coerce.date(),
  reason: z.string().optional(),
});

export class CreateHolidayDto extends createZodDto(createHolidaySchema) {}
