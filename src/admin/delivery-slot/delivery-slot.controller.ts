import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UsePipes,
} from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { CreateHolidayDto, CreateSlotDto } from './delivery-slot.schema';
import { DeliverySlotService } from './delivery-slot.service';
import { Weekday } from '@prisma/client';

@UsePipes(ZodValidationPipe)
@Controller('delivery-slots')
export class DeliverySlotController {
  constructor(private readonly slotService: DeliverySlotService) {}

  @Get()
  async getSlots(@Query('date') dateStr: string) {
    const date = new Date(dateStr);
    return this.slotService.getSlotsForDate(date);
  }

  @Post('slot')
  async createSlot(@Body() createSlotDto: CreateSlotDto) {
    return this.slotService.createSlot(createSlotDto);
  }

  @Post('holiday')
  async createHoliday(@Body() createHolidayDto: CreateHolidayDto) {
    return this.slotService.createHoliday(createHolidayDto);
  }

  @Delete(':id')
  async deleteSlot(@Param('id', ParseIntPipe) id: number) {
    return this.slotService.deleteSlot(id);
  }

  @Get('all')
  async getAll() {
    return this.slotService.getAllSlots();
  }

  @Get('weekday')
  async getWeekdaySlots(@Query('weekday') weekday: Weekday) {
    return this.slotService.getWeekdaySlots(weekday);
  }

  @Get('date')
  async getDateSlots(@Query('date') dateStr: string) {
    const date = new Date(dateStr);
    return this.slotService.getRawDateSlots(date);
  }

  @Get('holiday')
  async isHoliday(@Query('date') dateStr: string) {
    const date = new Date(dateStr);
    const result = await this.slotService.getHoliday(date);
    console.log(result);
    return result
      ? { isHoliday: true, reason: result.reason }
      : { isHoliday: false, reason: null };
  }
}
