import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class AnalyticsService {
  constructor(private prisma: PrismaService) {}

  // Returns a summary of key metrics
  async getSummary() {
    const totalUsers = await this.prisma.user.count();
    const totalOrders = await this.prisma.order.count();
    const orderAmountSum = await this.prisma.order.aggregate({
      _sum: { totalAmount: true },
    });
    const totalWarehouses = await this.prisma.warehouse.count();
    const totalWarehouseStaffs = await this.prisma.warehouseStaff.count();
    // Count delivery drivers based on the enum in User.type
    const totalDeliveryDrivers = await this.prisma.user.count({
      where: { type: 'DELIVERY_PERSON' },
    });
    const totalCompletedOrders = await this.prisma.order.count({
      where: { status: 'DELIVERED' },
    });
    const totalPendingOrders = await this.prisma.order.count({
      where: { status: 'PENDING' },
    });

    return {
      totalUsers,
      totalOrders,
      totalOrderAmount: orderAmountSum._sum.totalAmount || 0,
      totalWarehouses,
      totalWarehouseStaffs,
      totalDeliveryDrivers,
      totalCompletedOrders,
      totalPendingOrders,
    };
  }

  // Returns customer signup counts grouped by date in the given range
  async getCustomerSignups(startDate: Date, endDate: Date) {
    const users = await this.prisma.user.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: { createdAt: true },
    });
    const grouped = {};
    users.forEach((user) => {
      const dateKey = user.createdAt.toISOString().split('T')[0];
      grouped[dateKey] = (grouped[dateKey] || 0) + 1;
    });

    return Object.entries(grouped)
      .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
      .map(([date, count]) => ({ date, count }));
  }

  // Returns order counts grouped by date in the given range
  async getOrdersData(startDate: Date, endDate: Date) {
    const orders = await this.prisma.order.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: { createdAt: true },
    });
    const grouped = {};
    orders.forEach((order) => {
      const dateKey = order.createdAt.toISOString().split('T')[0];
      grouped[dateKey] = (grouped[dateKey] || 0) + 1;
    });

    return Object.entries(grouped)
      .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
      .map(([date, count]) => ({ date, count }));
  }

  // Returns stock change data based on inventory transactions grouped by date
  async getStocksData(startDate: Date, endDate: Date) {
    const transactions = await this.prisma.inventoryTransaction.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      select: { createdAt: true, quantity: true, type: true },
    });
    const grouped = {};
    transactions.forEach((tx) => {
      const dateKey = tx.createdAt.toISOString().split('T')[0];
      // Adjust quantity based on transaction type
      const adjustedQuantity =
        tx.type === 'SALE' || tx.type === 'RETURN' ? -tx.quantity : tx.quantity;

      grouped[dateKey] = (grouped[dateKey] || 0) + adjustedQuantity;
    });

    return Object.entries(grouped)
      .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
      .map(([date, count]) => ({ date, count }));
  }

  // Helper method to group items by date
  private groupByDate(items: { createdAt: Date }[]) {
    const grouped = {};
    items.forEach((item) => {
      const dateKey = item.createdAt.toISOString().split('T')[0];
      grouped[dateKey] = (grouped[dateKey] || 0) + 1;
    });

    return Object.entries(grouped)
      .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
      .map(([date, count]) => ({ date, count: Number(count) }));
  }

  // Get top selling products
  async getTopSellingProducts(limit: number = 10) {
    const productSales = await this.prisma.orderItem.groupBy({
      by: ['productId'],
      _sum: {
        quantity: true,
      },
      orderBy: {
        _sum: {
          quantity: 'desc',
        },
      },
      take: limit,
    });

    // Get product details for each top selling product
    const topProducts = await Promise.all(
      productSales.map(async (item) => {
        const product = await this.prisma.product.findUniqueOrThrow({
          where: { id: item.productId },
          select: { name: true, id: true },
        });

        return {
          id: product.id,
          name: product.name,
          totalSold: item._sum.quantity,
        };
      }),
    );

    return topProducts;
  }

  // Get revenue by category
  async getRevenueByCategory() {
    // Get all order items with their products and categories
    const orderItems = await this.prisma.orderItem.findMany({
      select: {
        price: true,
        quantity: true,
        product: {
          select: {
            categoryId: true,
          },
        },
      },
    });

    // Group revenue by category
    const revenueByCategory = {};
    for (const item of orderItems) {
      const categoryId = item.product.categoryId;
      const revenue = Number(item.price) * item.quantity;

      if (!revenueByCategory[categoryId]) {
        revenueByCategory[categoryId] = 0;
      }

      revenueByCategory[categoryId] += revenue;
    }

    // Get category names and map to the revenue data
    const categoryRevenue = await Promise.all(
      Object.entries(revenueByCategory).map(async ([categoryId, revenue]) => {
        const category = await this.prisma.category.findUniqueOrThrow({
          where: { id: Number(categoryId) },
          select: { name: true },
        });

        return {
          categoryId: Number(categoryId),
          categoryName: category.name,
          revenue: Number(revenue),
        };
      }),
    );

    return categoryRevenue.sort((a, b) => b.revenue - a.revenue);
  }
}
