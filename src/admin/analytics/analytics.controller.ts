// analytics.controller.ts
import {
  Controller,
  DefaultValuePipe,
  Get,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import { AnalyticsService } from './analytics.service';

@Controller('analytics')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  // Summary endpoint
  @Get('summary')
  async getSummary() {
    return await this.analyticsService.getSummary();
  }

  // Endpoint for chart data of customer signups
  @Get('customers')
  async getCustomerSignups(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return await this.analyticsService.getCustomerSignups(
      new Date(startDate),
      new Date(endDate),
    );
  }

  // Endpoint for chart data of orders
  @Get('orders')
  async getOrdersData(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return await this.analyticsService.getOrdersData(
      new Date(startDate),
      new Date(endDate),
    );
  }

  // Endpoint for chart data of stock changes (based on inventory transactions)
  @Get('stocks')
  async getStocksData(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return await this.analyticsService.getStocksData(
      new Date(startDate),
      new Date(endDate),
    );
  }

  // Endpoint for top selling products
  @Get('top-products')
  async getTopSellingProducts(
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return await this.analyticsService.getTopSellingProducts(limit);
  }

  // Endpoint for revenue by category
  @Get('category-revenue')
  async getRevenueByCategory() {
    return await this.analyticsService.getRevenueByCategory();
  }
}
