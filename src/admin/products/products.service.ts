import { HttpStatus, Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { AlgoliaService } from 'src/algolia/algolia.service';
import { Exception } from 'src/exceptions';
import { PrismaService } from 'src/prisma/prisma.service';
import { ProductVariationResponse } from 'src/types';
import { NotificationService } from '../notification/notification.service';
import {
  CreateProductAttributeDto,
  CreateProductAttributeOptionDto,
  CreateProductDto,
  CreateProductTranslationDto,
  EditProductDto,
  GenerateProductVariationsDto,
  UpdateProductTranslationDto,
  UpdateProductVariationDto,
} from './products.schema';

@Injectable()
export class ProductsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly notificationService: NotificationService,
    private readonly algoliaService: AlgoliaService,
  ) {}

  async getProducts({
    page,
    pageSize,
    search,
  }: {
    page: number;
    pageSize: number;
    search?: string;
  }) {
    const [products, total] = await this.prisma.$transaction(async (tx) => {
      const where: Prisma.ProductWhereInput = search
        ? {
            OR: [
              { name: { contains: search, mode: 'insensitive' } },
              { barcode: { contains: search, mode: 'insensitive' } },
              { description: { contains: search, mode: 'insensitive' } },
            ],
          }
        : {};

      const products = await tx.product.findMany({
        where,
        include: {
          media: true,
          thumbnail: true,
          category: true,
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      });

      const total = await tx.product.count({ where });

      return [products, total];
    });

    return {
      data: products,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async addProduct(createProductDto: CreateProductDto) {
    try {
      const existingBarcodeProduct = await this.prisma.product.findUnique({
        where: {
          barcode: createProductDto.barcode,
        },
      });

      if (existingBarcodeProduct) {
        throw new Exception('Barcode exists', HttpStatus.CONFLICT);
      }

      const product = await this.prisma.product.create({
        data: {
          name: createProductDto.name,
          barcode: createProductDto.barcode,
          description: createProductDto.description,
          categoryId: createProductDto.categoryId,
          highlights: createProductDto.highlights,
          information: createProductDto.information,
          gstPercentage: createProductDto.gstPercentage,
          media: createProductDto.media
            ? {
                createMany: {
                  data: createProductDto.media.map((media) => {
                    return {
                      url: media.url,
                      mediaType: media.type,
                    };
                  }),
                },
              }
            : undefined,
          // Thumbnail will be handled separately after product creation
          weight: createProductDto.weight,
          weightUnit: createProductDto.weightUnit,
          length: createProductDto.dimension?.length || 0,
          width: createProductDto.dimension?.width || 0,
          height: createProductDto.dimension?.height || 0,
          discountValue: createProductDto.discountValue,
          discountType: createProductDto.discountType,
          slug: createProductDto.slug,
          active: createProductDto.active ?? true, // Default to true if not provided
          productPolicies: createProductDto.productPolicies
            ? {
                createMany: {
                  data: createProductDto.productPolicies.map((policy) => ({
                    productPolicyTypeId: policy.productPolicyTypeId,
                    details: policy.details || {},
                  })),
                },
              }
            : undefined,
          maxCartQuantity: createProductDto.maxCartQuantity,
        },
        include: {
          media: true,
          thumbnail: true,
          category: true,
          relatedProducts: {
            include: {
              relatedProduct: true,
            },
          },
        },
      });

      // Handle related products if provided
      if (
        createProductDto.relatedProductIds &&
        createProductDto.relatedProductIds.length > 0
      ) {
        // Create relationships for each related product
        for (const relatedProductId of createProductDto.relatedProductIds) {
          // Create bidirectional relationship
          await this.prisma.relatedProduct.create({
            data: {
              productId: product.id,
              relatedProductId: relatedProductId,
            },
          });

          // Create the reverse relationship
          await this.prisma.relatedProduct.create({
            data: {
              productId: relatedProductId,
              relatedProductId: product.id,
            },
          });
        }
      }

      // Handle thumbnail if provided
      if (createProductDto.thumbnail) {
        const thumbnailMedia = await this.prisma.media.create({
          data: {
            url: createProductDto.thumbnail.url,
            mediaType: createProductDto.thumbnail.type,
          },
        });

        await this.prisma.product.update({
          where: { id: product.id },
          data: { thumbnailId: thumbnailMedia.id },
        });

        product.thumbnail = thumbnailMedia;
      }

      // Index the product in Algolia
      await this.indexProductInAlgolia(product);
      return product;
    } catch (error) {
      throw new Exception(error as Error);
    }
  }

  async getProduct(id: number) {
    const product = await this.prisma.product.findUniqueOrThrow({
      where: {
        id: id,
      },
      include: {
        media: true,
        thumbnail: true,
        category: true,
        relatedProducts: {
          include: {
            relatedProduct: {
              include: {
                media: true,
                thumbnail: true,
              },
            },
          },
        },
        productPolicies: {
          include: {
            productPolicyType: true,
          },
        },
      },
    });

    const relatedProducts = product.relatedProducts.map(
      (relation) => relation.relatedProduct,
    );

    return {
      ...product,
      relatedProducts,
    };
  }

  async editProduct(id: number, editProductDto: EditProductDto) {
    // Extract media, thumbnail, and relatedProductIds from the DTO if they exist
    const {
      media,
      thumbnail,
      relatedProductIds,
      productPolicies,
      ...productData
    } = editProductDto;

    const product = await this.prisma.product.update({
      where: {
        id: id,
      },
      data: productData,
      include: {
        media: true,
        thumbnail: true,
        relatedProducts: {
          include: {
            relatedProduct: {
              include: {
                media: true,
                thumbnail: true,
              },
            },
          },
        },
      },
    });

    // If media is provided, handle it separately
    if (media) {
      // First delete all existing media for this product
      await this.prisma.media.deleteMany({
        where: {
          productId: id,
        },
      });

      // Then create new media entries
      for (const mediaItem of media) {
        await this.prisma.media.create({
          data: {
            mediaType: mediaItem.type,
            url: mediaItem.url,
            productId: id,
          },
        });
      }
    }

    // If thumbnail is provided, handle it separately
    if (thumbnail) {
      // First delete existing thumbnail relation
      if (product.thumbnailId) {
        await this.prisma.product.update({
          where: { id },
          data: { thumbnailId: null },
        });
      }

      // Create new thumbnail media and connect it to the product
      const thumbnailMedia = await this.prisma.media.create({
        data: {
          mediaType: thumbnail.type,
          url: thumbnail.url,
        },
      });

      // Update the product with the new thumbnail
      await this.prisma.product.update({
        where: { id },
        data: { thumbnailId: thumbnailMedia.id },
      });
    }

    if (productPolicies) {
      await this.prisma.productPolicy.deleteMany({
        where: {
          productId: product.id,
        },
      });

      await this.prisma.productPolicy.createMany({
        data: productPolicies.map((policy) => ({
          description: policy.description,
          productPolicyTypeId: policy.productPolicyTypeId,
          details: policy.details || {},
          productId: product.id,
        })),
      });
    }

    // If relatedProductIds is provided, handle related products
    if (relatedProductIds !== undefined) {
      // First delete all existing related product relationships for this product
      await this.prisma.relatedProduct.deleteMany({
        where: {
          OR: [{ productId: id }, { relatedProductId: id }],
        },
      });

      // Then create new relationships if there are any related products
      if (relatedProductIds && relatedProductIds.length > 0) {
        for (const relatedProductId of relatedProductIds) {
          // Skip if trying to relate to itself
          if (relatedProductId === id) continue;

          // Create bidirectional relationship
          await this.prisma.relatedProduct.create({
            data: {
              productId: id,
              relatedProductId: relatedProductId,
            },
          });

          // Create the reverse relationship
          await this.prisma.relatedProduct.create({
            data: {
              productId: relatedProductId,
              relatedProductId: id,
            },
          });
        }
      }
    }

    // Fetch the updated product with new media and related products
    const productWithUpdates = await this.prisma.product.findUniqueOrThrow({
      where: {
        id: id,
      },
      include: {
        category: true,
        media: true,
        thumbnail: true,
        relatedProducts: {
          include: {
            relatedProduct: {
              include: {
                media: true,
                thumbnail: true,
              },
            },
          },
        },
        productPolicies: true,
      },
    });

    // Transform the related products data to a more usable format
    const relatedProducts = productWithUpdates.relatedProducts.map(
      (relation) => relation.relatedProduct,
    );

    await this.indexProductInAlgolia(productWithUpdates);

    return {
      ...productWithUpdates,
      relatedProducts,
    };
  }

  async deleteProduct(id: number) {
    await this.removeProductFromAlgolia(id);

    const product = await this.prisma.product.delete({
      where: {
        id: id,
      },
    });

    return product;
  }

  // Helper method to index product in Algolia
  private async indexProductInAlgolia(
    product: Prisma.ProductGetPayload<{
      include: {
        category: true;
        thumbnail: true;
      };
    }>,
  ): Promise<void> {
    try {
      const algoliaRecord =
        AlgoliaService.transformProductToAlgoliaRecord(product);
      await this.algoliaService.indexProduct(algoliaRecord);
    } catch (error) {
      console.error('Failed to index product in Algolia:', error);
    }
  }

  private async removeProductFromAlgolia(productId: number): Promise<void> {
    try {
      // Only remove the base product from Algolia since we're not indexing variations
      const objectIdToDelete = AlgoliaService.generateObjectId(productId);
      await this.algoliaService.deleteProduct(objectIdToDelete);
    } catch (error) {
      console.error('Failed to remove product from Algolia:', error);
    }
  }

  async getTranslations(productId: number) {
    return this.prisma.productTranslation.findMany({
      where: {
        productId: productId,
      },
      include: {
        language: true,
      },
    });
  }

  async addTranslation(productId: number, dto: CreateProductTranslationDto) {
    return this.prisma.productTranslation.create({
      data: {
        productId,
        ...dto,
      },
    });
  }

  async updateTranslation(
    productId: number,
    languageId: number,
    dto: UpdateProductTranslationDto,
  ) {
    return this.prisma.productTranslation.update({
      where: {
        productId_languageId: { productId, languageId },
      },
      data: dto,
    });
  }

  async deleteTranslation(productId: number, languageId: number) {
    return this.prisma.productTranslation.delete({
      where: {
        productId_languageId: { productId, languageId },
      },
    });
  }

  async notifySubscribedUsersForProduct(productId: number) {
    const subscriptions =
      await this.prisma.productAvailabilitySubscription.findMany({
        where: {
          productId: productId,
          notified: false,
        },
        include: {
          user: true,
        },
      });

    for (const sub of subscriptions) {
      if (sub.user.firebaseToken) {
        await this.notificationService.sendToDevice({
          token: sub.user.firebaseToken,
          title: 'Product Restocked!',
          body: `The product you subscribed to is back in stock.`,
          data: {
            screen: 'product',
            productId: sub.productId.toString(),
          },
        });

        await this.prisma.productAvailabilitySubscription.delete({
          where: { id: sub.id },
        });
      }
    }
  }

  // Product Variation Methods

  async createProductAttribute(dto: CreateProductAttributeDto) {
    // Check if product exists
    const product = await this.prisma.product.findUnique({
      where: { id: dto.productId },
    });

    if (!product) {
      throw new Exception('Product not found', HttpStatus.NOT_FOUND);
    }

    // Check if attribute with same name already exists for this product
    const existingAttribute = await this.prisma.productAttribute.findUnique({
      where: {
        productId_name: {
          productId: dto.productId,
          name: dto.name,
        },
      },
    });

    if (existingAttribute) {
      throw new Exception(
        `Attribute "${dto.name}" already exists for this product`,
        HttpStatus.CONFLICT,
      );
    }

    // Create the attribute
    return this.prisma.productAttribute.create({
      data: dto,
    });
  }

  async getProductAttributes(productId: number) {
    return this.prisma.productAttribute.findMany({
      where: {
        productId,
        deletedAt: null, // Only return non-deleted attributes
      },
      include: {
        options: {
          where: {
            deletedAt: null, // Only return non-deleted options
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });
  }

  async deleteProductAttribute(attributeId: number) {
    // Check if attribute exists
    const attribute = await this.prisma.productAttribute.findUnique({
      where: { id: attributeId },
      include: {
        options: true,
        product: true,
      },
    });

    if (!attribute) {
      throw new Exception('Attribute not found', HttpStatus.NOT_FOUND);
    }

    // Soft delete the attribute
    await this.prisma.productAttribute.update({
      where: { id: attributeId },
      data: { deletedAt: new Date() },
    });

    // Soft delete all options of this attribute in a single operation
    await this.prisma.productAttributeOption.updateMany({
      where: {
        attributeId: attribute.id,
        deletedAt: null,
      },
      data: { deletedAt: new Date() },
    });

    // Soft delete those variations in a single operation
    await this.prisma.productVariation.updateMany({
      where: {
        productId: attribute.product.id,
        deletedAt: null,
      },
      data: { deletedAt: new Date() },
    });

    return {
      success: true,
      message: 'Attribute and related options/variations soft deleted',
    };
  }

  async createProductAttributeOption(dto: CreateProductAttributeOptionDto) {
    // Check if attribute exists
    const attribute = await this.prisma.productAttribute.findUnique({
      where: { id: dto.attributeId },
    });

    if (!attribute) {
      throw new Exception('Attribute not found', HttpStatus.NOT_FOUND);
    }

    // Create the option
    return this.prisma.productAttributeOption.create({
      data: dto,
    });
  }

  async deleteProductAttributeOption(optionId: number) {
    // Check if option exists
    const option = await this.prisma.productAttributeOption.findUnique({
      where: { id: optionId },
      include: {
        attribute: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!option) {
      throw new Exception('Option not found', HttpStatus.NOT_FOUND);
    }

    // Soft delete the option
    await this.prisma.productAttributeOption.update({
      where: { id: optionId },
      data: { deletedAt: new Date() },
    });

    // Soft delete those variations
    await this.prisma.productVariation.updateMany({
      where: {
        productId: option.attribute.product.id,
        deletedAt: null, // Only update variations that aren't already deleted
      },
      data: { deletedAt: new Date() },
    });

    return {
      success: true,
      message: 'Option and related variations soft deleted',
    };
  }

  async getProductVariations(productId: number) {
    // Check if product exists
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
    });

    if (!product) {
      throw new Exception('Product not found', HttpStatus.NOT_FOUND);
    }

    return this.prisma.productVariation.findMany({
      where: {
        productId,
        deletedAt: null, // Only return non-deleted variations
      },
      include: {
        options: {
          include: {
            option: {
              include: {
                attribute: true,
              },
            },
          },
        },
        media: true,
      },
      orderBy: { createdAt: 'asc' },
    });
  }

  async getProductVariation(id: number) {
    const variation = await this.prisma.productVariation.findUnique({
      where: { id },
      include: {
        options: {
          include: {
            option: {
              include: {
                attribute: true,
              },
            },
          },
        },
        media: true,
      },
    });

    if (!variation) {
      throw new Exception('Variation not found', HttpStatus.NOT_FOUND);
    }

    return variation;
  }

  async updateProductVariation(id: number, dto: UpdateProductVariationDto) {
    // Check if variation exists
    const variation = await this.prisma.productVariation.findUnique({
      where: { id },
    });

    if (!variation) {
      throw new Exception('Variation not found', HttpStatus.NOT_FOUND);
    }

    // Check if barcode is unique if provided
    if (dto.barcode) {
      const existingBarcode = await this.prisma.productVariation.findUnique({
        where: { barcode: dto.barcode },
      });

      if (existingBarcode && existingBarcode.id !== id) {
        throw new Exception('Barcode already exists', HttpStatus.CONFLICT);
      }
    }

    // Update the variation
    await this.prisma.productVariation.update({
      where: { id },
      data: {
        barcode: dto.barcode,
      },
      include: {
        options: {
          include: {
            option: {
              include: {
                attribute: true,
              },
            },
          },
        },
      },
    });

    // Handle media if provided
    if (dto.media !== undefined) {
      // Delete existing media
      await this.prisma.media.deleteMany({
        where: { variationId: id },
      });

      // Add new media if any
      if (dto.media && dto.media.length > 0) {
        await this.prisma.media.createMany({
          data: dto.media.map((m) => ({
            url: m.url,
            mediaType: m.type,
            variationId: id,
          })),
        });
      }
    }

    // Return the updated variation with media
    return this.prisma.productVariation.findUnique({
      where: { id },
      include: {
        options: {
          include: {
            option: {
              include: {
                attribute: true,
              },
            },
          },
        },
        media: true,
      },
    });
  }

  async deleteProductVariation(id: number) {
    // Check if variation exists
    const variation = await this.prisma.productVariation.findUnique({
      where: { id },
      include: { product: true },
    });

    if (!variation) {
      throw new Exception('Variation not found', HttpStatus.NOT_FOUND);
    }

    // Soft delete the variation
    await this.prisma.productVariation.update({
      where: { id },
      data: { deletedAt: new Date() },
    });

    // Check if this was the last non-deleted variation for the product
    const remainingVariations = await this.prisma.productVariation.count({
      where: {
        productId: variation.product.id,
        deletedAt: null,
      },
    });

    // If no variations left, update product hasVariations flag
    if (remainingVariations === 0) {
      await this.prisma.product.update({
        where: { id: variation.product.id },
        data: { hasVariations: false },
      });
    }

    return { success: true, message: 'Variation soft deleted' };
  }

  async generateProductVariations(dto: GenerateProductVariationsDto) {
    // Check if product exists
    const product = await this.prisma.product.findUnique({
      where: { id: dto.productId },
      include: {
        attributes: {
          where: { deletedAt: null }, // Only include non-deleted attributes
          include: {
            options: {
              where: { deletedAt: null }, // Only include non-deleted options
            },
          },
        },
      },
    });

    if (!product) {
      throw new Exception('Product not found', HttpStatus.NOT_FOUND);
    }

    // Check if product has attributes
    if (product.attributes.length === 0) {
      throw new Exception(
        'Product has no attributes. Add attributes before generating variations.',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if all attributes have options
    const attributesWithoutOptions = product.attributes.filter(
      (attr) => attr.options.length === 0,
    );
    if (attributesWithoutOptions.length > 0) {
      throw new Exception(
        `Attributes without options: ${attributesWithoutOptions
          .map((attr) => attr.name)
          .join(
            ', ',
          )}. Add options to all attributes before generating variations.`,
        HttpStatus.BAD_REQUEST,
      );
    }

    // Generate all possible combinations of options
    const combinations = this.generateCombinations(product.attributes);

    // Get existing variations to avoid duplicates and preserve data
    const existingVariations = await this.prisma.productVariation.findMany({
      where: {
        productId: dto.productId,
        deletedAt: null, // Only consider non-deleted variations
      },
      include: {
        options: {
          include: {
            option: true,
          },
        },
        media: true,
        inventory: true,
      },
    });

    // Create variations for each combination
    const variations: ProductVariationResponse[] = [];
    let counter = 1;

    for (const combination of combinations) {
      // Check if this combination already exists
      const optionIds = combination.map((option) => option.id);
      const existingVariation = existingVariations.find((variation) => {
        const variationOptionIds = variation.options.map(
          (mapping) => mapping.option.id,
        );
        return (
          // Check if the variation has exactly the same options
          optionIds.length === variationOptionIds.length &&
          optionIds.every((id) => variationOptionIds.includes(id))
        );
      });

      if (existingVariation) {
        // If variation exists, keep it and add to the result
        variations.push(existingVariation as any);
      } else {
        // Generate a unique barcode
        const barcode = dto.barcodePrefix
          ? `${dto.barcodePrefix}-${counter}-${new Date().getTime()}`
          : `${product.barcode}-${counter}-${new Date().getTime()}`;

        // Create a new variation
        const variation = await this.prisma.productVariation.create({
          data: {
            productId: dto.productId,
            barcode,
            options: {
              create: combination.map((option) => ({
                option: {
                  connect: { id: option.id },
                },
              })),
            },
          },
          include: {
            options: {
              include: {
                option: {
                  include: {
                    attribute: true,
                  },
                },
              },
            },
            media: true,
          },
        });

        variations.push({
          ...variation,
          quantity: 0,
          inStock: false,
          price: '0.00',
          originalPrice: '0.00',
        });
        counter++;
      }
    }

    // Update product to indicate it has variations
    await this.prisma.product.update({
      where: { id: dto.productId },
      data: { hasVariations: true },
    });

    return variations;
  }

  // Helper method to generate all possible combinations of attribute options
  private generateCombinations(attributes: any[]) {
    // Helper function to generate combinations recursively
    const generateHelper = (
      attrIndex: number,
      currentCombination: any[],
    ): any[][] => {
      // Base case: if we've processed all attributes, return the current combination
      if (attrIndex >= attributes.length) {
        return [currentCombination];
      }

      // Get the current attribute and its options
      const attribute = attributes[attrIndex];
      const options = attribute.options;

      // Initialize an array to store all combinations
      let allCombinations: any[][] = [];

      // For each option of the current attribute
      for (const option of options) {
        // Add this option to the current combination
        const newCombination = [...currentCombination, option];

        // Recursively generate combinations for the remaining attributes
        const combinations = generateHelper(attrIndex + 1, newCombination);

        // Add these combinations to our result
        allCombinations = [...allCombinations, ...combinations];
      }

      return allCombinations;
    };

    // Start the recursive generation with an empty combination
    return generateHelper(0, []);
  }
}
