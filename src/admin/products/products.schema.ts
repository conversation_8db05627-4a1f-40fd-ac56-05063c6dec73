import { AmountType, MediaType, WeightUnit } from '@prisma/client';
import { createZodDto } from 'nestjs-zod';
import z from 'zod';

const createProductSchema = z.object({
  name: z.string(),
  barcode: z.string(),
  description: z.string(),
  categoryId: z.number(),
  gstPercentage: z.number().min(0).max(100),
  highlights: z.union([z.any(), z.null()]).optional(),
  information: z.union([z.any(), z.null()]).optional(),
  weight: z.number().default(0),
  weightUnit: z.nativeEnum(WeightUnit).default('GRAM'),
  slug: z.string().optional(),
  discountValue: z.number().nullable().optional(),
  discountType: z.nativeEnum(AmountType).nullable().optional(),
  active: z.boolean().default(true),
  maxCartQuantity: z.number().min(1).nullable().optional(),
  media: z
    .array(
      z.object({
        type: z.nativeEnum(MediaType),
        url: z.string(),
      }),
    )
    .optional(),
  thumbnail: z
    .object({
      type: z.nativeEnum(MediaType),
      url: z.string(),
    })
    .optional(),
  relatedProductIds: z.array(z.number()).optional(),
  dimension: z
    .object({
      length: z.number().default(0),
      width: z.number().default(0),
      height: z.number().default(0),
    })
    .default({
      length: 0,
      width: 0,
      height: 0,
    }),
  productPolicies: z
    .array(
      z.object({
        productPolicyTypeId: z.number(),
        description: z.string().nullable().optional(),
        details: z.record(z.string(), z.string()).default({}),
      }),
    )
    .optional(),
});

export class CreateProductDto extends createZodDto(createProductSchema) {}

const editProductSchema = createProductSchema
  .omit({ media: true, thumbnail: true })
  .partial()
  .extend({
    media: z
      .array(
        z.object({
          type: z.nativeEnum(MediaType),
          url: z.string(),
        }),
      )
      .optional(),
    thumbnail: z
      .object({
        type: z.nativeEnum(MediaType),
        url: z.string(),
      })
      .optional(),
    relatedProductIds: z.array(z.number()).optional(),
  });

export class EditProductDto extends createZodDto(editProductSchema) {}

export const createProductTranslationSchema = z.object({
  languageId: z.number().int(),
  name: z.string(),
  description: z.string(),
});

export const updateProductTranslationSchema =
  createProductTranslationSchema.partial();

export class CreateProductTranslationDto extends createZodDto(
  createProductTranslationSchema,
) {}
export class UpdateProductTranslationDto extends createZodDto(
  updateProductTranslationSchema,
) {}

// Product Variation DTOs

// Create Product Attribute
const createProductAttributeSchema = z.object({
  name: z.string().min(1, 'Attribute name is required'),
  productId: z.number().int().positive(),
});

export class CreateProductAttributeDto extends createZodDto(
  createProductAttributeSchema,
) {}

// Create Product Attribute Option
const createProductAttributeOptionSchema = z.object({
  attributeId: z.number().int().positive(),
  name: z.string().min(1, 'Option name is required'),
  colorCode: z.string().optional(),
  imageUrl: z.string().optional(),
  iconUrl: z.string().optional(),
});

export class CreateProductAttributeOptionDto extends createZodDto(
  createProductAttributeOptionSchema,
) {}

// Generate Product Variations
const generateProductVariationsSchema = z.object({
  productId: z.number().int().positive(),
  barcodePrefix: z.string().optional(),
});

export class GenerateProductVariationsDto extends createZodDto(
  generateProductVariationsSchema,
) {}

// Update Product Variation
const updateProductVariationSchema = z.object({
  barcode: z.string().optional(),
  media: z
    .array(
      z.object({
        type: z.nativeEnum(MediaType),
        url: z.string(),
      }),
    )
    .optional(),
});

export class UpdateProductVariationDto extends createZodDto(
  updateProductVariationSchema,
) {}
