import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import {
  CreateProductAttributeDto,
  CreateProductAttributeOptionDto,
  CreateProductDto,
  CreateProductTranslationDto,
  EditProductDto,
  GenerateProductVariationsDto,
  UpdateProductTranslationDto,
  UpdateProductVariationDto,
} from 'src/admin/products/products.schema';
import { Admin } from 'src/auth/auth.decorator';
import { ProductsService } from './products.service';

@Controller('products')
export class ProductsController {
  constructor(private readonly productService: ProductsService) {}

  @Get()
  @Admin()
  async getProducts(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
    @Query('search') search?: string,
  ) {
    return this.productService.getProducts({
      page,
      pageSize,
      search,
    });
  }

  @Get('/:id')
  @Admin()
  async getProduct(@Param('id', ParseIntPipe) id: number) {
    return this.productService.getProduct(id);
  }

  @Post()
  @Admin()
  async addProduct(@Body() createProductDto: CreateProductDto) {
    return this.productService.addProduct(createProductDto);
  }

  @Patch('/:id')
  @Admin()
  async editProduct(
    @Param('id', ParseIntPipe) id: number,
    @Body() editProductDto: EditProductDto,
  ) {
    return this.productService.editProduct(id, editProductDto);
  }

  @Delete('/:id')
  @Admin()
  async deleteProduct(@Param('id', ParseIntPipe) id: number) {
    return this.productService.deleteProduct(id);
  }

  @Post('/:id/translations')
  @Admin()
  async addTranslation(
    @Param('id', ParseIntPipe) productId: number,
    @Body() dto: CreateProductTranslationDto,
  ) {
    return this.productService.addTranslation(productId, dto);
  }

  @Get('/:id/translations')
  async getTranslations(@Param('id', ParseIntPipe) productId: number) {
    return this.productService.getTranslations(productId);
  }

  @Patch('/:id/translations/:languageId')
  @Admin()
  async updateTranslation(
    @Param('id', ParseIntPipe) productId: number,
    @Param('languageId', ParseIntPipe) languageId: number,
    @Body() dto: UpdateProductTranslationDto,
  ) {
    return this.productService.updateTranslation(productId, languageId, dto);
  }

  @Delete('/:id/translations/:languageId')
  @Admin()
  async deleteTranslation(
    @Param('id', ParseIntPipe) productId: number,
    @Param('languageId', ParseIntPipe) languageId: number,
  ) {
    return this.productService.deleteTranslation(productId, languageId);
  }

  // Product Variation Endpoints

  @Post('/:id/attributes')
  @Admin()
  async createProductAttribute(
    @Param('id', ParseIntPipe) productId: number,
    @Body() dto: CreateProductAttributeDto,
  ) {
    // Override productId from path parameter
    dto.productId = productId;
    return this.productService.createProductAttribute(dto);
  }

  @Get('/:id/attributes')
  @Admin()
  async getProductAttributes(@Param('id', ParseIntPipe) productId: number) {
    return this.productService.getProductAttributes(productId);
  }

  @Delete('/attributes/:id')
  @Admin()
  async deleteProductAttribute(@Param('id', ParseIntPipe) attributeId: number) {
    return this.productService.deleteProductAttribute(attributeId);
  }

  @Post('/attributes/options')
  @Admin()
  async createProductAttributeOption(
    @Body() dto: CreateProductAttributeOptionDto,
  ) {
    return this.productService.createProductAttributeOption(dto);
  }

  @Delete('/attributes/options/:id')
  @Admin()
  async deleteProductAttributeOption(
    @Param('id', ParseIntPipe) optionId: number,
  ) {
    return this.productService.deleteProductAttributeOption(optionId);
  }

  @Get('/:id/variations')
  @Admin()
  async getProductVariations(@Param('id', ParseIntPipe) productId: number) {
    return this.productService.getProductVariations(productId);
  }

  @Get('/variations/:id')
  @Admin()
  async getProductVariation(@Param('id', ParseIntPipe) id: number) {
    return this.productService.getProductVariation(id);
  }

  @Patch('/variations/:id')
  @Admin()
  async updateProductVariation(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateProductVariationDto,
  ) {
    return this.productService.updateProductVariation(id, dto);
  }

  @Delete('/variations/:id')
  @Admin()
  async deleteProductVariation(@Param('id', ParseIntPipe) id: number) {
    return this.productService.deleteProductVariation(id);
  }

  @Post('/:id/generate-variations')
  @Admin()
  async generateProductVariations(
    @Param('id', ParseIntPipe) productId: number,
    @Body() dto: GenerateProductVariationsDto,
  ) {
    // Override productId from path parameter
    dto.productId = productId;
    return this.productService.generateProductVariations(dto);
  }
}
