import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { Admin } from 'src/auth/auth.decorator';
import { UpdateUserDto } from './users.schema';
import { UsersService } from './users.service';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Admin()
  @Get(':id/warehouses')
  async getUserWarehouses(@Param('id', ParseIntPipe) id: number) {
    return this.usersService.getUserWarehouses(id);
  }

  @Admin()
  @Get()
  async getUsers(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe)
    pageSize: number,
    @Query('search') search?: string,
  ) {
    return this.usersService.getUsers({
      page: page,
      pageSize: pageSize,
      search: search,
    });
  }

  @Admin()
  @Get(':id')
  async getUser(@Param('id', ParseIntPipe) id: number) {
    return this.usersService.getUser({
      id: id,
    });
  }

  @Admin()
  @Patch('/:id')
  async updateUser(
    @Body() updateUserDto: UpdateUserDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.usersService.updateUser(id, updateUserDto);
  }

  @Admin()
  @Get('download/excel')
  async downloadUsersExcel(@Res({ passthrough: true }) res: Response) {
    const streamableFile = await this.usersService.downloadUsersExcel();

    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `users-export-${timestamp}.xlsx`;

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    return streamableFile;
  }
}
