import { Injectable, StreamableFile } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { UpdateUserDto } from './users.schema';
import { Prisma } from '@prisma/client';
import { RewardPointsService } from 'src/reward-points/reward-points.service';
import * as ExcelJS from 'exceljs';
import { PassThrough } from 'stream';

@Injectable()
export class UsersService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly rewardPointService: RewardPointsService,
  ) {}

  async getUsers(params: {
    page?: number;
    pageSize?: number;
    search?: string;
  }) {
    const { page = 1, pageSize = 10, search } = params;

    const where: Prisma.UserWhereInput = search
      ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
            { phone: { contains: search, mode: 'insensitive' } },
          ],
        }
      : {};

    const [users, total] = await this.prisma.$transaction([
      this.prisma.user.findMany({
        skip: (page - 1) * pageSize,
        take: pageSize,
        where,
        select: {
          id: true,
          name: true,
          email: true,
          type: true,
          createdAt: true,
          updatedAt: true,
          addresses: true,
          phone: true,
          profilePicture: true,
          phoneCountry: true,
          countryId: true,
          referralCode: true,
          rewardTier: true,
          rewardTierId: true,
          referrals: {
            select: {
              id: true,
            },
          },
          firebaseToken: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.user.count({ where }),
    ]);

    const usersWithStats = await Promise.all(
      users.map(async (user) => {
        // Get reward points
        const points = await this.rewardPointService.getUserRewardPoints(
          user.id,
        );

        // Get order statistics for each user
        const [totalOrders, orderStats] = await this.prisma.$transaction([
          this.prisma.order.count({
            where: { userId: user.id },
          }),
          this.prisma.order.aggregate({
            where: {
              userId: user.id,
              status: { notIn: ['CANCELLED'] }, // Exclude cancelled orders from financial calculations
            },
            _sum: {
              totalAmount: true,
            },
          }),
        ]);

        // Calculate average order value
        const totalOrderPrice = orderStats._sum.totalAmount?.toNumber() || 0;
        const averageOrderPrice =
          totalOrders > 0 ? totalOrderPrice / totalOrders : 0;

        return {
          ...user,
          rewardPoints: points,
          totalOrders: totalOrders,
          totalOrderPrice: totalOrderPrice.toFixed(2),
          averageOrderPrice: averageOrderPrice.toFixed(2),
        };
      }),
    );

    return {
      data: usersWithStats,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getUser({ id }: { id: number }) {
    const user = await this.prisma.user.findUniqueOrThrow({
      where: {
        id: id,
      },
      select: {
        id: true,
        name: true,
        email: true,
        type: true,
        createdAt: true,
        updatedAt: true,
        addresses: true,
        phone: true,
        profilePicture: true,
        phoneCountry: true,
        countryId: true,
        referralCode: true,
        rewardTier: true,
        rewardTierId: true,
        referrals: {
          select: {
            id: true,
          },
        },
      },
    });

    const { points } =
      await this.rewardPointService.determineUserRewardTier(id);

    // Get order statistics
    const [totalOrders, orderStats] = await this.prisma.$transaction([
      this.prisma.order.count({
        where: { userId: id },
      }),
      this.prisma.order.aggregate({
        where: {
          userId: id,
          status: { notIn: ['CANCELLED'] }, // Exclude cancelled orders from financial calculations
        },
        _sum: {
          totalAmount: true,
        },
      }),
    ]);

    // Calculate average order value
    const totalOrderPrice = orderStats._sum.totalAmount?.toNumber() || 0;
    const averageOrderPrice =
      totalOrders > 0 ? totalOrderPrice / totalOrders : 0;

    return {
      ...user,
      rewardPoints: points,
      totalOrders: totalOrders,
      totalOrderPrice: totalOrderPrice.toFixed(),
      averageOrderPrice: averageOrderPrice.toFixed(),
    };
  }

  async updateUser(id: number, updateUserDto: UpdateUserDto) {
    const user = await this.prisma.user.update({
      where: {
        id,
      },
      data: {
        name: updateUserDto.name,
        phone: updateUserDto.phone,
        type: updateUserDto.type,
      },
    });

    return user;
  }

  async getUserWarehouses(id: number) {
    const assignments = await this.prisma.warehouseStaff.findMany({
      where: { userId: id },
      include: { warehouse: true },
    });

    return assignments.map((a) => a.warehouse);
  }

  async downloadUsersExcel(): Promise<StreamableFile> {
    const users = await this.prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        type: true,
        createdAt: true,
        updatedAt: true,
        addresses: true,
        phone: true,
        profilePicture: true,
        phoneCountry: true,
        countryId: true,
        referralCode: true,
        rewardTier: {
          select: {
            name: true,
            type: true,
          },
        },
        rewardTierId: true,
        referrals: {
          select: {
            id: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Get additional stats for each user
    const usersWithStats = await Promise.all(
      users.map(async (user) => {
        // Get reward points
        const points = await this.rewardPointService.getUserRewardPoints(
          user.id,
        );

        // Get order statistics for each user
        const [totalOrders, orderStats] = await this.prisma.$transaction([
          this.prisma.order.count({
            where: { userId: user.id },
          }),
          this.prisma.order.aggregate({
            where: {
              userId: user.id,
              status: { notIn: ['CANCELLED'] },
            },
            _sum: {
              totalAmount: true,
            },
          }),
        ]);

        // Calculate average order value
        const totalOrderPrice = orderStats._sum.totalAmount?.toNumber() || 0;
        const averageOrderPrice =
          totalOrders > 0 ? totalOrderPrice / totalOrders : 0;

        return {
          ...user,
          rewardPoints: points,
          totalOrders: totalOrders,
          totalOrderPrice: totalOrderPrice.toFixed(2),
          averageOrderPrice: averageOrderPrice.toFixed(2),
        };
      }),
    );

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Users');

    // Define columns
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 10 },
      { header: 'Name', key: 'name', width: 20 },
      { header: 'Email', key: 'email', width: 30 },
      // { header: 'Phone', key: 'phone', width: 15 },
      // { header: 'Phone Country', key: 'phoneCountry', width: 15 },
      { header: 'User Type', key: 'type', width: 15 },
      { header: 'Referral Code', key: 'referralCode', width: 15 },
      { header: 'Reward Tier', key: 'rewardTier', width: 15 },
      { header: 'Reward Points', key: 'rewardPoints', width: 15 },
      { header: 'Total Orders', key: 'totalOrders', width: 15 },
      { header: 'Total Order Value', key: 'totalOrderPrice', width: 20 },
      { header: 'Average Order Value', key: 'averageOrderPrice', width: 20 },
      { header: 'Total Referrals', key: 'totalReferrals', width: 15 },
      { header: 'Total Addresses', key: 'totalAddresses', width: 15 },
      // { header: 'Profile Picture', key: 'profilePicture', width: 50 },
      { header: 'Created At', key: 'createdAt', width: 20 },
      { header: 'Updated At', key: 'updatedAt', width: 20 },
    ];

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    };

    // Add data rows
    usersWithStats.forEach((user) => {
      worksheet.addRow({
        id: user.id,
        name: user.name,
        email: user.email,
        // phone: user.phone,
        // phoneCountry: user.phoneCountry.,
        type: user.type,
        referralCode: user.referralCode,
        rewardTier: user.rewardTier?.name || 'None',
        rewardPoints: user.rewardPoints,
        totalOrders: user.totalOrders,
        totalOrderPrice: `$${user.totalOrderPrice}`,
        averageOrderPrice: `$${user.averageOrderPrice}`,
        totalReferrals: user.referrals.length,
        totalAddresses: user.addresses.length,
        // profilePicture: user.profilePicture || '',
        createdAt: user.createdAt.toISOString().split('T')[0],
        updatedAt: user.updatedAt.toISOString().split('T')[0],
      });
    });

    // Auto-fit columns
    worksheet.columns.forEach((column) => {
      if (column.header && column.header.length > 12) {
        column.width = column.header.length + 2;
      }
    });

    // Create stream
    const stream = new PassThrough();
    await workbook.xlsx.write(stream);
    stream.end();

    return new StreamableFile(stream);
  }
}
