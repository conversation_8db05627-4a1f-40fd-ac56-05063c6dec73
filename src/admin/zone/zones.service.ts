import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateZoneDto } from './zones.schema';
import { PolyUtil } from 'node-geometry-library';

@Injectable()
export class ZonesService {
  constructor(private readonly prisma: PrismaService) {}

  async getZones() {
    const zones = await this.prisma.zone.findMany({
      include: {
        coordinates: true,
      },
    });

    return zones;
  }

  async createZone(createZoneDto: CreateZoneDto) {
    const zone = await this.prisma.zone.create({
      data: {
        name: createZoneDto.name,
        coordinates: {
          createMany: {
            data: createZoneDto.coordinates,
          },
        },
      },
      include: {
        coordinates: true,
      },
    });

    return zone;
  }

  async deleteZone(id: number) {
    const zone = await this.prisma.zone.delete({
      where: {
        id: id,
      },
      include: {
        coordinates: true,
      },
    });

    return zone;
  }

  async checkZone(lat: string, long: string) {
    console.log(`${lat}, ${long}`);

    const zones = await this.prisma.zone.findMany({
      include: {
        coordinates: true,
      },
      where: {
        active: true,
      },
    });

    let withinZone = false;

    for (const zone of zones) {
      const response = PolyUtil.containsLocation(
        { lat: +lat, lng: +long },
        zone.coordinates.map((coord) => ({
          lat: +coord.lat,
          lng: +coord.long,
        })),
      );

      if (response) {
        withinZone = response;
        break;
      }
    }

    return {
      withinZone: withinZone,
    };
  }
}
