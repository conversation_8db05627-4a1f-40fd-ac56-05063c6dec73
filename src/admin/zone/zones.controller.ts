import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
} from '@nestjs/common';
import { ZonesService } from './zones.service';
import { CreateZoneDto } from './zones.schema';

@Controller('zones')
export class ZonesController {
  constructor(private readonly zoneService: ZonesService) {}

  @Get()
  async getZones() {
    return this.zoneService.getZones();
  }

  @Post()
  async createZone(@Body() createZoneDto: CreateZoneDto) {
    return this.zoneService.createZone(createZoneDto);
  }

  @Delete(':id')
  async deleteZone(@Param('id', ParseIntPipe) id: number) {
    return this.zoneService.deleteZone(id);
  }

  @Get('check')
  async checkZone(@Query('lat') lat: string, @Query('long') long: string) {
    return this.zoneService.checkZone(lat, long);
  }
}
