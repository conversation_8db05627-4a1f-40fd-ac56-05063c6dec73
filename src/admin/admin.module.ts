import { Module } from '@nestjs/common';
import { Context } from 'src/context';
import { PrismaService } from 'src/prisma/prisma.service';
import { BannersController } from './banners/banners.controller';
import { BannersService } from './banners/banners.service';
import { CategoriesController } from './categories/categories.controller';
import { CategoriesService } from './categories/categories.service';
import { CountriesController } from './countries/countries.controller';
import { CountriesService } from './countries/countries.service';
import { CouponsController } from './coupons/coupons.controller';
import { CouponsService } from './coupons/coupons.service';
import { DeliveryDriversController } from './delivery-drivers/delivery-drivers.controller';
import { DeliveryDriversService } from './delivery-drivers/delivery-drivers.service';
import { InventoryController } from './inventories/inventories.controller';
import { InventoryService } from './inventories/inventories.service';
import { MediaController } from './media/media.controller';
import { MediaService } from './media/media.service';
import { OrdersController } from './orders/orders.controller';
import { OrdersService } from './orders/orders.service';
import { ProductsController } from './products/products.controller';
import { ProductsService } from './products/products.service';
import { RewardPointConfigController } from './reward-point-config/reward-point-config.controller';
import { RewardPointConfigService } from './reward-point-config/reward-point-config.service';
import { RewardTiersController } from './reward-tiers/reward-tiers.controller';
import { RewardTiersService } from './reward-tiers/reward-tiers.service';
import { SuppliersController } from './suppliers/suppliers.controller';
import { SuppliersService } from './suppliers/suppliers.service';
import { UsersController } from './users/users.controller';
import { UsersService } from './users/users.service';
import { WarehouseController } from './warehouse/warehouse.controller';
import { WarehouseService } from './warehouse/warehouse.service';
import { ZonesController } from './zone/zones.controller';
import { ZonesService } from './zone/zones.service';
import { AnalyticsService } from './analytics/analytics.service';
import { AnalyticsController } from './analytics/analytics.controller';
import { TierProductDiscountsService } from './tier-product-discounts/tier-product-discounts.service';
import { TierProductDiscountsController } from './tier-product-discounts/tier-product-discounts.controller';
import { RewardPointsService } from 'src/reward-points/reward-points.service';
import { NotificationService } from './notification/notification.service';
import { NotificationController } from './notification/notification.controller';
import { DeliverySlotService } from './delivery-slot/delivery-slot.service';
import { DeliverySlotController } from './delivery-slot/delivery-slot.controller';
import { HomeSectionsService } from './home-sections/home-sections.service';
import { HomeSectionsController } from './home-sections/home-sections.controller';
import { ProductPolicyService } from './product-policy/product-policy.service';
import { ProductPolicyController } from './product-policy/product-policy.controller';
import { PermissionFeaturesController } from './permissions/permission-features.controller';
import { PermissionFeaturesService } from './permissions/permission-features.service';
import { RolesController } from './permissions/roles.controller';
import { RolesService } from './permissions/roles.service';
import { PermissionsService } from './permissions/permissions.service';
import { PermissionGuard } from 'src/auth/permission.guard';
import { AdminOrderTransformer } from 'src/transformers/admin-order.transformer';
import { ProductTransformer } from 'src/transformers/product.transformer';
import { AlgoliaService } from 'src/algolia/algolia.service';
import { EmailService } from 'src/email/email.service';

@Module({
  imports: [],
  controllers: [
    ProductsController,
    CategoriesController,
    CountriesController,
    UsersController,
    OrdersController,
    InventoryController,
    MediaController,
    CouponsController,
    WarehouseController,
    ZonesController,
    BannersController,
    SuppliersController,
    DeliveryDriversController,
    RewardTiersController,
    RewardPointConfigController,
    AnalyticsController,
    TierProductDiscountsController,
    NotificationController,
    DeliverySlotController,
    HomeSectionsController,
    ProductPolicyController,
    PermissionFeaturesController,
    RolesController,
  ],
  providers: [
    ProductsService,
    CategoriesService,
    CountriesService,
    UsersService,
    OrdersService,
    InventoryService,
    MediaService,
    Context,
    PrismaService,
    CouponsService,
    WarehouseService,
    ZonesService,
    BannersService,
    SuppliersService,
    DeliveryDriversService,
    RewardTiersService,
    RewardPointConfigService,
    AnalyticsService,
    TierProductDiscountsService,
    RewardPointsService,
    NotificationService,
    DeliverySlotService,
    HomeSectionsService,
    ProductPolicyService,
    PermissionFeaturesService,
    RolesService,
    PermissionsService,
    PermissionGuard,
    ProductTransformer,
    AdminOrderTransformer,
    AlgoliaService,
    EmailService,
  ],
  exports: [
    InventoryService,
    WarehouseService,
    OrdersService,
    NotificationService,
    ZonesService,
    DeliverySlotService,
    RewardTiersService,
    PermissionsService,
    PermissionGuard,
  ],
})
export class AdminModule {}
