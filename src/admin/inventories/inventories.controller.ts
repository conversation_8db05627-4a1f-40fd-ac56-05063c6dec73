import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { InventoryService } from './inventories.service';
import {
  CreateInventoryDto,
  CreateInventoryTransactionDto,
  UpdateInventoryDto,
} from './inventories.schema';

@Controller('inventories')
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  @Get()
  async getInventories(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
  ) {
    return this.inventoryService.getInventories({
      page: page,
      pageSize: pageSize,
    });
  }

  @Get('batches')
  async getInventoryBatches(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
  ) {
    return this.inventoryService.getInventoryBatches({
      page,
      pageSize,
    });
  }

  @Get('batches/:id')
  async getInventoriesByBatch(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
    @Param('id', ParseIntPipe) batchId: number,
  ) {
    return this.inventoryService.getInventoriesByBatch({
      batchId,
      page,
      pageSize,
    });
  }

  @Get('/:id')
  async getInventoryById(@Param('id', ParseIntPipe) id: number) {
    return this.inventoryService.getInventoryById(id);
  }

  @Put('/:id')
  async updateInventory(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateInventoryDto: UpdateInventoryDto,
  ) {
    return this.inventoryService.updateInventory(id, updateInventoryDto);
  }

  @Post('/:id/transactions')
  async addInventoryTransaction(
    @Param('id', ParseIntPipe) id: number,
    @Body() createTransactionDto: CreateInventoryTransactionDto,
  ) {
    return this.inventoryService.addInventoryTransaction(
      id,
      createTransactionDto,
    );
  }

  @Post()
  async createInventories(@Body() createInventoryDto: CreateInventoryDto) {
    return this.inventoryService.createInventories(createInventoryDto);
  }

  @Get('/products/:productId/variations')
  async getProductVariationsWithStock(
    @Param('productId', ParseIntPipe) productId: number,
    @Query('warehouseId', ParseIntPipe) warehouseId: number,
  ) {
    return this.inventoryService.getProductVariationsWithStock(
      productId,
      warehouseId,
    );
  }
}
