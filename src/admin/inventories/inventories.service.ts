import { Injectable } from '@nestjs/common';
import {
  Inventory,
  InventoryTransaction,
  InventoryTransactionType,
  Prisma,
  WarehouseType,
} from '@prisma/client';
import { Context } from 'src/context';
import { Exception, NoWarehouseException } from 'src/exceptions';
import { PrismaService } from 'src/prisma/prisma.service';
import { WarehouseService } from '../warehouse/warehouse.service';
import {
  CreateInventoryDto,
  CreateInventoryTransactionDto,
  UpdateInventoryDto,
} from './inventories.schema';
import { ProductsService } from '../products/products.service';
import { ProductVariationResponse } from 'src/types';

@Injectable()
export class InventoryService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly context: Context,
    private readonly warehouseService: WarehouseService,
    private readonly productsService: ProductsService,
  ) {}

  async getInventories(params: { page?: number; pageSize?: number }) {
    const { page = 1, pageSize = 10 } = params;

    const [inventories, total] = await this.prisma.$transaction([
      this.prisma.inventory.findMany({
        skip: (page - 1) * pageSize,
        take: pageSize,
        include: {
          inventoryTransactions: true,
          supplier: true,
          product: true,
          batch: {
            include: {
              warehouse: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.inventory.count(),
    ]);

    return {
      data: inventories.map((inventory) => ({
        ...inventory,
        quantity: this.getQuantityOfInventoryItem(inventory),
      })),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getInventoryBatches(params: { page?: number; pageSize?: number }) {
    const { page = 1, pageSize = 10 } = params;

    const [batches, total] = await this.prisma.$transaction([
      this.prisma.inventoryBatch.findMany({
        skip: (page - 1) * pageSize,
        take: pageSize,
        include: {
          warehouse: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.inventoryBatch.count(),
    ]);

    return {
      data: batches,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getInventoriesByBatch(params: {
    batchId: number;
    page?: number;
    pageSize?: number;
  }) {
    const { batchId, page = 1, pageSize = 10 } = params;

    const [inventories, total] = await this.prisma.$transaction([
      this.prisma.inventory.findMany({
        include: {
          inventoryTransactions: true,
          supplier: true,
          product: true,
          batch: {
            include: {
              warehouse: true,
            },
          },
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        where: { batchId },
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.inventory.count({
        where: { batchId },
      }),
    ]);

    return {
      data: inventories,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getInventoryById(id: number) {
    const inventory = await this.prisma.inventory.findUniqueOrThrow({
      where: { id },
      include: {
        inventoryTransactions: true,
        product: true,
        batch: {
          include: {
            warehouse: true,
          },
        },
      },
    });

    return {
      ...inventory,
      quantity: this.getQuantityOfInventoryItem(inventory),
      inventoryTransactions: inventory.inventoryTransactions,
    };
  }

  async updateInventory(id: number, updateInventoryDto: UpdateInventoryDto) {
    // Verify inventory exists
    await this.prisma.inventory.findUniqueOrThrow({
      where: { id },
    });

    const updatedInventory = await this.prisma.inventory.update({
      where: { id },
      data: updateInventoryDto,
      include: {
        inventoryTransactions: true,
        product: true,
        batch: {
          include: {
            warehouse: true,
          },
        },
      },
    });

    return {
      ...updatedInventory,
      quantity: this.getQuantityOfInventoryItem(updatedInventory),
      inventoryTransactions: updatedInventory.inventoryTransactions,
    };
  }

  async addInventoryTransaction(
    inventoryId: number,
    createTransactionDto: CreateInventoryTransactionDto,
  ) {
    // Verify inventory exists
    const inventory = await this.prisma.inventory.findUniqueOrThrow({
      where: { id: inventoryId },
    });

    const transaction = await this.prisma.inventoryTransaction.create({
      data: {
        ...createTransactionDto,
        inventoryId,
        byUserId: this.context.user?.id,
      },
    });

    if (
      (createTransactionDto.type === 'PURCHASE' ||
        createTransactionDto.type === 'ADJUSTMENT') &&
      createTransactionDto.quantity > 0
    ) {
      await this.productsService.notifySubscribedUsersForProduct(
        inventory.productId,
      );
    }

    return transaction;
  }

  async createInventories(createInventoryDto: CreateInventoryDto) {
    const batch = await this.prisma.$transaction(async (tx) => {
      const batch = await tx.inventoryBatch.create({
        data: {
          name: createInventoryDto.batchName,
          warehouseId: createInventoryDto.warehouseId,
        },
        include: { inventories: true },
      });

      for (const inventory of createInventoryDto.inventories) {
        const { initialStock, barcode, ...rest } = inventory;

        // First check if barcode belongs to a product
        const product = await tx.product.findUnique({
          where: { barcode },
        });

        // If not a product, check if it's a product variation
        const variation = !product
          ? await tx.productVariation.findUnique({
              where: { barcode },
              include: { product: true },
            })
          : null;

        if (!product && !variation) {
          throw new Exception(
            `Product or variation with barcode "${barcode}" not found.`,
          );
        }

        // If neither product nor variation.product exists, throw an error
        if (!product && !variation?.product) {
          throw new Error('Neither product nor variation product found');
        }
        const productId = product?.id ?? variation!.product.id;
        const variationId = variation ? variation.id : null;

        const i = await tx.inventory.create({
          data: {
            ...rest,
            productId,
            variationId,
            batchId: batch.id,
            inventoryTransactions: {
              create: {
                type: 'PURCHASE',
                quantity: initialStock,
                byUserId: this.context.user?.id,
              },
            },
          },
          include: {
            inventoryTransactions: true,
            product: true,
            variation: true,
            batch: true,
          },
        });
        batch.inventories.push(i);
      }
      return batch;
    });

    // Notify subscribers
    for (const inventory of batch.inventories) {
      await this.productsService.notifySubscribedUsersForProduct(
        inventory.productId,
      );
    }

    return batch;
  }

  getQuantityOfInventoryItem(
    inventory: Inventory & { inventoryTransactions: InventoryTransaction[] },
  ): number {
    return inventory.inventoryTransactions.reduce((acc, transaction) => {
      if (
        (
          [
            InventoryTransactionType.PURCHASE,
            InventoryTransactionType.RETURN,
            InventoryTransactionType.ADJUSTMENT,
          ] as InventoryTransactionType[]
        ).includes(transaction.type)
      ) {
        return acc + transaction.quantity;
      }
      return acc - transaction.quantity;
    }, 0);
  }

  async getProductInventoryByLocation(
    productId: number,
    userLocation: { lat: number; long: number },
    warehouseType: WarehouseType,
    variationId?: number,
  ): Promise<Inventory & { quantity: number }> {
    const nearestWarehouse = await this.warehouseService.getNearestWarehouse(
      userLocation,
      warehouseType,
    );
    if (!nearestWarehouse) {
      throw new NoWarehouseException();
    }

    // Build the query based on whether a variation is specified
    const whereClause: any = {
      productId,
      batch: { warehouseId: nearestWarehouse.id },
    };

    // If variation is specified, add it to the query
    if (variationId) {
      whereClause.variationId = variationId;
    } else {
      // If no variation is specified, only get inventory for the base product
      whereClause.variationId = null;
    }

    const inventories = await this.prisma.inventory.findMany({
      where: whereClause,
      include: { inventoryTransactions: true, batch: true, variation: true },
    });

    if (inventories.length < 1) {
      throw new Exception(
        `No inventory found for this ${variationId ? 'product variation' : 'product'} in the nearest warehouse`,
      );
    }

    inventories.sort((a, b) => {
      const qtyA = this.getQuantityOfInventoryItem(a);
      const qtyB = this.getQuantityOfInventoryItem(b);
      if (qtyA !== qtyB) {
        return qtyA - qtyB;
      }
      if (a.expiryDate && b.expiryDate) {
        return (
          new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime()
        );
      }
      return 0;
    });

    const selected = inventories[0];
    return {
      ...selected,
      quantity: this.getQuantityOfInventoryItem(selected),
    };
  }

  async getProductInventoryByWarehouse(
    productId: number,
    warehouseId: number,
    variationId?: number,
  ): Promise<Inventory & { quantity: number }> {
    // Build the query based on whether a variation is specified
    const whereClause: any = {
      productId,
      batch: { warehouseId },
    };

    // If variation is specified, add it to the query
    if (variationId) {
      whereClause.variationId = variationId;
    } else {
      // If no variation is specified, only get inventory for the base product
      whereClause.variationId = null;
    }

    const inventories = await this.prisma.inventory.findMany({
      where: whereClause,
      include: { inventoryTransactions: true, batch: true, variation: true },
    });

    if (inventories.length < 1) {
      throw new Exception(
        `No inventory found for this ${variationId ? 'product variation' : 'product'} in the specified warehouse`,
      );
    }

    inventories.sort((a, b) => {
      const qtyA = this.getQuantityOfInventoryItem(a);
      const qtyB = this.getQuantityOfInventoryItem(b);
      if (qtyA !== qtyB) {
        return qtyA - qtyB;
      }
      if (a.expiryDate && b.expiryDate) {
        return (
          new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime()
        );
      }
      return 0;
    });

    const selected = inventories[0];
    return {
      ...selected,
      quantity: this.getQuantityOfInventoryItem(selected),
    };
  }

  // New method to get all variations of a product with their stock information
  async getProductVariationsWithStock(productId: number, warehouseId: number) {
    // Get all variations for the product
    const variations = await this.prisma.productVariation.findMany({
      where: {
        productId,
        deletedAt: null, // Only include non-deleted variations
      },
      include: {
        options: {
          include: {
            option: {
              include: {
                attribute: true,
              },
            },
          },
        },
        media: true,
      },
    });

    // Get inventory for each variation
    const result: ProductVariationResponse[] = [];
    for (const variation of variations) {
      const inventories = await this.prisma.inventory.findMany({
        where: {
          productId,
          variationId: variation.id,
          batch: { warehouseId },
        },
        include: { inventoryTransactions: true },
      });

      // Sort inventories using the same logic as getProductInventoryByWarehouse
      inventories.sort((a, b) => {
        const qtyA = this.getQuantityOfInventoryItem(a);
        const qtyB = this.getQuantityOfInventoryItem(b);
        if (qtyA !== qtyB) {
          return qtyA - qtyB;
        }
        if (a.expiryDate && b.expiryDate) {
          return (
            new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime()
          );
        }
        return 0;
      });

      // Select the first inventory after sorting (same as in getProductInventoryByWarehouse)
      let quantity = 0;
      let selectedPrice: Prisma.Decimal | null = null;

      if (inventories.length > 0) {
        const selected = inventories[0];
        quantity = this.getQuantityOfInventoryItem(selected);
        selectedPrice = selected.sellingPrice;
      }

      result.push({
        ...variation,
        quantity: quantity,
        inStock: quantity > 0,
        price: selectedPrice ? selectedPrice.toString() : null,
      });
    }

    return result;
  }

  // TODO: add update batch, update inventory (update data, create transactions), remove inventory
}
