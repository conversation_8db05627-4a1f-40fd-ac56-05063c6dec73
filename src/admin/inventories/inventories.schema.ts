import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const CreateInventorySchema = z.object({
  batchName: z.string(),
  warehouseId: z.number().int().positive(),
  inventories: z.array(
    z
      .object({
        barcode: z.string().min(1, 'Barcode is required'),
        buyingPrice: z
          .number()
          .positive('Buying price must be a positive number'),
        sellingPrice: z
          .number()
          .positive('Selling price must be a positive number'),
        manufactureDate: z.date().optional(),
        expiryDate: z.date({
          required_error: 'Expiry date is required',
          invalid_type_error: 'Expiry date must be a valid date',
        }),
        supplierId: z.number().int().positive().optional(),
        initialStock: z.number().int().positive(),
      })
      .refine((data) => data.buyingPrice < data.sellingPrice, {
        message: 'Buying price must be less than selling price',
        path: ['sellingPrice'],
      }),
  ),
});

export class CreateInventoryDto extends createZodDto(CreateInventorySchema) {}

export const UpdateInventorySchema = z
  .object({
    buyingPrice: z.number().positive('Buying price must be a positive number'),
    sellingPrice: z
      .number()
      .positive('Selling price must be a positive number'),
    manufactureDate: z.date().optional(),
    expiryDate: z.date({
      required_error: 'Expiry date is required',
      invalid_type_error: 'Expiry date must be a valid date',
    }),
    supplierId: z.number().int().positive().optional(),
  })
  .refine((data) => data.buyingPrice < data.sellingPrice, {
    message: 'Buying price must be less than selling price',
    path: ['sellingPrice'],
  });

export class UpdateInventoryDto extends createZodDto(UpdateInventorySchema) {}

export const CreateInventoryTransactionSchema = z.object({
  type: z.enum(['PURCHASE', 'SALE', 'ADJUSTMENT', 'RETURN']),
  quantity: z.number().int().positive('Quantity must be a positive number'),
  remark: z.string().optional(),
});

export class CreateInventoryTransactionDto extends createZodDto(
  CreateInventoryTransactionSchema,
) {}
