import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { WarehouseService } from './warehouse.service';
import {
  AssignWarehousesToStaffDto,
  CreateWarehouseDto,
  UpdateWarehouseDto,
} from './warehouse.schema';

@Controller('warehouses')
export class WarehouseController {
  constructor(private readonly warehouseService: WarehouseService) {}

  @Get()
  async getWarehouses(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
  ) {
    return this.warehouseService.getWarehouses({
      page: page ?? 1,
      pageSize: pageSize ?? 10,
    });
  }

  @Get(':id')
  async getWarehouseById(@Param('id', ParseIntPipe) id: number) {
    return this.warehouseService.getWarehouseById(id);
  }

  @Post()
  async createWarehouse(@Body() createWarehouseDto: CreateWarehouseDto) {
    return this.warehouseService.createWarehouse(createWarehouseDto);
  }

  @Patch('assign-staff')
  async assignWarehousesToStaff(@Body() dto: AssignWarehousesToStaffDto) {
    return this.warehouseService.assignWarehousesToStaff(dto);
  }

  @Patch(':id')
  async updateWarehouse(
    @Body() updateWarehouseDto: UpdateWarehouseDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.warehouseService.updateWarehouse(updateWarehouseDto, id);
  }

  @Delete(':id')
  async deleteWarehouse(@Param('id', ParseIntPipe) id: number) {
    return this.warehouseService.deleteWarehouse(id);
  }
}
