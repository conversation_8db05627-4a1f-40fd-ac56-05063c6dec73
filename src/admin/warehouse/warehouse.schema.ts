import { WarehouseType } from '@prisma/client';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const CreateWarehouseSchema = z.object({
  lat: z.string(),
  long: z.string(),
  name: z.string(),
  type: z.nativeEnum(WarehouseType),
  active: z.boolean().default(true),
});

export class CreateWarehouseDto extends createZodDto(CreateWarehouseSchema) {}

const UpdateWarehouseSchema = CreateWarehouseSchema.partial();

export class UpdateWarehouseDto extends createZodDto(UpdateWarehouseSchema) {}

export const AssignWarehousesToStaffSchema = z.object({
  userId: z.number().int().positive(),
  warehouseIds: z.array(z.number().int().positive()),
});

export class AssignWarehousesToStaffDto extends createZodDto(
  AssignWarehousesToStaffSchema,
) {}
