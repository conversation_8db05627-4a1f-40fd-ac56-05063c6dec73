import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  AssignWarehousesToStaffDto,
  CreateWarehouseDto,
  UpdateWarehouseDto,
} from './warehouse.schema';
import { Warehouse, WarehouseType } from '@prisma/client';
import { calculateDistance } from 'src/util/utils';

@Injectable()
export class WarehouseService {
  constructor(private readonly prisma: PrismaService) {}

  async getWarehouses(params: { page?: number; pageSize?: number }) {
    const { page = 1, pageSize = 10 } = params;

    const [warehouses, total] = await this.prisma.$transaction([
      this.prisma.warehouse.findMany({
        skip: (page - 1) * pageSize,
        take: params.pageSize,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.warehouse.count({}),
    ]);

    return {
      data: warehouses,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getWarehouseById(id: number) {
    const warehouse = await this.prisma.warehouse.findUniqueOrThrow({
      where: {
        id: id,
      },
    });

    return warehouse;
  }

  async createWarehouse(createWarehouseDto: CreateWarehouseDto) {
    const warehouse = await this.prisma.warehouse.create({
      data: createWarehouseDto,
    });

    return warehouse;
  }

  async updateWarehouse(updateWarehouseDto: UpdateWarehouseDto, id: number) {
    const warehouse = await this.prisma.warehouse.update({
      where: {
        id: id,
      },
      data: updateWarehouseDto,
    });

    return warehouse;
  }

  async deleteWarehouse(id: number) {
    const warehouse = await this.prisma.warehouse.delete({
      where: {
        id: id,
      },
    });
    return warehouse;
  }

  async getNearestWarehouse(
    userLocation: { lat: number; long: number },
    warehouseType: WarehouseType,
  ): Promise<Warehouse | null> {
    const warehouses = await this.prisma.warehouse.findMany({
      where: {
        type: warehouseType,
        active: true,
      },
    });

    if (warehouses.length === 0) {
      return null;
    }

    let nearest = warehouses[0];
    let minDistance = calculateDistance(
      userLocation.lat,
      userLocation.long,
      parseFloat(nearest.lat),
      parseFloat(nearest.long),
    );

    for (const wh of warehouses) {
      const distance = calculateDistance(
        userLocation.lat,
        userLocation.long,
        parseFloat(wh.lat),
        parseFloat(wh.long),
      );
      if (distance < minDistance) {
        nearest = wh;
        minDistance = distance;
      }
    }

    return nearest;
  }

  async assignWarehousesToStaff(dto: AssignWarehousesToStaffDto) {
    const user = await this.prisma.user.findUniqueOrThrow({
      where: {
        id: dto.userId,
      },
    });

    await this.prisma.warehouseStaff.deleteMany({
      where: { userId: dto.userId },
    });

    const assignments = await this.prisma.warehouseStaff.createManyAndReturn({
      data: dto.warehouseIds.map((warehouseId) => ({
        userId: dto.userId,
        warehouseId,
      })),
      skipDuplicates: true,
      include: {
        warehouse: true,
      },
    });

    const { password: _, ...userSafe } = user;

    return {
      user: userSafe,
      warehouses: assignments.map((assignment) => assignment.warehouse),
    };
  }
}
