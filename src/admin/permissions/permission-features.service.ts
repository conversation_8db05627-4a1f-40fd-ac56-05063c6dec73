import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class PermissionFeaturesService {
  constructor(private readonly prisma: PrismaService) {}

  async findAll() {
    return this.prisma.permissionFeature.findMany({
      orderBy: { name: 'asc' },
    });
  }

  async findOne(id: number) {
    return this.prisma.permissionFeature.findUniqueOrThrow({
      where: { id },
      include: {
        rolePermissions: {
          include: {
            role: true,
          },
        },
      },
    });
  }
}
