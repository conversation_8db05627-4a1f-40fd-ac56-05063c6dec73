import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  UsePipes,
} from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { Admin } from 'src/auth/auth.decorator';
import { RolesService } from './roles.service';
import { CreateRoleDto, UpdateRoleDto, AssignPermissionsDto, AssignRolesToUserDto } from './roles.schema';

@Controller('roles')
@UsePipes(ZodValidationPipe)
export class RolesController {
  constructor(private readonly service: RolesService) {}

  @Post()
  @Admin()
  create(@Body() createDto: CreateRoleDto) {
    return this.service.create(createDto);
  }

  @Get()
  @Admin()
  findAll() {
    return this.service.findAll();
  }

  @Get(':id')
  @Admin()
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.service.findOne(id);
  }

  @Patch(':id')
  @Admin()
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateRoleDto,
  ) {
    return this.service.update(id, updateDto);
  }

  @Delete(':id')
  @Admin()
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.service.remove(id);
  }

  @Post(':id/permissions')
  @Admin()
  assignPermissions(
    @Param('id', ParseIntPipe) roleId: number,
    @Body() assignDto: Omit<AssignPermissionsDto, 'roleId'>,
  ) {
    return this.service.assignPermissions({ roleId, ...assignDto });
  }

  @Post('assign-to-user')
  @Admin()
  assignRolesToUser(@Body() assignDto: AssignRolesToUserDto) {
    return this.service.assignRolesToUser(assignDto);
  }

  @Get('user/:userId')
  @Admin()
  getUserRoles(@Param('userId', ParseIntPipe) userId: number) {
    return this.service.getUserRoles(userId);
  }
}
