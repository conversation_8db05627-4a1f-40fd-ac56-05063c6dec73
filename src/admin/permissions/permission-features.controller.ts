import { Controller, Get, Param, ParseIntPipe } from '@nestjs/common';
import { Admin } from 'src/auth/auth.decorator';
import { PermissionFeaturesService } from './permission-features.service';

@Controller('permission-features')
export class PermissionFeaturesController {
  constructor(private readonly service: PermissionFeaturesService) {}

  @Get()
  @Admin()
  findAll() {
    return this.service.findAll();
  }

  @Get(':id')
  @Admin()
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.service.findOne(id);
  }
}
