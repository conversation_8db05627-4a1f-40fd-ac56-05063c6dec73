import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { Context } from 'src/context';
import {
  CreateRoleDto,
  UpdateRoleDto,
  AssignPermissionsDto,
  AssignRolesToUserDto,
} from './roles.schema';

@Injectable()
export class RolesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly context: Context,
  ) {}

  async create(createDto: CreateRoleDto) {
    return this.prisma.role.create({
      data: createDto,
    });
  }

  async findAll() {
    return this.prisma.role.findMany({
      orderBy: { name: 'asc' },
      include: {
        permissions: {
          include: {
            feature: true,
          },
        },
        _count: {
          select: {
            userRoles: true,
          },
        },
      },
    });
  }

  async findOne(id: number) {
    return this.prisma.role.findUniqueOrThrow({
      where: { id },
      include: {
        permissions: {
          include: {
            feature: true,
          },
        },
        userRoles: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                type: true,
              },
            },
          },
        },
      },
    });
  }

  async update(id: number, updateDto: UpdateRoleDto) {
    return this.prisma.role.update({
      where: { id },
      data: updateDto,
    });
  }

  async remove(id: number) {
    return this.prisma.role.delete({
      where: { id },
    });
  }

  async assignPermissions(assignDto: AssignPermissionsDto) {
    const { roleId, permissions } = assignDto;

    return this.prisma.$transaction(async (tx) => {
      // Remove existing permissions for this role
      await tx.rolePermission.deleteMany({
        where: { roleId },
      });

      // Add new permissions
      if (permissions.length > 0) {
        await tx.rolePermission.createMany({
          data: permissions.map((permission) => ({
            roleId,
            featureId: permission.featureId,
            accessLevel: permission.accessLevel,
          })),
        });
      }

      return tx.role.findUnique({
        where: { id: roleId },
        include: {
          permissions: {
            include: {
              feature: true,
            },
          },
        },
      });
    });
  }

  async assignRolesToUser(assignDto: AssignRolesToUserDto) {
    const { userId, roleIds } = assignDto;

    return this.prisma.$transaction(async (tx) => {
      // Remove existing roles for this user
      await tx.userRole.deleteMany({
        where: { userId },
      });

      // Add new roles
      if (roleIds.length > 0) {
        await tx.userRole.createMany({
          data: roleIds.map((roleId) => ({
            userId,
            roleId,
          })),
        });
      }

      return tx.user.findUnique({
        where: { id: userId },
        include: {
          userRoles: {
            include: {
              role: true,
            },
          },
        },
      });
    });
  }

  async getUserRoles(userId: number) {
    return this.prisma.userRole.findMany({
      where: { userId },
      include: {
        role: {
          include: {
            permissions: {
              include: {
                feature: true,
              },
            },
          },
        },
      },
    });
  }
}
