import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';
import { AccessLevel } from '@prisma/client';

const createRoleSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  isSystemRole: z.boolean().default(false),
  active: z.boolean().default(true),
});

export class CreateRoleDto extends createZodDto(createRoleSchema) {}

const updateRoleSchema = createRoleSchema.partial();

export class UpdateRoleDto extends createZodDto(updateRoleSchema) {}

const rolePermissionSchema = z.object({
  featureId: z.number(),
  accessLevel: z.nativeEnum(AccessLevel),
});

const assignPermissionsSchema = z.object({
  roleId: z.number(),
  permissions: z.array(rolePermissionSchema),
});

export class AssignPermissionsDto extends createZodDto(assignPermissionsSchema) {}

const assignRolesToUserSchema = z.object({
  userId: z.number(),
  roleIds: z.array(z.number()),
});

export class AssignRolesToUserDto extends createZodDto(assignRolesToUserSchema) {}
