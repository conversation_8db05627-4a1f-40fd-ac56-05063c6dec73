import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { AccessLevel, UserType } from '@prisma/client';

@Injectable()
export class PermissionsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Check if a user has a specific access level for a feature
   * @param userId - The user ID to check
   * @param featureName - The name of the feature (e.g., "Order", "Product")
   * @param accessLevel - The access level to check (VIEW, MANAGE, DELETE)
   * @returns Promise<boolean> - Whether the user has the required permission
   */
  async hasPermission(
    userId: number,
    featureName: string,
    accessLevel: AccessLevel,
  ): Promise<boolean> {
    // First check if user has custom roles with this permission
    const userRolePermission = await this.prisma.userRole.findFirst({
      where: {
        userId,
        role: {
          active: true,
          permissions: {
            some: {
              feature: {
                name: featureName,
              },
              accessLevel,
            },
          },
        },
      },
    });

    if (userRolePermission) {
      return true;
    }

    // Fallback to UserType-based permissions for backward compatibility
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { type: true },
    });

    if (!user) {
      return false;
    }

    // Define default permissions for system roles
    return this.hasSystemRolePermission(user.type, featureName, accessLevel);
  }

  /**
   * Get all permissions for a user
   * @param userId - The user ID
   * @returns Promise with user's permissions grouped by feature
   */
  async getUserPermissions(userId: number) {
    const userRoles = await this.prisma.userRole.findMany({
      where: { userId },
      include: {
        role: {
          include: {
            permissions: {
              include: {
                feature: true,
              },
            },
          },
        },
      },
    });

    // Aggregate permissions from all roles
    const permissionMap = new Map<string, Set<AccessLevel>>();

    userRoles.forEach((userRole) => {
      userRole.role.permissions.forEach((permission) => {
        const featureName = permission.feature.name;
        if (!permissionMap.has(featureName)) {
          permissionMap.set(featureName, new Set());
        }
        permissionMap.get(featureName)!.add(permission.accessLevel);
      });
    });

    // Convert to object format
    const permissions: Record<string, AccessLevel[]> = {};
    permissionMap.forEach((accessLevels, featureName) => {
      permissions[featureName] = Array.from(accessLevels);
    });

    return permissions;
  }

  /**
   * Check if a user can perform any action on a feature
   * @param userId - The user ID
   * @param featureName - The feature name
   * @returns Promise<boolean> - Whether user has any permission for the feature
   */
  async hasAnyPermission(
    userId: number,
    featureName: string,
  ): Promise<boolean> {
    return (
      (await this.hasPermission(userId, featureName, AccessLevel.VIEW)) ||
      (await this.hasPermission(userId, featureName, AccessLevel.MANAGE)) ||
      (await this.hasPermission(userId, featureName, AccessLevel.DELETE))
    );
  }

  /**
   * Default system role permissions for backward compatibility
   */
  private hasSystemRolePermission(
    userType: UserType,
    featureName: string,
    accessLevel: AccessLevel,
  ): boolean {
    // Admin has all permissions
    if (userType === UserType.ADMIN) {
      return true;
    }

    // Define specific permissions for other user types
    const systemPermissions: Record<UserType, Record<string, AccessLevel[]>> = {
      [UserType.ADMIN]: {}, // Already handled above
      [UserType.CUSTOMER]: {}, // No admin permissions
      [UserType.WAREHOUSE_STAFF]: {
        Order: [AccessLevel.VIEW, AccessLevel.MANAGE],
        Inventory: [AccessLevel.VIEW, AccessLevel.MANAGE],
        Product: [AccessLevel.VIEW],
      },
      [UserType.DELIVERY_PERSON]: {
        Order: [AccessLevel.VIEW, AccessLevel.MANAGE],
      },
    };

    const userPermissions = systemPermissions[userType];
    const featurePermissions = userPermissions[featureName];

    return featurePermissions
      ? featurePermissions.includes(accessLevel)
      : false;
  }

  /**
   * Bulk check permissions for multiple features
   * @param userId - The user ID
   * @param checks - Array of {featureName, accessLevel} to check
   * @returns Promise<Record<string, boolean>> - Results keyed by "featureName:accessLevel"
   */
  async checkMultiplePermissions(
    userId: number,
    checks: Array<{ featureName: string; accessLevel: AccessLevel }>,
  ): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    for (const check of checks) {
      const key = `${check.featureName}:${check.accessLevel}`;
      results[key] = await this.hasPermission(
        userId,
        check.featureName,
        check.accessLevel,
      );
    }

    return results;
  }
}
