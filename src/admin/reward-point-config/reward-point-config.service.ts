import { Injectable } from '@nestjs/common';
import { RewardPointConfig } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
import { UpdateRewardPointConfigDto } from './reward-point-config.schema';

@Injectable()
export class RewardPointConfigService {
  constructor(private readonly prisma: PrismaService) {}

  async getRewardPointConfig(): Promise<RewardPointConfig | null> {
    const config = await this.prisma.rewardPointConfig.findFirst({});

    return config;
  }

  async updateRewardPointConfig(updateData: UpdateRewardPointConfigDto) {
    const existingConfig = await this.prisma.rewardPointConfig.findFirst({});
    if (!existingConfig) {
      return await this.prisma.rewardPointConfig.create({
        data: updateData,
      });
    } else {
      return await this.prisma.rewardPointConfig.update({
        where: {
          id: existingConfig.id,
        },
        data: updateData,
      });
    }
  }
}
