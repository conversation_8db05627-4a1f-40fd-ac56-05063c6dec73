import { Body, Controller, Get, Put } from '@nestjs/common';
import { UpdateRewardPointConfigDto } from './reward-point-config.schema';
import { RewardPointConfigService } from './reward-point-config.service';

@Controller('reward-point-config')
export class RewardPointConfigController {
  constructor(
    private readonly rewardPointConfigService: RewardPointConfigService,
  ) {}

  @Get()
  async getRewardPointConfig() {
    return this.rewardPointConfigService.getRewardPointConfig();
  }

  @Put()
  async updateRewardPointConfig(
    @Body() updateRewardPointConfigDto: UpdateRewardPointConfigDto,
  ) {
    return this.rewardPointConfigService.updateRewardPointConfig(
      updateRewardPointConfigDto,
    );
  }
}
