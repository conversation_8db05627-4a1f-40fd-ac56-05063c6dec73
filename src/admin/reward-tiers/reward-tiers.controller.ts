import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
} from '@nestjs/common';
import { EditRewardTierDto } from './reward-tiers.schema';
import { RewardTiersService } from './reward-tiers.service';

@Controller('reward-tiers')
export class RewardTiersController {
  constructor(private readonly service: RewardTiersService) {}

  @Get()
  findAll() {
    return this.service.findAll();
  }

  @Get(':id')
  findById(@Param('id', ParseIntPipe) id: number) {
    return this.service.findById(id);
  }

  // @Post()
  // create(@Body() dto: CreateRewardTierDto) {
  //   return this.service.create(dto);
  // }

  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: EditRewardTierDto,
  ) {
    return this.service.update(id, dto);
  }

  // @Delete(':id')
  // delete(@Param('id', ParseIntPipe) id: number) {
  //   return this.service.delete(id);
  // }
}
