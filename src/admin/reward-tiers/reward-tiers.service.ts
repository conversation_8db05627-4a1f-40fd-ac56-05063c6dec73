import { Injectable } from '@nestjs/common';
import { RewardTier } from '@prisma/client';
import { addDays, subDays } from 'date-fns';
import { Context } from 'src/context';
import { PrismaService } from 'src/prisma/prisma.service';
import { ROLLING_DAYS } from 'src/util/utils';
import { EditRewardTierDto } from './reward-tiers.schema';

@Injectable()
export class RewardTiersService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly context: Context,
  ) {}

  private rollingWindowStart(now: Date) {
    return subDays(now, ROLLING_DAYS); // date‑fns
  }

  async getRollingSpend(
    userId: number = this.context.user!.id,
    now: Date = new Date(),
  ): Promise<number> {
    const res = await this.prisma.order.aggregate({
      _sum: { totalAmount: true },
      where: {
        userId,
        paymentStatus: 'PAID',
        status: { in: ['CONFIRMED', 'SHIPPED', 'DELIVERED'] },
        createdAt: { gte: this.rollingWindowStart(now), lt: now },
      },
    });
    return res._sum.totalAmount?.toNumber() ?? 0;
  }

  findAll() {
    return this.prisma.rewardTier.findMany({
      orderBy: {
        requiredRollingSpend: 'asc',
      },
    });
  }

  findById(id: number) {
    return this.prisma.rewardTier.findUniqueOrThrow({
      where: {
        id: id,
      },
    });
  }

  // create(dto: CreateRewardTierDto) {
  //   return this.prisma.rewardTier.create({
  //     data: dto,
  //   });
  // }

  update(id: number, dto: EditRewardTierDto) {
    return this.prisma.rewardTier.update({ where: { id }, data: dto });
  }

  // delete(id: number) {
  //   return this.prisma.rewardTier.delete({ where: { id } });
  // }

  private async determineTier(
    userId: number,
    now: Date = new Date(),
  ): Promise<{
    tier: RewardTier | null;
    spend: number;
  }> {
    const spend = await this.getRollingSpend(userId, now);

    const tiers = await this.prisma.rewardTier.findMany({
      orderBy: { requiredRollingSpend: 'desc' },
    });

    const tier =
      tiers.find((t) => spend >= t.requiredRollingSpend.toNumber()) ?? null;
    return { tier, spend };
  }

  async handleQualifyingOrder(orderId: number): Promise<void> {
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      select: {
        id: true,
        userId: true,
        paymentStatus: true,
        status: true,
      },
    });
    if (
      !order ||
      order.paymentStatus !== 'PAID' ||
      !['CONFIRMED', 'READY', 'SHIPPED', 'DELIVERED'].includes(order.status)
    )
      return;

    const now = new Date();
    const { tier } = await this.determineTier(order.userId, now);
    if (!tier) return;

    const user = await this.prisma.user.findUniqueOrThrow({
      where: { id: order.userId },
      select: { rewardTierId: true },
    });

    if (user.rewardTierId === tier.id) return; // already at / above

    await this.prisma.user.update({
      where: { id: order.userId },
      data: {
        rewardTierId: tier.id,
        tierExpiresAt: addDays(now, ROLLING_DAYS),
      },
    });
  }
}
