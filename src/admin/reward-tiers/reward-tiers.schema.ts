import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';

export const RewardTierSchema = z.object({
  name: z.string(),
  requiredRollingSpend: z.number(),
  earnPercentage: z.number().min(0).max(100),
  redeemPercentage: z.number().min(0).max(100),
  maxDiscountValue: z.number(),
});

export const EditRewardTierSchema = RewardTierSchema.partial();

// export class CreateRewardTierDto extends createZodDto(RewardTierSchema) {}
export class EditRewardTierDto extends createZodDto(EditRewardTierSchema) {}
