import {
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseBoolPipe,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import { DeliveryDriversService } from './delivery-drivers.service';

@Controller('delivery-drivers')
export class DeliveryDriversController {
  constructor(
    private readonly deliveryDriversService: DeliveryDriversService,
  ) {}

  @Get()
  async getDeliveryDrivers(
    @Query('active', new DefaultValuePipe(true), ParseBoolPipe) active: boolean,
    @Query('search') search?: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize = 10,
    @Query('warehouseId', ParseIntPipe) warehouseId?: number,
  ) {
    return this.deliveryDriversService.getDeliveryDrivers({
      search,
      page: page,
      pageSize: pageSize,
      warehouseId,
      active: active,
    });
  }

  @Get(':id')
  async getDeliveryDriverPerformance(@Param('id', ParseIntPipe) id: number) {
    return this.deliveryDriversService.getDeliveryPerformance(id);
  }

  @Get(':id/location')
  async getDriverLocation(@Param('id', ParseIntPipe) id: number) {
    return this.deliveryDriversService.getDriverLastLocation(id);
  }

  @Get(':id/distance/:warehouseId')
  async getDriverDistanceFromWarehouse(
    @Param('id', ParseIntPipe) id: number,
    @Param('warehouseId', ParseIntPipe) warehouseId: number,
  ) {
    return this.deliveryDriversService.getDriverDistanceFromWarehouse(
      id,
      warehouseId,
    );
  }
}
