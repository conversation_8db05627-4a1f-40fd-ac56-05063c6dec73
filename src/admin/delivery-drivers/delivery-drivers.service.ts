import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
import { calculateDistance, combineDateAndTime } from 'src/util/utils';

@Injectable()
export class DeliveryDriversService {
  constructor(private readonly prisma: PrismaService) {}

  async getDeliveryDrivers({
    search,
    page,
    pageSize,
    warehouseId,
    active,
  }: {
    search?: string;
    page: number;
    pageSize: number;
    warehouseId?: number;
    active: boolean;
  }) {
    const where: Prisma.UserWhereInput = {
      type: 'DELIVERY_PERSON' as const,
      ...(search
        ? {
            OR: [
              { name: { contains: search, mode: 'insensitive' } },
              { email: { contains: search, mode: 'insensitive' } },
              { phone: { contains: search, mode: 'insensitive' } },
            ],
          }
        : {}),
      deliveryDriverProfile: {
        isActive: active,
      },
    };

    const [drivers, total] = await this.prisma.$transaction([
      this.prisma.user.findMany({
        where,
        select: {
          createdAt: true,
          updatedAt: true,
          countryId: true,
          id: true,
          name: true,
          email: true,
          phone: true,
          phoneCountry: true,
          profilePicture: true,
          type: true,
          firebaseToken: true,
          tierExpiresAt: true,
          rewardTier: true,
          rewardTierId: true,
          referralCode: true,
          referredById: true,
          deliveryDriverProfile: true,
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: [
          {
            deliveryDriverProfile: {
              isActive: 'desc',
            },
          },
          { id: 'desc' },
        ],
      }),
      this.prisma.user.count({ where }),
    ]);

    const driversWithPerformance = await Promise.all(
      drivers.map(async (driver) => {
        const [performance, lastLocation, distanceFromWarehouse] =
          await Promise.all([
            this.getDeliveryPerformance(driver.id),
            this.getDriverLastLocation(driver.id),
            warehouseId
              ? this.getDriverDistanceFromWarehouse(driver.id, warehouseId)
              : Promise.resolve(null),
          ]);

        // Create profile if it doesn't exist (for backward compatibility)
        let profile = driver.deliveryDriverProfile;
        if (!profile) {
          profile = await this.prisma.deliveryDriverProfile.create({
            data: {
              userId: driver.id,
              isActive: true,
            },
          });
        }

        return {
          ...driver,
          deliveryDriverProfile: profile,
          performance,
          lastLocation,
          distanceFromWarehouse,
        };
      }),
    );

    // Sort by distance if warehouse is specified (active drivers first, then by distance)
    if (warehouseId) {
      driversWithPerformance.sort((a, b) => {
        // Active drivers first
        const aActive = a.deliveryDriverProfile?.isActive ?? false;
        const bActive = b.deliveryDriverProfile?.isActive ?? false;

        if (aActive && !bActive) return -1;
        if (!aActive && bActive) return 1;

        // Both active or both inactive, sort by distance
        const distanceA = a.distanceFromWarehouse?.distance ?? Infinity;
        const distanceB = b.distanceFromWarehouse?.distance ?? Infinity;
        return distanceA - distanceB;
      });
    }

    return {
      data: driversWithPerformance,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getDeliveryPerformance(driverId: number): Promise<{
    totalOrderCount: number;
    avgPickupMins: number;
    avgDeliveryMins: number;
    lateDeliveryCount: number;
    avgLateMins: number;
  }> {
    const orders = await this.prisma.order.findMany({
      where: {
        deliveryDriverId: driverId,
        status: 'DELIVERED',
      },
      include: {
        orderStatusHistory: true,
      },
    });

    let pickupTotal = 0;
    let deliveryTotal = 0;
    let lateTotal = 0;
    let lateCount = 0;
    let count = 0;

    for (const order of orders) {
      const confirmed = order.orderStatusHistory.find(
        (s) => s.status === 'CONFIRMED',
      )?.createdAt;
      const shipped = order.orderStatusHistory.find(
        (s) => s.status === 'SHIPPED',
      )?.createdAt;
      const delivered = order.orderStatusHistory.find(
        (s) => s.status === 'DELIVERED',
      )?.createdAt;

      const deliveryDriverAssignedDate = order.deliveryDriverAssignedDate;

      if (!confirmed || !shipped || !delivered || !deliveryDriverAssignedDate)
        continue;

      const pickupTime =
        (shipped.getTime() - deliveryDriverAssignedDate.getTime()) / 60000;
      const deliveryTime = (delivered.getTime() - shipped.getTime()) / 60000;

      pickupTotal += pickupTime;
      deliveryTotal += deliveryTime;
      count++;

      const deliveryDeadline = combineDateAndTime(
        order.deliveryDate,
        order.deliveryEndTime,
      );
      const lateBy = (delivered.getTime() - deliveryDeadline.getTime()) / 60000;
      if (lateBy > 0) {
        lateCount++;
        lateTotal += lateBy;
      }
    }

    return {
      totalOrderCount: count,
      avgPickupMins: count ? Math.round(pickupTotal / count) : 0,
      avgDeliveryMins: count ? Math.round(deliveryTotal / count) : 0,
      lateDeliveryCount: lateCount,
      avgLateMins: lateCount ? Math.round(lateTotal / lateCount) : 0,
    };
  }

  async getDriverLastLocation(driverId: number) {
    const lastLocation = await this.prisma.deliveryDriverLocation.findFirst({
      where: {
        driverId,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    return lastLocation;
  }

  async getDriverDistanceFromWarehouse(driverId: number, warehouseId: number) {
    const [lastLocation, warehouse] = await Promise.all([
      this.getDriverLastLocation(driverId),
      this.prisma.warehouse.findUnique({
        where: { id: warehouseId },
      }),
    ]);

    if (!lastLocation || !warehouse) {
      return null;
    }

    const distance = calculateDistance(
      parseFloat(lastLocation.lat),
      parseFloat(lastLocation.long),
      parseFloat(warehouse.lat),
      parseFloat(warehouse.long),
    );

    return {
      distance: Math.round(distance * 100) / 100, // Round to 2 decimal places
      unit: 'km',
      lastUpdated: lastLocation.updatedAt,
    };
  }
}
