import { OrderStatus, PaymentStatus } from '@prisma/client';
import { createZodDto } from 'nestjs-zod';
import { timeRegex } from 'src/admin/delivery-slot/delivery-slot.schema';
import { z } from 'zod';

const UpdateOrderStatusSchema = z.object({
  orderStatus: z.nativeEnum(OrderStatus).optional(),
  paymentStatus: z.nativeEnum(PaymentStatus).optional(),
  deliveryDriverId: z.number().int().optional(),
});

const UpdateOrderDeliverySchema = z.object({
  deliveryDate: z.coerce.date(),
  deliveryStartTime: z.string().refine((value) => timeRegex.test(value), {
    message: 'deliveryStartTime must be in HH:mm format (00:00 - 23:59)',
  }),
  deliveryEndTime: z.string().refine((value) => timeRegex.test(value), {
    message: 'deliveryEndTime must be in HH:mm format (00:00 - 23:59)',
  }),
});

export class UpdateOrderStatusDto extends createZodDto(
  UpdateOrderStatusSchema,
) {}

export class UpdateOrderDeliveryDto extends createZodDto(
  UpdateOrderDeliverySchema,
) {}
