import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
  UsePipes,
} from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { AuthGuard } from 'src/auth/auth.guard';
import { CategoriesService } from './categories.service';
import {
  CategoryTranslationDto,
  CreateCategoryDto,
  EditCategoryDto,
} from './categories.schema';
import { CategoryType } from '@prisma/client';

@Controller('categories')
@UseGuards(AuthGuard)
@UsePipes(ZodValidationPipe)
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Get()
  async getCategories(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
    @Query('search') search?: string,
    @Query('type') type?: string,
  ) {
    return this.categoriesService.getCategories({
      page: page,
      pageSize: pageSize,
      search: search,
      type: type?.split(',').map((v) => v as CategoryType),
    });
  }

  @Get('/:id')
  async getCategoryById(@Param('id', ParseIntPipe) id: number) {
    return this.categoriesService.getCategoryById(id);
  }

  @Post()
  async createCategory(@Body() createCategoryDto: CreateCategoryDto) {
    return this.categoriesService.createCategory(createCategoryDto);
  }

  @Patch('/:id')
  async editCategory(
    @Param('id', ParseIntPipe) id: number,
    @Body() editCategoryDto: EditCategoryDto,
  ) {
    return this.categoriesService.editCategory(id, editCategoryDto);
  }

  @Delete('/:id')
  async deleteCategory(@Param('id', ParseIntPipe) id: number) {
    return this.categoriesService.deleteCategory(id);
  }

  @Get('/:id/translation')
  async getTranslations(@Param('id', ParseIntPipe) id: number) {
    return this.categoriesService.getTranslation(id);
  }

  @Post('/:id/translation')
  async addTranslation(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: CategoryTranslationDto,
  ) {
    return this.categoriesService.addTranslation(id, dto);
  }

  @Patch('/:id/translation')
  async updateTranslation(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: CategoryTranslationDto,
  ) {
    return this.categoriesService.updateTranslation(id, dto);
  }

  @Delete('/:id/translation/:languageId')
  async deleteTranslation(
    @Param('id', ParseIntPipe) id: number,
    @Param('languageId', ParseIntPipe) languageId: number,
  ) {
    return this.categoriesService.deleteTranslation(id, languageId);
  }
}
