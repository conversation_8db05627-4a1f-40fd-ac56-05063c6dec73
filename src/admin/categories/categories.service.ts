import {
  BadRequestException,
  ConflictException,
  Injectable,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  CategoryTranslationDto,
  CreateCategoryDto,
  EditCategoryDto,
} from './categories.schema';
import { CategoryType, Prisma } from '@prisma/client';
import { createPaginatedResponse } from 'src/util/utils';

@Injectable()
export class CategoriesService {
  constructor(private readonly prisma: PrismaService) {}

  async getCategories({
    page,
    pageSize,
    search,
    type,
  }: {
    page: number;
    pageSize: number;
    search?: string;
    type?: CategoryType[];
  }) {
    const [categories, total] = await this.prisma.$transaction(async (tx) => {
      const where: Prisma.CategoryWhereInput = {
        name: search
          ? {
              contains: search,
              mode: 'insensitive',
            }
          : undefined,
        type: {
          in: type,
        },
      };

      const categories = await tx.category.findMany({
        where: where,
        include: {
          parent: true,
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      });

      const total = await tx.category.count({
        where: where,
      });

      return [categories, total];
    });

    return createPaginatedResponse({
      data: categories,
      total,
      page,
      pageSize,
    });
  }

  async getCategoryById(id: number) {
    const categories = this.prisma.category.findUniqueOrThrow({
      where: {
        id: id,
      },
      include: {
        parent: true,
        children: true,
      },
    });

    return categories;
  }

  async createCategory(dto: CreateCategoryDto) {
    const checkSlug = await this.prisma.category.findUnique({
      where: {
        slug: dto.slug,
      },
    });

    if (checkSlug) {
      throw new ConflictException('Slug already exists');
    }

    if (dto.type === 'COLLECTION') {
      if (dto.parentId != null)
        throw new BadRequestException('Collection cannot have a parent');
    }

    if (dto.type === 'CATEGORY') {
      if (dto.parentId == null)
        throw new BadRequestException('Category must belong to a Collection');

      const parent = await this.prisma.category.findUniqueOrThrow({
        where: { id: dto.parentId },
      });
      if (parent.type !== 'COLLECTION')
        throw new BadRequestException('Parent must be a Collection');
    }

    if (dto.type === 'SEGMENT') {
      if (dto.parentId == null)
        throw new BadRequestException('Segment must belong to a Category');

      const parent = await this.prisma.category.findUniqueOrThrow({
        where: { id: dto.parentId },
      });
      if (parent.type !== 'CATEGORY')
        throw new BadRequestException('Parent must be a Category');

      if (!parent.parentId)
        throw new BadRequestException(
          'That Category must itself belong to a Collection',
        );
      const grandparent = await this.prisma.category.findUniqueOrThrow({
        where: { id: parent.parentId },
      });
      if (grandparent.type !== 'COLLECTION')
        throw new BadRequestException('Grandparent must be a Collection');
    }
    const category = await this.prisma.category.create({
      data: {
        name: dto.name,
        parentId: dto.parentId,
        type: dto.type,
        slug: dto.slug,
        bannerUrl: dto.bannerUrl,
        iconUrl: dto.iconUrl,
        isActive: dto.isActive,
      },
    });

    return category;
  }

  async editCategory(id: number, dto: EditCategoryDto) {
    const existing = await this.prisma.category.findUniqueOrThrow({
      where: { id },
    });

    if (dto.slug) {
      const checkSlug = await this.prisma.category.findUnique({
        where: {
          slug: dto.slug,
        },
      });

      if (checkSlug && checkSlug.id != id) {
        throw new ConflictException('Slug already exists');
      }
    }

    // ── Only parentId (and metadata) may change—never `type`
    if (dto.parentId !== undefined) {
      if (existing.type === 'COLLECTION') {
        if (dto.parentId != null)
          throw new BadRequestException('Collection cannot have a parent');
      }

      if (existing.type === 'CATEGORY') {
        if (dto.parentId == null)
          throw new BadRequestException('Category must belong to a Collection');

        const parent = await this.prisma.category.findUniqueOrThrow({
          where: { id: dto.parentId },
        });
        if (parent.type !== 'COLLECTION')
          throw new BadRequestException('Parent must be a Collection');
      }

      if (existing.type === 'SEGMENT') {
        if (dto.parentId == null)
          throw new BadRequestException('Segment must belong to a Category');

        const parent = await this.prisma.category.findUniqueOrThrow({
          where: { id: dto.parentId },
        });
        if (parent.type !== 'CATEGORY')
          throw new BadRequestException('Parent must be a Category');
      }
    }

    return this.prisma.category.update({
      where: { id },
      data: dto,
    });
  }

  async deleteCategory(id: number) {
    // First check if the category exists
    const category = await this.prisma.category.findUniqueOrThrow({
      where: { id },
      include: {
        products: {
          take: 1, // We only need to know if there are any products, not all of them
        },
      },
    });

    // Check if the category has associated products
    if (category.products.length > 0) {
      throw new BadRequestException(
        'Cannot delete category with associated products',
      );
    }

    // If no products are associated, proceed with deletion
    const deletedCategory = await this.prisma.category.delete({
      where: { id },
    });

    return deletedCategory;
  }

  async getTranslation(categoryId: number) {
    return this.prisma.categoryTranslation.findMany({
      where: {
        categoryId: categoryId,
      },
      include: {
        language: true,
      },
    });
  }

  async addTranslation(categoryId: number, dto: CategoryTranslationDto) {
    return this.prisma.categoryTranslation.create({
      data: {
        categoryId,
        languageId: dto.languageId,
        name: dto.name,
      },
      include: {
        language: true,
      },
    });
  }

  async updateTranslation(categoryId: number, dto: CategoryTranslationDto) {
    return this.prisma.categoryTranslation.upsert({
      where: {
        categoryId_languageId: {
          categoryId,
          languageId: dto.languageId,
        },
      },
      create: {
        categoryId,
        languageId: dto.languageId,
        name: dto.name,
      },
      update: {
        name: dto.name,
      },
      include: {
        language: true,
      },
    });
  }

  async deleteTranslation(categoryId: number, languageId: number) {
    return this.prisma.categoryTranslation.delete({
      where: {
        categoryId_languageId: {
          categoryId,
          languageId,
        },
      },
      include: {
        language: true,
      },
    });
  }
}
