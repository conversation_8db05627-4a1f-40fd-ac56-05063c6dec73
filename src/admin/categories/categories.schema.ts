import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';
import { CategoryType } from '@prisma/client';

export const CreateCategorySchema = z
  .object({
    name: z.string().min(1, 'Name is required'),
    parentId: z.number().optional().nullable(),
    slug: z.string().min(1, 'Slug is required'),
    isActive: z.boolean().default(true),
    type: z.nativeEnum(CategoryType),
    bannerUrl: z.string().url().optional(),
    iconUrl: z.string().url().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.type === CategoryType.COLLECTION) {
      if (data.parentId != null) {
        ctx.addIssue({
          path: ['parentId'],
          code: z.ZodIssueCode.custom,
          message: 'A COLLECTION cannot have a parent',
        });
      }
    }
    if (data.type === CategoryType.CATEGORY) {
      if (data.parentId == null) {
        ctx.addIssue({
          path: ['parentId'],
          code: z.ZodIssueCode.custom,
          message: 'A CATEGORY must have a parent COLLECTION',
        });
      }
    }
    if (data.type === CategoryType.SEGMENT) {
      if (data.parentId == null) {
        ctx.addIssue({
          path: ['parentId'],
          code: z.ZodIssueCode.custom,
          message: 'A SEGMENT must have a parent CATEGORY',
        });
      }
    }
  });

export const EditCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  parentId: z.number().optional().nullable(),
  slug: z.string().min(1, 'Slug is required').optional(),
  isActive: z.boolean().optional(),
  bannerUrl: z.string().url().optional(),
  iconUrl: z.string().url().optional(),
});

export class CreateCategoryDto extends createZodDto(CreateCategorySchema) {}
export class EditCategoryDto extends createZodDto(EditCategorySchema) {}

export const CategoryTranslationSchema = z.object({
  languageId: z.number().int().min(1),
  name: z.string().min(1, 'Translated name is required'),
});

export class CategoryTranslationDto extends createZodDto(
  CategoryTranslationSchema,
) {}
