import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
} from '@nestjs/common';
import { ProductPolicyService } from './product-policy.service';
import {
  CreateProductPolicy,
  CreateProductPolicyType,
  UpdateProductPolicy,
  UpdateProductPolicyType,
} from './product-policy.schema';

@Controller('product-policy')
export class ProductPolicyController {
  constructor(private readonly productPolicyService: ProductPolicyService) {}

  // Policy Type endpoints
  @Post('type')
  createType(@Body() dto: CreateProductPolicyType) {
    return this.productPolicyService.createType(dto);
  }

  @Get('type')
  findAllTypes() {
    return this.productPolicyService.findAllTypes();
  }

  @Get('type/:id')
  findOneType(@Param('id', ParseIntPipe) id: number) {
    return this.productPolicyService.findOneType(id);
  }

  @Patch('type/:id')
  updateType(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateProductPolicyType,
  ) {
    return this.productPolicyService.updateType(id, dto);
  }

  @Delete('type/:id')
  removeType(@Param('id', ParseIntPipe) id: number) {
    return this.productPolicyService.removeType(id);
  }

  // Policy endpoints
  @Post()
  create(@Body() dto: CreateProductPolicy) {
    return this.productPolicyService.create(dto);
  }

  @Get('product/:productId')
  findAllForProduct(@Param('productId', ParseIntPipe) productId: number) {
    return this.productPolicyService.findAllForProduct(productId);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.productPolicyService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateProductPolicy,
  ) {
    return this.productPolicyService.update(id, dto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.productPolicyService.remove(id);
  }
}
