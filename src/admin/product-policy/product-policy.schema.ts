import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

// Product Policy Type schemas
const createProductPolicyType = z.object({
  name: z.string(),
  icon: z.string().optional(),
});

export class CreateProductPolicyType extends createZodDto(
  createProductPolicyType,
) {}

const updateProductPolicyType = createProductPolicyType.partial();

export class UpdateProductPolicyType extends createZodDto(
  updateProductPolicyType,
) {}

// Product Policy schemas
const createProductPolicy = z.object({
  productId: z.number(),
  productPolicyTypeId: z.number(),
  description: z.string().nullable().optional(),
  details: z.record(z.string(), z.string()).default({}),
});

export class CreateProductPolicy extends createZodDto(createProductPolicy) {}

const updateProductPolicy = createProductPolicy.partial();

export class UpdateProductPolicy extends createZodDto(updateProductPolicy) {}
