import { Injectable, NotFoundException } from '@nestjs/common';
import { ProductPolicy, ProductPolicyType } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  CreateProductPolicy,
  CreateProductPolicyType,
  UpdateProductPolicy,
  UpdateProductPolicyType,
} from './product-policy.schema';

@Injectable()
export class ProductPolicyService {
  constructor(private readonly prisma: PrismaService) {}

  /* ─────────── Helpers ─────────── */
  private async getTypeOrThrow(id: number): Promise<ProductPolicyType> {
    const record = await this.prisma.productPolicyType.findUnique({
      where: { id },
    });
    if (!record) throw new NotFoundException(`Policy type ${id} not found`);
    return record;
  }

  private async getPolicyOrThrow(id: number): Promise<ProductPolicy> {
    const record = await this.prisma.productPolicy.findUnique({
      where: { id },
      include: { productPolicyType: true },
    });
    if (!record) throw new NotFoundException(`Policy ${id} not found`);
    return record;
  }

  /* ─────────── Policy Type CRUD ─────────── */
  async createType(dto: CreateProductPolicyType): Promise<ProductPolicyType> {
    return this.prisma.productPolicyType.create({ data: dto });
  }

  async findAllTypes(): Promise<ProductPolicyType[]> {
    return this.prisma.productPolicyType.findMany({
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOneType(id: number): Promise<ProductPolicyType> {
    return this.getTypeOrThrow(id);
  }

  async updateType(
    id: number,
    dto: UpdateProductPolicyType,
  ): Promise<ProductPolicyType> {
    await this.getTypeOrThrow(id); // ensure 404 if missing
    return this.prisma.productPolicyType.update({
      where: { id },
      data: dto,
    });
  }

  async removeType(id: number): Promise<ProductPolicyType> {
    await this.getTypeOrThrow(id); // ensure 404 if missing
    return this.prisma.productPolicyType.delete({ where: { id } });
  }

  /* ─────────── Policy CRUD ─────────── */
  async create(dto: CreateProductPolicy): Promise<ProductPolicy> {
    // Verify that the product and policy type exist
    const product = await this.prisma.product.findUnique({
      where: { id: dto.productId },
    });
    if (!product)
      throw new NotFoundException(`Product ${dto.productId} not found`);

    await this.getTypeOrThrow(dto.productPolicyTypeId);

    return this.prisma.productPolicy.create({
      data: {
        productId: dto.productId,
        productPolicyTypeId: dto.productPolicyTypeId,
        details: dto.details,
        description: dto.description,
      },
      include: { productPolicyType: true },
    });
  }

  async findAllForProduct(productId: number): Promise<ProductPolicy[]> {
    // Verify that the product exists
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
    });
    if (!product) throw new NotFoundException(`Product ${productId} not found`);

    return this.prisma.productPolicy.findMany({
      where: { productId },
      include: { productPolicyType: true },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: number): Promise<ProductPolicy> {
    return this.getPolicyOrThrow(id);
  }

  async update(id: number, dto: UpdateProductPolicy): Promise<ProductPolicy> {
    await this.getPolicyOrThrow(id); // ensure 404 if missing

    // If policy type is being updated, verify it exists
    if (dto.productPolicyTypeId) {
      await this.getTypeOrThrow(dto.productPolicyTypeId);
    }

    return this.prisma.productPolicy.update({
      where: { id },
      data: dto,
      include: { productPolicyType: true },
    });
  }

  async remove(id: number): Promise<ProductPolicy> {
    await this.getPolicyOrThrow(id); // ensure 404 if missing
    return this.prisma.productPolicy.delete({
      where: { id },
      include: { productPolicyType: true },
    });
  }
}
