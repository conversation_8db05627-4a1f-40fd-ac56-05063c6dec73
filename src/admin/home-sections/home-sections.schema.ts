import { z } from 'zod';
import { HomeSectionType } from '@prisma/client';
import { createZodDto } from 'nestjs-zod';

export const homeSectionCategoryInputSchema = z.object({
  categoryId: z.number().int(),
  displayOrder: z.number().int().min(0),
});

export const createHomeSectionSchema = z.object({
  title: z.string(),
  onlyDiscount: z.boolean(),
  type: z.nativeEnum(HomeSectionType),
  warehouseId: z.number().int().optional().nullable(),
  categories: z.array(homeSectionCategoryInputSchema),
  displayOrder: z.number().default(1),
});

export const updateHomeSectionSchema = createHomeSectionSchema.partial();

export const upsertTranslationSchema = z.object({
  languageId: z.number().int(),
  title: z.string(),
});

export const updateHomeSectionCategorySchema = z.object({
  id: z.string().cuid(),
  displayOrder: z.number().int().min(0),
});

export const addHomeSectionCategorySchema = z.object({
  categoryId: z.number().int(),
  displayOrder: z.number().int().min(0).default(0),
});

export class CreateHomeSectionDto extends createZodDto(
  createHomeSectionSchema,
) {}
export class UpdateHomeSectionDto extends createZodDto(
  updateHomeSectionSchema,
) {}
export class UpsertTranslationDto extends createZodDto(
  upsertTranslationSchema,
) {}

export class UpdateHomeSectionCategoryDto extends createZodDto(
  updateHomeSectionCategorySchema,
) {}
export class AddHomeSectionCategoryDto extends createZodDto(
  addHomeSectionCategorySchema,
) {}
