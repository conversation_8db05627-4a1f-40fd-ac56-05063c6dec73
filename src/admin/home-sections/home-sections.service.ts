import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { Prisma } from '@prisma/client';
import { createPaginatedResponse } from 'src/util/utils';
import {
  AddHomeSectionCategoryDto,
  CreateHomeSectionDto,
  UpdateHomeSectionDto,
  UpsertTranslationDto,
} from './home-sections.schema';

@Injectable()
export class HomeSectionsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateHomeSectionDto) {
    const homeSection = this.prisma.homeSection.create({
      data: {
        title: data.title,
        type: data.type,
        onlyDiscount: data.onlyDiscount,
        warehouseId: data.warehouseId,
        displayOrder: data.displayOrder,
        homeSectionCategory: {
          create: data.categories.map((cat) => ({
            categoryId: cat.categoryId,
            displayOrder: cat.displayOrder ?? 0,
          })),
        },
      },
      include: {
        homeSectionCategory: {
          include: {
            category: true,
          },
        },
        translations: true,
      },
    });

    return homeSection;
  }

  async findAll({
    page,
    pageSize,
    warehouseId,
  }: {
    page: number;
    pageSize: number;
    warehouseId?: number;
  }) {
    const where: Prisma.HomeSectionWhereInput = {
      warehouseId: warehouseId,
    };

    const [homeSections, total] = await this.prisma.$transaction([
      this.prisma.homeSection.findMany({
        where,
        include: {
          homeSectionCategory: true,
          translations: true,
          warehouse: true,
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: { displayOrder: 'asc' },
      }),
      this.prisma.homeSection.count({ where }),
    ]);

    return createPaginatedResponse({
      data: homeSections,
      total,
      page,
      pageSize,
    });
  }

  async findOne(id: string) {
    const section = await this.prisma.homeSection.findUnique({
      where: { id },
      include: {
        homeSectionCategory: {
          include: {
            category: true,
          },
        },
        translations: {
          include: {
            language: true,
          },
        },
        warehouse: true,
      },
    });
    if (!section) throw new NotFoundException('Home section not found');
    return section;
  }

  async update(id: string, data: UpdateHomeSectionDto) {
    return this.prisma.$transaction(async (tx) => {
      if (data.categories) {
        await tx.homeSectionCategory.deleteMany({
          where: { homeSectionId: id },
        });
        await tx.homeSectionCategory.createMany({
          data: data.categories.map((cat) => ({
            homeSectionId: id,
            categoryId: cat.categoryId,
            displayOrder: cat.displayOrder ?? 0,
          })),
        });
      }

      return tx.homeSection.update({
        where: { id },
        data: {
          title: data.title,
          onlyDiscount: data.onlyDiscount,
          type: data.type,
          warehouseId: data.warehouseId,
        },
      });
    });
  }

  async delete(id: string) {
    return this.prisma.homeSection.delete({ where: { id } });
  }

  async upsertTranslation(id: string, dto: UpsertTranslationDto) {
    return this.prisma.homeSectionTranslation.upsert({
      where: {
        homeSectionId_languageId: {
          homeSectionId: id,
          languageId: dto.languageId,
        },
      },
      update: { title: dto.title },
      create: {
        homeSectionId: id,
        languageId: dto.languageId,
        title: dto.title,
      },
    });
  }

  async deleteTranslation(sectionId: string, languageId: number) {
    return this.prisma.homeSectionTranslation.delete({
      where: {
        homeSectionId_languageId: {
          homeSectionId: sectionId,
          languageId,
        },
      },
    });
  }

  async updateHomeSectionCategory(
    homeSectionId: string,
    id: string,
    dto: UpdateHomeSectionDto,
  ) {
    return this.prisma.homeSectionCategory.update({
      where: { id: id, homeSectionId: homeSectionId },
      data: dto,
    });
  }

  async addHomeSectionCategory(
    homeSectionId: string,
    dto: AddHomeSectionCategoryDto,
  ) {
    return this.prisma.homeSectionCategory.create({
      data: {
        homeSectionId: homeSectionId,
        categoryId: dto.categoryId,
        displayOrder: dto.displayOrder ?? 0,
      },
    });
  }

  async deleteHomeSectionCategory(homeSectionId: string, id: string) {
    return this.prisma.homeSectionCategory.delete({
      where: { id, homeSectionId: homeSectionId },
    });
  }
}
