import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  Patch,
  Delete,
  Query,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { HomeSectionsService } from './home-sections.service';
import {
  AddHomeSectionCategoryDto,
  CreateHomeSectionDto,
  UpdateHomeSectionDto,
  UpsertTranslationDto,
} from './home-sections.schema';

@Controller('home-sections')
export class HomeSectionsController {
  constructor(private readonly service: HomeSectionsService) {}

  @Post()
  create(@Body() body: CreateHomeSectionDto) {
    return this.service.create(body);
  }

  @Get()
  findAll(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
    @Query('warehouseId', new DefaultValuePipe(0), ParseIntPipe)
    warehouseId: number,
  ) {
    return this.service.findAll({
      page,
      pageSize,
      warehouseId: warehouseId > 0 ? warehouseId : undefined,
    });
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.service.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() body: UpdateHomeSectionDto) {
    return this.service.update(id, body);
  }

  @Delete(':id')
  delete(@Param('id') id: string) {
    return this.service.delete(id);
  }

  @Patch(':homeSectionId/categories/:id')
  updateHomeSectionCategory(
    @Param('homeSectionId') homeSectionId: string,
    @Param('id') id: string,
    @Body() dto: UpdateHomeSectionDto,
  ) {
    return this.service.updateHomeSectionCategory(homeSectionId, id, dto);
  }

  @Post(':homeSectionId/categories')
  addHomeSectionCategory(
    @Param('homeSectionId') homeSectionId: string,
    @Body() body: AddHomeSectionCategoryDto,
  ) {
    return this.service.addHomeSectionCategory(homeSectionId, body);
  }

  @Delete(':homeSectionId/categories/:id')
  deleteHomeSectionCategory(
    @Param('homeSectionId') homeSectionId: string,
    @Param('id') id: string,
  ) {
    return this.service.deleteHomeSectionCategory(homeSectionId, id);
  }

  @Post(':id/translation')
  upsertTranslation(
    @Param('id') id: string,
    @Body() body: UpsertTranslationDto,
  ) {
    return this.service.upsertTranslation(id, body);
  }

  @Delete(':id/translation')
  deleteTranslation(
    @Param('id') id: string,
    @Query('languageId', ParseIntPipe) languageId: number,
  ) {
    return this.service.deleteTranslation(id, languageId);
  }
}
