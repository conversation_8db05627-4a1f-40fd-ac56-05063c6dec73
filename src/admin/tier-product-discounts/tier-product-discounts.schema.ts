import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';
import { AmountType } from '@prisma/client';

export const TierProductDiscountSchema = z.object({
  tierId: z.number().int().positive(),
  productId: z.number().int().positive(),
  discountValue: z.number().positive(),
  discountType: z.nativeEnum(AmountType),
  active: z.boolean().default(true),
});

export const EditTierProductDiscountSchema = z.object({
  discountValue: z.number().positive(),
  discountType: z.nativeEnum(AmountType),
  active: z.boolean().optional(),
});

export class CreateTierProductDiscountDto extends createZodDto(
  TierProductDiscountSchema,
) {}
export class EditTierProductDiscountDto extends createZodDto(
  EditTierProductDiscountSchema,
) {}
