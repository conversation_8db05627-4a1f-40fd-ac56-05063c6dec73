import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { TierProductDiscountsService } from './tier-product-discounts.service';
import {
  CreateTierProductDiscountDto,
  EditTierProductDiscountDto,
} from './tier-product-discounts.schema';
import { AuthGuard } from '../../auth/auth.guard';
import { Admin } from 'src/auth/auth.decorator';

@Controller('tier-product-discounts')
@UseGuards(AuthGuard)
@Admin()
export class TierProductDiscountsController {
  constructor(private readonly service: TierProductDiscountsService) {}

  @Get('tier/:tierId')
  findAllForTier(@Param('tierId', ParseIntPipe) tierId: number) {
    return this.service.findAllForTier(tierId);
  }

  @Get(':id')
  findById(@Param('id', ParseIntPipe) id: number) {
    return this.service.findById(id);
  }

  @Post()
  create(@Body() dto: CreateTierProductDiscountDto) {
    return this.service.create(dto);
  }

  @Patch(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: EditTierProductDiscountDto,
  ) {
    return this.service.update(id, dto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.service.remove(id);
  }

  @Patch(':id/toggle-status')
  toggleStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: { active: boolean },
  ) {
    return this.service.toggleStatus(id, dto.active);
  }
}
