import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import {
  CreateTierProductDiscountDto,
  EditTierProductDiscountDto,
} from './tier-product-discounts.schema';
import { Context } from '../../context';

@Injectable()
export class TierProductDiscountsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly context: Context,
  ) {}

  async findAllForTier(tierId: number) {
    // First check if the tier exists
    const tier = await this.prisma.rewardTier.findUnique({
      where: { id: tierId },
    });

    if (!tier) {
      throw new NotFoundException(`Reward tier with ID ${tierId} not found`);
    }

    // Get all discounts for this tier
    const discounts = await this.prisma.tierProductDiscount.findMany({
      where: { tierId },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            barcode: true,
            media: {
              select: {
                url: true,
              },
              take: 1,
            },
          },
        },
      },
    });

    // Filter out discounts where the product has been removed
    return discounts.filter((discount) => discount.product !== null);
  }

  async findById(id: number) {
    const discount = await this.prisma.tierProductDiscount.findUnique({
      where: { id },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            barcode: true,
            media: {
              select: {
                url: true,
              },
              take: 1,
            },
          },
        },
        tier: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!discount) {
      throw new NotFoundException(
        `Tier product discount with ID ${id} not found`,
      );
    }

    return discount;
  }

  async create(dto: CreateTierProductDiscountDto) {
    // Check if tier exists
    const tier = await this.prisma.rewardTier.findUnique({
      where: { id: dto.tierId },
    });

    if (!tier) {
      throw new NotFoundException(
        `Reward tier with ID ${dto.tierId} not found`,
      );
    }

    // Check if product exists
    const product = await this.prisma.product.findUnique({
      where: { id: dto.productId },
    });

    if (!product) {
      throw new NotFoundException(`Product with ID ${dto.productId} not found`);
    }

    // Check if a discount already exists for this tier-product combination
    const existingDiscount = await this.prisma.tierProductDiscount.findUnique({
      where: {
        tierId_productId: {
          tierId: dto.tierId,
          productId: dto.productId,
        },
      },
    });

    if (existingDiscount) {
      throw new Error(
        `A discount already exists for this tier-product combination`,
      );
    }

    // Create the discount with history in a transaction
    return this.prisma.$transaction(async (tx) => {
      // Create the discount
      const discount = await tx.tierProductDiscount.create({
        data: dto,
      });

      return discount;
    });
  }

  async update(id: number, dto: EditTierProductDiscountDto) {
    // Check if discount exists
    const existingDiscount = await this.prisma.tierProductDiscount.findUnique({
      where: { id },
    });

    if (!existingDiscount) {
      throw new NotFoundException(
        `Tier product discount with ID ${id} not found`,
      );
    }

    // Get the current user ID for history tracking
    const userId = this.context.user?.id;

    // If only updating the active status
    if (dto.active !== undefined && Object.keys(dto).length === 1) {
      return this.toggleStatus(id, dto.active, existingDiscount, userId);
    }

    // Update the discount with history in a transaction
    return this.prisma.tierProductDiscount.update({
      where: { id },
      data: dto,
    });
  }

  async toggleStatus(
    id: number,
    active: boolean,
    existingDiscount?: any,
    userId?: number,
  ) {
    // If existingDiscount is not provided, fetch it
    if (!existingDiscount) {
      existingDiscount = await this.prisma.tierProductDiscount.findUnique({
        where: { id },
      });

      if (!existingDiscount) {
        throw new NotFoundException(
          `Tier product discount with ID ${id} not found`,
        );
      }

      // If userId is not provided, get it from context
      if (!userId) {
        userId = this.context.user?.id;
      }
    }

    // Update the discount with history in a transaction
    return this.prisma.tierProductDiscount.update({
      where: { id },
      data: { active },
    });
  }

  async remove(id: number) {
    // Check if discount exists
    const existingDiscount = await this.prisma.tierProductDiscount.findUnique({
      where: { id },
    });

    if (!existingDiscount) {
      throw new NotFoundException(
        `Tier product discount with ID ${id} not found`,
      );
    }

    // Delete the discount with history in a transaction
    return this.prisma.tierProductDiscount.delete({
      where: { id },
    });
  }
}
