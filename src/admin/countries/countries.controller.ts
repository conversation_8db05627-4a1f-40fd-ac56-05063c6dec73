import {
  Controller,
  Get,
  Param,
  Post,
  Body,
  Patch,
  Delete,
  ParseIntPipe,
  UseGuards,
  UsePipes,
} from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { AuthGuard } from 'src/auth/auth.guard';
import { CountriesService } from './countries.service';
import { AddCountryDto, EditCountryDto } from './countries.schema';

@Controller('countries')
@UseGuards(AuthGuard)
@UsePipes(ZodValidationPipe)
export class CountriesController {
  constructor(private countriesService: CountriesService) {}

  @Get()
  async getCountries() {
    return this.countriesService.getCountries();
  }

  @Get('/:id')
  async getCountryById(@Param('id', ParseIntPipe) id: number) {
    return this.countriesService.getCountryById(id);
  }

  @Post()
  async addCountry(@Body() addCountryDto: AddCountryDto) {
    return this.countriesService.addCountry(addCountryDto);
  }

  @Patch('/:id')
  async editCountry(
    @Param('id', ParseIntPipe) id: number,
    @Body() editCountryDto: EditCountryDto,
  ) {
    return this.countriesService.editCountry(id, editCountryDto);
  }

  @Delete('/:id')
  async deleteCountry(@Param('id', ParseIntPipe) id: number) {
    return this.countriesService.deleteCountry(id);
  }
}
