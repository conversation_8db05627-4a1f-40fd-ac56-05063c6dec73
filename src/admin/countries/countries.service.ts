import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { AddCountryDto, EditCountryDto } from './countries.schema';

@Injectable()
export class CountriesService {
  constructor(private readonly prisma: PrismaService) {}

  async getCountries() {
    return this.prisma.country.findMany({
      orderBy: {
        name: 'asc',
      },
    });
  }

  async getCountryById(id: number) {
    const country = await this.prisma.country.findUniqueOrThrow({
      where: { id },
    });

    return country;
  }

  async addCountry(addCountryDto: AddCountryDto) {
    const country = await this.prisma.country.create({
      data: addCountryDto,
    });

    return country;
  }

  async editCountry(id: number, editCountryDto: EditCountryDto) {
    const country = await this.prisma.country.update({
      where: { id },
      data: editCountryDto,
    });

    return country;
  }

  async deleteCountry(id: number) {
    const country = await this.prisma.country.delete({
      where: { id },
    });

    return country;
  }
}
