import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const sendNotificationSchema = z
  .object({
    title: z.string().min(1, 'Title is required'),
    description: z.string().min(1, 'Description is required'),
    imageUrl: z.string().url().optional(),
    targetType: z.enum([
      'ALL_USERS',
      'REWARD_TIERS',
      'SPECIFIC_USER',
      'SPENDING_BASED',
    ]),
    rewardTierIds: z.array(z.number()).optional(),
    userId: z.number().optional(),
    spendingAmount: z.number().positive().optional(),
    spendingDays: z.number().positive().int().optional(),
  })
  .refine(
    (data) => {
      // Validate required fields based on target type
      if (data.targetType === 'REWARD_TIERS') {
        return data.rewardTierIds && data.rewardTierIds.length > 0;
      }
      if (data.targetType === 'SPECIFIC_USER') {
        return data.userId !== undefined;
      }
      if (data.targetType === 'SPENDING_BASED') {
        return (
          data.spendingAmount !== undefined && data.spendingDays !== undefined
        );
      }
      return true;
    },
    {
      message: 'Required fields missing for the selected target type',
      path: ['targetType'],
    },
  );

export class SendNotificationDto extends createZodDto(sendNotificationSchema) {}
