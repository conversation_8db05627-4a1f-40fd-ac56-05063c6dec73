import { Body, Controller, Get, Post, UsePipes } from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { NotificationService } from './notification.service';
import { SendNotificationDto } from './notification.schema';

@Controller('notifications')
@UsePipes(ZodValidationPipe)
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Post('send')
  async sendNotification(@Body() sendNotificationDto: SendNotificationDto) {
    return this.notificationService.sendNotification(sendNotificationDto);
  }

  @Get('reward-tiers')
  async getRewardTiers() {
    return this.notificationService.getRewardTiers();
  }
}
