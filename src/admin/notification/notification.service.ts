import { Injectable } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { PrismaService } from 'src/prisma/prisma.service';
import { SendNotificationDto } from './notification.schema';

@Injectable()
export class NotificationService {
  constructor(private readonly prisma: PrismaService) {}

  async sendToDevice({
    token,
    title,
    body,
    data,
    imageUrl,
  }: {
    token: string;
    title: string;
    body: string;
    data?: Record<string, string>;
    imageUrl?: string;
  }) {
    try {
      const message: admin.messaging.Message = {
        token,
        notification: {
          title,
          body,
          imageUrl,
        },
        data,
      };

      const response = await admin.messaging().send(message);
      return { success: true, response };
    } catch (err) {
      console.error('Error sending notification:', err);
      return { success: false, error: (err as Error).message };
    }
  }

  async sendNotification(sendNotificationDto: SendNotificationDto) {
    const {
      title,
      description,
      imageUrl,
      targetType,
      rewardTierIds,
      userId,
      spendingAmount,
      spendingDays,
    } = sendNotificationDto;

    let tokens: string[] = [];

    try {
      switch (targetType) {
        case 'ALL_USERS':
          tokens = await this.getAllUserTokens();
          break;

        case 'REWARD_TIERS':
          tokens = await this.getUserTokensByRewardTiers(rewardTierIds!);
          break;

        case 'SPECIFIC_USER':
          const token = await this.getUserTokenByUserId(userId!);
          if (token) {
            tokens = [token];
          }
          break;

        case 'SPENDING_BASED':
          tokens = await this.getUserTokensBySpending(
            spendingAmount!,
            spendingDays!,
          );
          break;

        default:
          return { success: false, error: 'Invalid target type' };
      }

      if (tokens.length === 0) {
        return {
          success: false,
          error: 'No users found with valid tokens for the specified criteria',
        };
      }

      const result = await this.sendToMultipleDevices({
        tokens,
        title,
        body: description,
        imageUrl,
      });

      return {
        ...result,
        targetedUsers: tokens.length,
      };
    } catch (error) {
      console.error('Error in sendNotification:', error);
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  async sendToMultipleDevices({
    tokens,
    title,
    body,
    data,
    imageUrl,
  }: {
    tokens: string[];
    title: string;
    body: string;
    data?: Record<string, string>;
    imageUrl?: string;
  }) {
    try {
      if (tokens.length === 0) {
        return {
          success: true,
          successCount: 0,
          failureCount: 0,
          responses: [],
        };
      }

      // Firebase supports up to 500 tokens per batch
      const batchSize = 500;
      const batches: string[][] = [];

      for (let i = 0; i < tokens.length; i += batchSize) {
        batches.push(tokens.slice(i, i + batchSize));
      }

      let totalSuccessCount = 0;
      let totalFailureCount = 0;
      const allResponses: any[] = [];

      for (const batch of batches) {
        const messages = batch.map((token: string) => ({
          token,
          notification: {
            title,
            body,
            imageUrl,
          },
          data,
        }));

        const response = await admin.messaging().sendEach(messages);
        totalSuccessCount += response.successCount;
        totalFailureCount += response.failureCount;
        allResponses.push(...response.responses);
      }

      return {
        success: true,
        successCount: totalSuccessCount,
        failureCount: totalFailureCount,
        responses: allResponses,
      };
    } catch (err) {
      console.error('Error sending notifications:', err);
      return { success: false, error: (err as Error).message };
    }
  }

  async getAllUserTokens(): Promise<string[]> {
    const users = await this.prisma.user.findMany({
      where: {
        firebaseToken: {
          not: null,
        },
      },
      select: {
        firebaseToken: true,
      },
    });

    return users
      .map((user) => user.firebaseToken)
      .filter((token): token is string => token !== null);
  }

  async getUserTokensByRewardTiers(tierIds: number[]): Promise<string[]> {
    const users = await this.prisma.user.findMany({
      where: {
        firebaseToken: {
          not: null,
        },
        rewardTierId: {
          in: tierIds,
        },
      },
      select: {
        firebaseToken: true,
      },
    });

    return users
      .map((user) => user.firebaseToken)
      .filter((token): token is string => token !== null);
  }

  async getUserTokenByUserId(userId: number): Promise<string | null> {
    const user = await this.prisma.user.findUnique({
      where: {
        id: userId,
      },
      select: {
        firebaseToken: true,
      },
    });

    return user?.firebaseToken || null;
  }

  async getUserTokensBySpending(
    amount: number,
    days: number,
  ): Promise<string[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const users = await this.prisma.user.findMany({
      where: {
        firebaseToken: {
          not: null,
        },
        orders: {
          some: {
            createdAt: {
              gte: startDate,
            },
            status: {
              in: ['DELIVERED'],
            },
          },
        },
      },
      select: {
        firebaseToken: true,
        orders: {
          where: {
            createdAt: {
              gte: startDate,
            },
            status: {
              in: ['DELIVERED'],
            },
          },
          select: {
            totalAmount: true,
          },
        },
      },
    });

    const filteredUsers = users.filter((user) => {
      const totalSpent = user.orders.reduce(
        (sum: number, order: any) => sum + Number(order.totalAmount),
        0,
      );
      return totalSpent >= amount;
    });

    return filteredUsers
      .map((user) => user.firebaseToken)
      .filter((token): token is string => token !== null);
  }

  async getRewardTiers() {
    return this.prisma.rewardTier.findMany({
      where: {
        type: {
          not: 'NONE',
        },
      },
      select: {
        id: true,
        name: true,
        type: true,
        _count: {
          select: {
            users: true,
          },
        },
      },
      orderBy: { requiredRollingSpend: 'asc' },
    });
  }
}
