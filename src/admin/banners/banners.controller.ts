import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards,
  UsePipes,
} from '@nestjs/common';
import { Admin } from 'src/auth/auth.decorator';
import { CreateBannerDto, UpdateBannerDto } from './banners.schema';
import { BannersService } from './banners.service';
import { ZodValidationPipe } from 'nestjs-zod';
import { AuthGuard } from 'src/auth/auth.guard';

@UseGuards(AuthGuard)
@UsePipes(ZodValidationPipe)
@Controller('banners')
export class BannersController {
  constructor(private readonly bannerService: BannersService) {}

  @Get()
  @Admin()
  async getBanners() {
    return this.bannerService.getBanners();
  }

  @Get(':id')
  @Admin()
  async getBannerById(@Param('id', ParseIntPipe) id: number) {
    return this.bannerService.getBannerById(id);
  }

  @Post()
  @Admin()
  async createBanner(@Body() createBannerDto: CreateBannerDto) {
    return this.bannerService.createBanner(createBannerDto);
  }

  @Put(':id')
  @Admin()
  async updateBanner(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateBannerDto: UpdateBannerDto,
  ) {
    return this.bannerService.updateBanner(id, updateBannerDto);
  }

  @Delete(':id')
  @Admin()
  async deleteBanner(@Param('id', ParseIntPipe) id: number) {
    return this.bannerService.deleteBanner(id);
  }
}
