import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateBannerDto, UpdateBannerDto } from './banners.schema';

@Injectable()
export class BannersService {
  constructor(private readonly prisma: PrismaService) {}

  async getBanners() {
    return this.prisma.banner.findMany({
      include: {
        media: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getBannerById(id: number) {
    const banner = await this.prisma.banner.findUnique({
      where: { id },
      include: {
        media: true,
      },
    });

    if (!banner) {
      throw new NotFoundException(`Banner with ID ${id} not found`);
    }

    return banner;
  }

  async createBanner(createBannerDto: CreateBannerDto) {
    const banner = await this.prisma.banner.create({
      data: {
        active: createBannerDto.active,
        media: {
          create: {
            url: createBannerDto.imageUrl,
            mediaType: 'IMAGE',
          },
        },
        language: createBannerDto.languageId
          ? {
              connect: {
                id: createBannerDto.languageId,
              },
            }
          : undefined,
      },
      include: {
        media: true,
      },
    });

    return banner;
  }

  async updateBanner(id: number, updateBannerDto: UpdateBannerDto) {
    // First, check if the banner exists
    const existingBanner = await this.getBannerById(id);

    // If updating the image URL, update the media record
    if (updateBannerDto.imageUrl) {
      await this.prisma.media.update({
        where: { id: existingBanner.mediaId },
        data: {
          url: updateBannerDto.imageUrl,
        },
      });
    }

    // Update the banner
    const banner = await this.prisma.banner.update({
      where: { id },
      data: {
        active:
          updateBannerDto.active !== undefined
            ? updateBannerDto.active
            : existingBanner.active,
        languageId: updateBannerDto.languageId,
      },
      include: {
        media: true,
      },
    });

    return banner;
  }

  async deleteBanner(id: number) {
    // First, check if the banner exists
    const existingBanner = await this.getBannerById(id);

    // Delete the banner
    await this.prisma.banner.delete({
      where: { id },
    });

    // Delete the associated media
    await this.prisma.media.delete({
      where: { id: existingBanner.mediaId },
    });

    return {
      success: true,
      message: `Banner with ID ${id} deleted successfully`,
    };
  }
}
