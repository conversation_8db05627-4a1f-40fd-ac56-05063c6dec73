import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';

export const CreateBannerSchema = z.object({
  imageUrl: z.string().url('Image URL must be a valid URL'),
  active: z.boolean().default(true),
  languageId: z.number().optional().nullable(),
});

export const UpdateBannerSchema = z.object({
  imageUrl: z.string().url('Image URL must be a valid URL').optional(),
  active: z.boolean().optional(),
  languageId: z.number().optional().nullable(),
});

export class CreateBannerDto extends createZodDto(CreateBannerSchema) {}
export class UpdateBannerDto extends createZodDto(UpdateBannerSchema) {}
