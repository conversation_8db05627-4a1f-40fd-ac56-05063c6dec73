import { HttpStatus, Injectable } from '@nestjs/common';
import { Exception } from 'src/exceptions';
import { CreateSupplierDto, UpdateSupplierDto } from './suppliers.schema';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class SuppliersService {
  constructor(private readonly prisma: PrismaService) {}

  async create(dto: CreateSupplierDto) {
    return this.prisma.supplier.create({
      data: dto,
    });
  }

  async findAll() {
    return this.prisma.supplier.findMany({
      orderBy: { createdAt: 'desc' },
      include: { country: true },
    });
  }

  async findOne(id: number) {
    const supplier = await this.prisma.supplier.findUnique({
      where: { id },
      include: { country: true },
    });

    if (!supplier)
      throw new Exception('Supplier not found', HttpStatus.NOT_FOUND);

    return supplier;
  }

  async update(id: number, dto: UpdateSupplierDto) {
    await this.findOne(id);

    return this.prisma.supplier.update({
      where: { id },
      data: dto,
    });
  }

  async remove(id: number) {
    await this.findOne(id);

    return this.prisma.supplier.delete({
      where: { id },
    });
  }
}
