import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const createSupplierSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email(),
  phone: z.string().min(5, 'Phone number is required'),
  address: z.string().min(1, 'Address is required'),
  countryId: z.number().int().positive('Country ID must be a positive integer'),
});

export const updateSupplierSchema = createSupplierSchema.partial();

export class CreateSupplierDto extends createZodDto(createSupplierSchema) {}
export class UpdateSupplierDto extends createZodDto(updateSupplierSchema) {}
