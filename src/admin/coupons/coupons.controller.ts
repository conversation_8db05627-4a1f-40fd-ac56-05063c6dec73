import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
  UsePipes,
} from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { AuthGuard } from 'src/auth/auth.guard';
import { CouponsService } from './coupons.service';
import { CreateCouponDto, UpdateCouponDto } from './coupons.schema';

@Controller('coupons')
@UseGuards(AuthGuard)
@UsePipes(ZodValidationPipe)
export class CouponsController {
  constructor(private readonly couponsService: CouponsService) {}

  @Get()
  async getCoupons(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
  ) {
    return this.couponsService.getCoupons(page, pageSize);
  }

  @Get(':id')
  async getCouponById(@Param('id', ParseIntPipe) id: number) {
    return this.couponsService.getCouponById(id);
  }

  @Post()
  async createCoupon(@Body() createCouponDto: CreateCouponDto) {
    return this.couponsService.createCoupon(createCouponDto);
  }

  @Patch(':id')
  async updateCoupon(
    @Body() updateCouponDto: UpdateCouponDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.couponsService.updateCoupon(updateCouponDto, id);
  }

  @Delete(':id')
  async deleteCoupon(@Param('id', ParseIntPipe) id: number) {
    return this.couponsService.deleteCoupon(id);
  }
}
