import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateCouponDto, UpdateCouponDto } from './coupons.schema';
import { Coupon } from '@prisma/client';

@Injectable()
export class CouponsService {
  constructor(private readonly prisma: PrismaService) {}

  async getCoupons(page: number, pageSize: number) {
    const [coupons, total] = await this.prisma.$transaction(async (tx) => {
      const coupons = await tx.coupon.findMany({
        orderBy: {
          createdAt: 'desc',
        },
      });
      const total = await tx.coupon.count({});

      return [coupons, total];
    });

    return {
      data: coupons,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getCouponById(id: number) {
    const coupon = await this.prisma.coupon.findUniqueOrThrow({
      where: {
        id: id,
      },
    });

    return coupon;
  }

  async createCoupon(createCouponDto: CreateCouponDto) {
    const coupon = await this.prisma.coupon.create({
      data: createCouponDto,
    });

    return coupon;
  }

  async updateCoupon(updateCouponDto: UpdateCouponDto, id: number) {
    const coupon = await this.prisma.coupon.update({
      where: {
        id: id,
      },
      data: updateCouponDto,
    });

    return coupon;
  }

  async deleteCoupon(id: number) {
    const coupon = await this.prisma.coupon.delete({
      where: {
        id: id,
      },
    });

    return coupon;
  }

  async validateCoupon(
    code: string,
    userId: number,
    orderAmount: number,
  ): Promise<Coupon> {
    // 1. Must exist
    const coupon = await this.prisma.coupon.findUnique({
      where: { code },
    });
    if (!coupon) {
      throw new BadRequestException('Coupon code not found');
    }

    // 2. Must be active
    if (!coupon.isActive) {
      throw new BadRequestException('This coupon is not active');
    }

    // 3. Must not be expired
    if (coupon.expiresAt && coupon.expiresAt < new Date()) {
      throw new BadRequestException('This coupon has expired');
    }

    // 4. Must meet minimum order value
    if (
      coupon.minOrderValue !== null &&
      orderAmount < Number(coupon.minOrderValue)
    ) {
      throw new BadRequestException(
        `Minimum order value for this coupon is ${coupon.minOrderValue}`,
      );
    }

    // 5. Global usage limit
    if (coupon.usageLimit !== null) {
      const totalUses = await this.prisma.order.count({
        where: { couponId: coupon.id },
      });
      if (totalUses >= coupon.usageLimit) {
        throw new BadRequestException(
          'This coupon has reached its usage limit',
        );
      }
    }

    // 6. Per-user usage limit
    if (coupon.perUserLimit !== null) {
      const userUses = await this.prisma.order.count({
        where: {
          couponId: coupon.id,
          userId: userId,
        },
      });
      if (userUses >= coupon.perUserLimit) {
        throw new BadRequestException(
          'You have already used this coupon the maximum number of times',
        );
      }
    }

    // All checks passed
    return coupon;
  }
}
