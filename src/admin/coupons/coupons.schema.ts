import { AmountType } from '@prisma/client';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const CreateCouponSchema = z.object({
  code: z.string().min(3, 'Coupon code must be at least 3 characters').max(50),
  discountValue: z
    .number()
    .positive('Discount value must be a positive number'),
  discountType: z.nativeEnum(AmountType),
  minOrderValue: z.number().positive().optional(),
  maxDiscount: z.number().positive().optional(),
  usageLimit: z.number().int().positive().optional(),
  perUserLimit: z.number().int().positive().optional(),
  expiresAt: z.string().optional(),
  isActive: z.boolean().default(true),
});

export class CreateCouponDto extends createZodDto(CreateCouponSchema) {}

export const UpdateCouponSchema = CreateCouponSchema.partial();

export class UpdateCouponDto extends createZodDto(UpdateCouponSchema) {}
