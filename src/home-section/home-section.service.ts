import { Injectable, NotFoundException } from '@nestjs/common';
import { Category, Prisma, WarehouseType } from '@prisma/client';
import { WarehouseService } from 'src/admin/warehouse/warehouse.service';
import { PrismaService } from 'src/prisma/prisma.service';
import { ProductTransformer } from 'src/transformers/product.transformer';
import { ProductQueryResult, ProductWithStockResponse } from 'src/types';

@Injectable()
export class HomeSectionService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly warehouseService: WarehouseService,
    private readonly productTransformer: ProductTransformer,
  ) {}

  async getHomeSections(params: {
    lang: string;
    lat: number;
    long: number;
    warehouseType: WarehouseType;
  }) {
    const language = await this.prisma.language.findUnique({
      where: { code: params.lang },
    });
    if (!language) throw new NotFoundException('Language not found');

    const warehouse = await this.warehouseService.getNearestWarehouse(
      {
        lat: params.lat,
        long: params.long,
      },
      params.warehouseType,
    );

    let sectionCountForWarehouse: number = 0;

    if (warehouse) {
      sectionCountForWarehouse = await this.prisma.homeSection.count({
        where: {
          warehouseId: warehouse.id,
        },
      });
    }

    const sections = await this.prisma.homeSection.findMany({
      where: {
        warehouseId:
          sectionCountForWarehouse > 0 ? (warehouse?.id ?? null) : null,
      },
      include: {
        homeSectionCategory: {
          include: {
            category: {
              include: {
                categoryTranslation: {
                  where: { languageId: language.id },
                  take: 1,
                },
              },
            },
          },
          orderBy: { displayOrder: 'asc' },
        },
        warehouse: true,
        translations: {
          where: { languageId: language.id },
          take: 1,
        },
      },
      orderBy: { displayOrder: 'asc' },
    });

    const sectionsWithData: (Prisma.HomeSectionGetPayload<object> & {
      categories: Category[] | undefined;
      products: ProductWithStockResponse[] | undefined;
    })[] = await Promise.all(
      sections.map(async (section) => {
        let categories: Category[] | undefined;
        let products: ProductWithStockResponse[] | undefined;

        if (section.type === 'CATEGORY') {
          categories = section.homeSectionCategory.map(
            (homeSectionCategory) => {
              return {
                ...homeSectionCategory.category,
                name:
                  homeSectionCategory.category.categoryTranslation[0]?.name ??
                  homeSectionCategory.category.name,
              };
            },
          );
        } else {
          const productResults: ProductQueryResult[] =
            await this.prisma.product.findMany({
              where: {
                categoryId: {
                  in: section.homeSectionCategory.map((hsc) => hsc.categoryId),
                },
              },
              include: {
                media: true,
                thumbnail: true,
                category: {
                  include: {
                    categoryTranslation: {
                      where: { languageId: language.id },
                      take: 1,
                    },
                  },
                },
                productTranslation: {
                  where: { languageId: language.id },
                  take: 1,
                },
                productPolicies: {
                  include: {
                    productPolicyType: true,
                  },
                },
              },
            });

          products = await Promise.all(
            productResults.map((product) =>
              this.productTransformer.getProductWithStockResponse(
                product,
                warehouse?.id,
              ),
            ),
          );
        }

        return {
          createdAt: section.createdAt,
          updatedAt: section.updatedAt,
          id: section.id,
          type: section.type,
          title: section.translations[0]?.title ?? section.title,
          onlyDiscount: section.onlyDiscount,
          displayOrder: section.displayOrder,
          warehouseId: section.warehouseId,
          warehouse: section.warehouse,
          categories: categories,
          products: products,
        };
      }),
    );

    return sectionsWithData;
  }
}
