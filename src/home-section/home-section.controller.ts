import { Controller, DefaultValuePipe, Get, Query } from '@nestjs/common';
import { HomeSectionService } from './home-section.service';
import { WarehouseType } from '@prisma/client';

@Controller('home-sections')
export class HomeSectionController {
  constructor(private readonly service: HomeSectionService) {}

  @Get()
  getHomeSections(
    @Query('lat') lat: string,
    @Query('long') long: string,
    @Query('warehouseType') warehouseType: WarehouseType,
    @Query('lang', new DefaultValuePipe('en')) lang: string,
  ) {
    return this.service.getHomeSections({
      lat: +lat,
      long: +long,
      warehouseType: warehouseType,
      lang: lang,
    });
  }
}
