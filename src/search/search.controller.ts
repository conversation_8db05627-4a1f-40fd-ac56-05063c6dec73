import {
  Controller,
  Get,
  Query,
  DefaultValuePipe,
  ParseIntPipe,
  ParseBoolPipe,
} from '@nestjs/common';
import { SearchService } from './search.service';

@Controller('search')
export class SearchController {
  constructor(private readonly searchService: SearchService) {}

  @Get('products')
  async searchProducts(
    @Query('query') query: string = '',
    @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(20), ParseIntPipe) pageSize: number,
    @Query('categoryId', new ParseIntPipe({ optional: true }))
    categoryId?: number,
    @Query('active', new ParseBoolPipe({ optional: true })) active?: boolean,
  ) {
    return this.searchService.searchProducts({
      query,
      page,
      pageSize,
      filters: {
        categoryId,
        active: active,
      },
    });
  }

  @Get('suggestions')
  async getSearchSuggestions(
    @Query('query') query: string = '',
    @Query('limit', new DefaultValuePipe(5), ParseIntPipe) limit: number,
  ) {
    return this.searchService.getSearchSuggestions(query, limit);
  }
}
