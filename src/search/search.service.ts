import { Injectable } from '@nestjs/common';
import { AlgoliaService } from '../algolia/algolia.service';
import { PrismaService } from '../prisma/prisma.service';

export interface SearchFilters {
  categoryId?: number;
  active?: boolean;
}

export interface ProductSearchParams {
  query: string;
  page: number;
  pageSize: number;
  filters?: SearchFilters;
}

export interface ProductSearchResult {
  hits: any[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  processingTimeMS: number;
  facets?: Record<string, Record<string, number>>;
}

@Injectable()
export class SearchService {
  constructor(
    private readonly algoliaService: AlgoliaService,
    private readonly prismaService: PrismaService,
  ) {}

  async searchProducts(
    params: ProductSearchParams,
  ): Promise<ProductSearchResult> {
    const { query, page, pageSize, filters = {} } = params;

    // Build Algolia filters
    const algoliaFilters = this.buildAlgoliaFilters(filters);
    const facetFilters = this.buildFacetFilters(filters);

    try {
      const result = await this.algoliaService.searchProducts(query, {
        page,
        hitsPerPage: pageSize,
        filters: algoliaFilters,
        facetFilters,
      });

      return {
        hits: result.hits,
        total: result.nbHits,
        page: result.page,
        pageSize: result.hitsPerPage,
        totalPages: result.nbPages,
        processingTimeMS: result.processingTimeMS,
      };
    } catch (error) {
      console.error(
        'Algolia search failed, falling back to database search:',
        error,
      );

      // Fallback to database search if Algolia fails
      return this.fallbackDatabaseSearch(params);
    }
  }

  async getSearchSuggestions(
    query: string,
    limit: number = 5,
  ): Promise<string[]> {
    if (!query || query.length < 2) {
      return [];
    }

    try {
      const result = await this.algoliaService.searchProducts(query, {
        hitsPerPage: limit,
        attributesToRetrieve: ['name', 'categoryName'],
      });

      // Extract unique suggestions from product names and categories
      const suggestions = new Set<string>();

      result.hits.forEach((hit) => {
        if (hit.name) {
          suggestions.add(hit.name);
        }
        if (hit.categoryName) {
          suggestions.add(hit.categoryName);
        }
      });

      return Array.from(suggestions).slice(0, limit);
    } catch (error) {
      console.error('Failed to get search suggestions:', error);
      return [];
    }
  }

  async searchCategories(query: string, page: number, pageSize: number) {
    const where = query
      ? {
          OR: [
            { name: { contains: query, mode: 'insensitive' as const } },
            { slug: { contains: query, mode: 'insensitive' as const } },
          ],
        }
      : {};

    const [categories, total] = await Promise.all([
      this.prismaService.category.findMany({
        where,
        skip: page * pageSize,
        take: pageSize,
        orderBy: { name: 'asc' },
      }),
      this.prismaService.category.count({ where }),
    ]);

    return {
      hits: categories,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  private buildAlgoliaFilters(filters: SearchFilters): string {
    const filterParts: string[] = [];

    if (filters.active !== undefined) {
      filterParts.push(`active:${filters.active}`);
    }

    if (filters.categoryId !== undefined) {
      filterParts.push(`categoryId:${filters.categoryId}`);
    }

    return filterParts.join(' AND ');
  }

  private buildFacetFilters(filters: SearchFilters): string[][] {
    const facetFilters: string[][] = [];

    // Add facet filters here if needed
    // Example: facetFilters.push(['category:Electronics', 'category:Books']);

    return facetFilters;
  }

  private async fallbackDatabaseSearch(
    params: ProductSearchParams,
  ): Promise<ProductSearchResult> {
    const { query, page, pageSize, filters = {} } = params;

    const where: any = {
      active: filters.active !== undefined ? filters.active : true,
    };

    if (query) {
      where.OR = [
        { name: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { barcode: { contains: query, mode: 'insensitive' } },
      ];
    }

    if (filters.categoryId) {
      where.categoryId = filters.categoryId;
    }

    const [products, total] = await Promise.all([
      this.prismaService.product.findMany({
        where,
        include: {
          category: true,
          media: true,
          thumbnail: true,
        },
        skip: page * pageSize,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
      }),
      this.prismaService.product.count({ where }),
    ]);

    // Transform products to match Algolia format
    const hits = products.map((product) =>
      AlgoliaService.transformProductToAlgoliaRecord(product),
    );

    return {
      hits,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
      processingTimeMS: 0,
    };
  }
}
