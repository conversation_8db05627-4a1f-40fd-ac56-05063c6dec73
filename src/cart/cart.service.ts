import { Injectable, NotFoundException } from '@nestjs/common';
import { Coupon, WarehouseType } from '@prisma/client';
import { InventoryService } from 'src/admin/inventories/inventories.service';
import { WarehouseService } from 'src/admin/warehouse/warehouse.service';
import { Context } from 'src/context';
import { Exception, NoWarehouseException } from 'src/exceptions';
import { PrismaService } from 'src/prisma/prisma.service';
import { ProductTransformer } from 'src/transformers/product.transformer';
import {
  CartItemResponse,
  CartResponse,
  ProductVariationResponse,
  UserSafe,
} from 'src/types';
import { AddCartItemDto } from './cart.schema';
import { RewardPointsService } from 'src/reward-points/reward-points.service';
import { isAllowedToOverrideUser } from 'src/util/utils';

@Injectable()
export class CartService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly context: Context,
    private readonly inventoryService: InventoryService,
    private readonly warehouseService: WarehouseService,
    private readonly productTransformer: ProductTransformer,
    private readonly rewardPointsService: RewardPointsService,
  ) {}

  async getCart({
    lat,
    long,
    warehouseType = 'GENERAL',
    couponCode,
    useRewardPoints = false,
    lang,
    overrideUser,
    overrideWarehouseId,
  }: {
    overrideUser?: UserSafe;
    lat: number;
    long: number;
    warehouseType: WarehouseType;
    couponCode?: string | null;
    useRewardPoints?: boolean;
    lang: string;
    overrideWarehouseId?: number;
  }): Promise<CartResponse> {
    isAllowedToOverrideUser(this.context.user!, overrideUser);

    const user = overrideUser ?? this.context.user!;
    const warehouse = overrideWarehouseId
      ? await this.warehouseService.getWarehouseById(overrideWarehouseId)
      : await this.warehouseService.getNearestWarehouse(
          { lat, long },
          warehouseType,
        );

    if (!warehouse) throw new Exception('Warehouse not found');

    const language = await this.prisma.language.findUniqueOrThrow({
      where: { code: lang },
    });

    const cartItems = await this.prisma.cart.findMany({
      where: { userId: user.id },
      include: {
        product: {
          include: {
            media: true,
            thumbnail: true,
            category: {
              include: {
                categoryTranslation: {
                  where: { languageId: language.id },
                  take: 1,
                },
              },
            },
            productTranslation: {
              where: { languageId: language.id },
              take: 1,
            },
            productPolicies: {
              include: {
                productPolicyType: true,
              },
            },
          },
        },
        variation: {
          include: {
            options: {
              include: {
                option: {
                  include: {
                    attribute: true,
                  },
                },
              },
            },
            media: true,
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });

    let subtotal = 0;
    // Subtotal for products without discounts - used for coupon calculations
    // Coupons should only apply to non-discounted products
    let nonDiscountedSubtotal = 0;
    const cartDetails = await Promise.all(
      cartItems.map(async (item) => {
        const product =
          await this.productTransformer.getProductWithStockResponse(
            item.product,
            warehouse.id,
          );

        // Transform variation to match ProductVariationResponse type
        let transformedVariation: ProductVariationResponse | null = null;
        let price: number;
        let originalPrice: number;
        let quantity: number;
        let inStock: boolean;

        if (item.variationId && item.variation) {
          // Get inventory for the specific variation to get stock information
          const inventory =
            await this.inventoryService.getProductInventoryByWarehouse(
              item.productId,
              warehouse.id,
              item.variationId,
            );

          // Get variation pricing
          quantity = inventory.quantity;
          inStock = inventory.quantity > 0;
          originalPrice = inventory.sellingPrice.toNumber();

          // Apply product discount to variation price if applicable
          if (product.discountType && product.discountValue) {
            price =
              product.discountType === 'PERCENTAGE'
                ? originalPrice * (1 - product.discountValue / 100)
                : originalPrice - product.discountValue;
          } else {
            price = originalPrice;
          }

          // Transform to match ProductVariationResponse type
          transformedVariation = {
            createdAt: item.variation.createdAt,
            updatedAt: item.variation.updatedAt,
            productId: item.variation.productId,
            id: item.variation.id,
            barcode: item.variation.barcode,
            quantity: quantity,
            inStock: inStock,
            price: price.toFixed(2),
            originalPrice: originalPrice.toFixed(2),
            options: item.variation.options.map((mapping) => ({
              option: {
                id: mapping.option.id,
                name: mapping.option.name,
                colorCode: mapping.option.colorCode,
                imageUrl: mapping.option.imageUrl,
                attribute: {
                  id: mapping.option.attribute?.id || 0,
                  name: mapping.option.attribute?.name || 'Unknown',
                },
              },
            })),
            media: item.variation.media,
          };
        } else {
          // Use base product pricing
          price = +product.price;
          originalPrice = +product.originalPrice;
          quantity = product.quantity;
          inStock = product.inStock;
        }

        // Calculate GST and totals
        const gstAmount = price * (product.gstPercentage / 100);

        const total = price * item.quantity;
        const originalTotal = originalPrice * item.quantity;

        subtotal += total;

        // Check if product has a discount
        const hasDiscount =
          item.product.discountValue !== null && item.product.discountValue > 0;

        // Add to non-discounted subtotal only if product doesn't have a discount
        if (!hasDiscount) {
          nonDiscountedSubtotal += total;
        }

        return {
          ...item,
          product: product,
          itemTotal: total.toFixed(2),
          originalTotal: originalTotal.toFixed(2),
          gstAmount: gstAmount.toFixed(2),
          variation: transformedVariation,
        };
      }),
    );

    // --- Handle coupon
    let coupon: Coupon | null = null;
    let couponDiscount = 0;
    let couponValid = false;

    if (couponCode) {
      coupon = await this.prisma.coupon.findUnique({
        where: { code: couponCode, isActive: true },
      });
      if (
        coupon &&
        coupon.isActive &&
        (!coupon.expiresAt || coupon.expiresAt > new Date())
      ) {
        // Check if there are any non-discounted products to apply coupon to
        // If all products have discounts, we won't apply any coupon discount
        if (nonDiscountedSubtotal > 0) {
          // Check minimum order value against the total subtotal (for eligibility)
          // but calculate discount based on non-discounted subtotal only
          if (
            !coupon.minOrderValue ||
            subtotal >= coupon.minOrderValue.toNumber()
          ) {
            const rawDiscount =
              coupon.discountType === 'PERCENTAGE'
                ? (nonDiscountedSubtotal * coupon.discountValue.toNumber()) /
                  100
                : Math.min(
                    coupon.discountValue.toNumber(),
                    nonDiscountedSubtotal,
                  );
            couponDiscount = Math.min(
              rawDiscount,
              coupon.maxDiscount?.toNumber() ?? rawDiscount,
            );
            couponValid = true;
          }
        }
      }
    }

    let rewardDiscount = 0;
    let rewardPointsUsed = 0;

    if (useRewardPoints && user.rewardTierId) {
      const userWithTier = await this.prisma.user.findUniqueOrThrow({
        where: { id: user.id },
        include: {
          rewardTier: true,
        },
      });

      const totalPoints = await this.rewardPointsService.getUserRewardPoints(
        userWithTier.id,
      );

      const tier = userWithTier.rewardTier!;
      // Unlike coupons, reward points apply to all products (including discounted ones)
      // If this behavior needs to change, replace subtotal with nonDiscountedSubtotal here
      const potentialDiscount = subtotal * (tier.redeemPercentage / 100);
      const maxDiscount = Math.min(
        potentialDiscount,
        tier.maxDiscountValue.toNumber(),
      );

      if (totalPoints >= 1) {
        rewardDiscount = Math.min(totalPoints, maxDiscount);
        rewardPointsUsed = rewardDiscount;
      }
    }

    const handlingCharge = 0;
    const deliveryFee = 0;

    const total =
      subtotal - couponDiscount - rewardDiscount + handlingCharge + deliveryFee;

    // Return the cart response with all calculated values
    // Note: couponDiscount is now calculated only on non-discounted products
    return {
      items: cartDetails,
      subtotal: subtotal.toFixed(2),
      handlingCharge: handlingCharge.toFixed(2),
      deliveryFee: deliveryFee.toFixed(2),
      couponCode: couponCode ?? null,
      couponValid,
      couponDiscount: couponDiscount.toFixed(2),
      useRewardPoints: useRewardPoints,
      rewardDiscount: rewardDiscount.toFixed(2),
      rewardPointsUsed,
      total: total.toFixed(2),
      canOrder: cartDetails.every((item) => {
        // If item has a variation, check variation stock
        if (item.variation) {
          return (
            item.variation.inStock && item.quantity <= item.variation.quantity
          );
        }
        // Otherwise check product stock
        return item.product.inStock && item.quantity <= item.product.quantity;
      }),
    } satisfies CartResponse;
  }

  async addCartItem({
    addCartItemDto,
    lang,
    lat,
    long,
    warehouseType,
    overrideUser,
    overrideWarehouseId,
  }: {
    overrideUser?: UserSafe;
    addCartItemDto: AddCartItemDto;
    lat: number;
    long: number;
    warehouseType: WarehouseType;
    lang: string;
    overrideWarehouseId?: number;
  }): Promise<CartItemResponse> {
    isAllowedToOverrideUser(this.context.user!, overrideUser);

    const user = overrideUser ?? this.context.user!;
    const { productId, quantity, variationId } = addCartItemDto;

    const warehouse = overrideWarehouseId
      ? await this.warehouseService.getWarehouseById(overrideWarehouseId)
      : await this.warehouseService.getNearestWarehouse(
          {
            lat: lat,
            long: long,
          },
          warehouseType,
        );

    if (!warehouse) {
      throw new NoWarehouseException();
    }

    const language = await this.prisma.language.findUniqueOrThrow({
      where: { code: lang },
    });

    // Check if product exists and if it has variations
    const productEntity = await this.prisma.product.findUniqueOrThrow({
      where: { id: productId },
    });

    // If product has variations, a variationId is required
    if (productEntity.hasVariations && !variationId) {
      throw new Exception(
        'This product has variations. Please select a specific variation.',
      );
    }

    // Check maxCartQuantity restriction
    if (productEntity.maxCartQuantity) {
      if (quantity > productEntity.maxCartQuantity) {
        throw new Exception(
          `Maximum ${productEntity.maxCartQuantity} items allowed in cart for this product`,
        );
      }
    }

    // Get inventory based on whether a variation is specified
    const inventory =
      await this.inventoryService.getProductInventoryByWarehouse(
        productId,
        warehouse.id,
        variationId ?? undefined,
      );

    if (inventory.quantity < quantity) {
      throw new Exception('Requested quantity is not available in stock');
    }

    const cartItem = await this.prisma.$transaction(async (tx) => {
      // Include relations for the response
      const include = {
        product: {
          include: {
            media: true,
            thumbnail: true,
            category: {
              include: {
                categoryTranslation: {
                  where: { languageId: language.id },
                  take: 1,
                },
              },
            },
            productTranslation: {
              where: { languageId: language.id },
              take: 1,
            },
          },
        },
        variation: variationId
          ? {
              include: {
                options: {
                  include: {
                    option: {
                      include: {
                        attribute: true,
                      },
                    },
                  },
                },
                media: true,
              },
            }
          : undefined,
      };

      // Find existing cart item
      const existingCartItem = await tx.cart.findFirst({
        where: {
          userId: user.id,
          productId,
          variationId: variationId ?? null,
        },
      });

      let cartItem: any;

      if (existingCartItem) {
        // If quantity is 0, delete the item
        if (quantity === 0) {
          cartItem = await tx.cart.delete({
            where: {
              id: existingCartItem.id,
            },
            include,
          });
          cartItem.quantity = 0;
        } else {
          // Update existing item
          cartItem = await tx.cart.update({
            where: {
              id: existingCartItem.id,
            },
            data: {
              quantity,
            },
            include,
          });
        }
      } else {
        // Create new item if it doesn't exist and quantity > 0
        if (quantity > 0) {
          cartItem = await tx.cart.create({
            data: {
              userId: user.id,
              productId,
              variationId: variationId ?? null,
              quantity,
            },
            include,
          });
        } else {
          // If quantity is 0 and item doesn't exist, return empty cart item
          // This is an edge case but we should handle it
          const product = await tx.product.findUnique({
            where: { id: productId },
            include: include.product.include,
          });

          let variation: any = null;
          if (variationId) {
            variation = await tx.productVariation.findUnique({
              where: { id: variationId },
              include: include.variation?.include,
            });
          }

          cartItem = {
            id: -1, // Dummy ID
            userId: user.id,
            productId,
            variationId: variationId ?? null,
            quantity: 0,
            createdAt: new Date(),
            updatedAt: new Date(),
            product,
            variation,
          };
        }
      }

      return cartItem;
    });

    // Transform the product with stock information
    const transformedProduct =
      await this.productTransformer.getProductWithStockResponse(
        cartItem.product,
        warehouse.id,
      );

    // If there's a variation, we need to fetch its complete data
    let transformedVariation: ProductVariationResponse | null = null;
    // Initialize with default values from the product
    let itemPrice: number = +transformedProduct.price;
    let itemOriginalPrice: number = +transformedProduct.originalPrice;
    let itemQuantityInStock: number = transformedProduct.quantity;
    let itemInStock: boolean = transformedProduct.inStock;

    if (cartItem.variationId) {
      // Get inventory for the specific variation to get stock information
      const inventory =
        await this.inventoryService.getProductInventoryByWarehouse(
          cartItem.productId,
          warehouse.id,
          cartItem.variationId,
        );

      const variationDetails = await this.prisma.productVariation.findUnique({
        where: { id: cartItem.variationId },
        include: {
          options: {
            include: {
              option: {
                include: {
                  attribute: true,
                },
              },
            },
          },
          media: true,
        },
      });

      if (variationDetails) {
        // Get variation pricing
        itemQuantityInStock = inventory.quantity;
        itemInStock = inventory.quantity > 0;
        itemOriginalPrice = inventory.sellingPrice.toNumber();

        // Apply product discount to variation price if applicable
        if (
          transformedProduct.discountType &&
          transformedProduct.discountValue
        ) {
          itemPrice =
            transformedProduct.discountType === 'PERCENTAGE'
              ? itemOriginalPrice * (1 - transformedProduct.discountValue / 100)
              : itemOriginalPrice - transformedProduct.discountValue;
        } else {
          itemPrice = itemOriginalPrice;
        }

        // Transform to match ProductVariationResponse type
        transformedVariation = {
          createdAt: variationDetails.createdAt,
          updatedAt: variationDetails.updatedAt,
          productId: variationDetails.productId,
          id: variationDetails.id,
          barcode: variationDetails.barcode,
          quantity: itemQuantityInStock,
          inStock: itemInStock,
          price: itemPrice.toFixed(2),
          originalPrice: itemOriginalPrice.toFixed(2),
          options: variationDetails.options.map((mapping) => ({
            option: {
              id: mapping.option.id,
              name: mapping.option.name,
              colorCode: mapping.option.colorCode,
              imageUrl: mapping.option.imageUrl,
              attribute: {
                id: mapping.option.attribute?.id || 0,
                name: mapping.option.attribute?.name || 'Unknown',
              },
            },
          })),
          media: variationDetails.media,
        };
      }
    } else {
      // Use base product pricing
      itemPrice = +transformedProduct.price;
      itemOriginalPrice = +transformedProduct.originalPrice;
      itemQuantityInStock = transformedProduct.quantity;
      itemInStock = transformedProduct.inStock;
    }

    // Calculate GST and totals
    const gstAmount = itemPrice * (transformedProduct.gstPercentage / 100);

    const total = itemPrice * cartItem.quantity;
    const originalTotal = itemOriginalPrice * cartItem.quantity;

    const cartItemWithDetails = {
      ...cartItem,
      product: transformedProduct,
      itemTotal: total.toFixed(2),
      gstAmount: gstAmount.toFixed(2),
      originalTotal: originalTotal.toFixed(2),
      variation: transformedVariation,
    } satisfies CartItemResponse;

    return cartItemWithDetails;
  }

  async deleteCartItem({
    id,
    lat,
    long,
    warehouseType,
    lang,
    overrideWarehouseId,
  }: {
    id: number;
    lang: string;
    lat: number;
    long: number;
    warehouseType: WarehouseType;
    overrideWarehouseId?: number;
  }): Promise<CartItemResponse> {
    const warehouse = overrideWarehouseId
      ? await this.warehouseService.getWarehouseById(overrideWarehouseId)
      : await this.warehouseService.getNearestWarehouse(
          {
            lat: lat,
            long: long,
          },
          warehouseType,
        );

    if (!warehouse) {
      throw new NoWarehouseException();
    }

    const language = await this.prisma.language.findUniqueOrThrow({
      where: { code: lang },
    });

    const checkCartItem = await this.prisma.cart.findUnique({
      where: {
        id: id,
      },
    });

    if (!checkCartItem) {
      throw new NotFoundException('Cart item not found');
    }

    const cartItem = await this.prisma.cart.delete({
      where: {
        id: id,
      },
      include: {
        product: {
          include: {
            media: true,
            thumbnail: true,
            category: {
              include: {
                categoryTranslation: {
                  where: { languageId: language.id },
                  take: 1,
                },
              },
            },
            productTranslation: {
              where: { languageId: language.id },
              take: 1,
            },
            productPolicies: {
              include: {
                productPolicyType: true,
              },
            },
          },
        },
        variation: {
          include: {
            options: {
              include: {
                option: {
                  include: {
                    attribute: true,
                  },
                },
              },
            },
            media: true,
          },
        },
      },
    });

    cartItem.quantity = 0;

    const transformedProduct =
      await this.productTransformer.getProductWithStockResponse(
        cartItem.product,
        warehouse.id,
        cartItem.variationId || undefined,
      );

    // Transform variation to match ProductVariationResponse type
    let transformedVariation: ProductVariationResponse | null = null;
    if (cartItem.variationId && cartItem.variation) {
      // Get inventory for the specific variation to get stock information
      const inventory =
        await this.inventoryService.getProductInventoryByWarehouse(
          cartItem.productId,
          warehouse.id,
          cartItem.variationId,
        );

      // Get variation pricing
      const itemQuantityInStock = inventory.quantity;
      const itemInStock = inventory.quantity > 0;
      const itemOriginalPrice = inventory.sellingPrice.toNumber();

      // Apply product discount to variation price if applicable
      let itemPrice = itemOriginalPrice;
      if (transformedProduct.discountType && transformedProduct.discountValue) {
        itemPrice =
          transformedProduct.discountType === 'PERCENTAGE'
            ? itemOriginalPrice * (1 - transformedProduct.discountValue / 100)
            : itemOriginalPrice - transformedProduct.discountValue;
      }

      // Transform to match ProductVariationResponse type
      transformedVariation = {
        createdAt: cartItem.variation.createdAt,
        updatedAt: cartItem.variation.updatedAt,
        productId: cartItem.variation.productId,
        id: cartItem.variation.id,
        barcode: cartItem.variation.barcode,
        quantity: itemQuantityInStock,
        inStock: itemInStock,
        price: itemPrice.toFixed(2),
        originalPrice: itemOriginalPrice.toFixed(2),
        options: cartItem.variation.options.map((mapping) => ({
          option: {
            id: mapping.option.id,
            name: mapping.option.name,
            colorCode: mapping.option.colorCode,
            imageUrl: mapping.option.imageUrl,
            attribute: {
              id: mapping.option.attribute?.id || 0,
              name: mapping.option.attribute?.name || 'Unknown',
            },
          },
        })),
        media: cartItem.variation.media,
      };
    }

    const cartItemWithDetails = {
      ...cartItem,
      product: transformedProduct,
      itemTotal: '0.00',
      originalTotal: '0.00',
      gstAmount: '0.00',
      variation: transformedVariation,
    } satisfies CartItemResponse;

    return cartItemWithDetails;
  }

  async clearCart(overrideUser?: UserSafe): Promise<CartResponse> {
    isAllowedToOverrideUser(this.context.user!, overrideUser);

    const user = overrideUser ?? this.context.user!;

    await this.prisma.cart.deleteMany({
      where: {
        userId: user.id,
      },
    });

    return {
      items: [],
      subtotal: '0.00',
      handlingCharge: '0.00',
      deliveryFee: '0.00',
      couponCode: null,
      couponValid: false,
      couponDiscount: '0.00',
      useRewardPoints: false,
      rewardDiscount: '0.00',
      rewardPointsUsed: 0,
      total: '0.00',
      canOrder: false,
    } satisfies CartResponse;
  }
}
