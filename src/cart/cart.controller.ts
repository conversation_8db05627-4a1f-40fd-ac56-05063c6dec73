import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseBoolPipe,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
  UsePipes,
} from '@nestjs/common';
import { WarehouseType } from '@prisma/client';
import { ZodValidationPipe } from 'nestjs-zod';
import { AuthGuard } from 'src/auth/auth.guard';
import { AddCartItemDto } from './cart.schema';
import { CartService } from './cart.service';
import { User } from 'src/auth/auth.decorator';
import { UsersService } from 'src/user/users.service';

@Controller('cart')
@UseGuards(AuthGuard)
@UsePipes(ZodValidationPipe)
export class CartController {
  constructor(
    private readonly cartService: CartService,
    private readonly usersService: UsersService,
  ) {}

  @User()
  @Get()
  async getCart(
    @Query('lat') lat: number,
    @Query('long') long: number,
    @Query('warehouseType') warehouseType: WarehouseType,
    @Query('lang', new DefaultValuePipe('en')) lang: string,
    @Query('couponCode') couponCode?: string,
    @Query('useRewardPoints', new DefaultValuePipe(false), ParseBoolPipe)
    useRewardPoints?: boolean,
    @Query(
      'overrideUserId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideUserId?: number,
    @Query(
      'overrideWarehouseId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideWarehouseId?: number,
  ) {
    const overrideUser = overrideUserId
      ? await this.usersService.getUserDetailsById(overrideUserId)
      : undefined;
    return this.cartService.getCart({
      lat: lat,
      long: long,
      warehouseType: warehouseType,
      couponCode,
      useRewardPoints,
      lang: lang,
      overrideUser: overrideUser,
      overrideWarehouseId,
    });
  }

  @User()
  @Post()
  async addCartItem(
    @Body() addCartItemDto: AddCartItemDto,
    @Query('lang', new DefaultValuePipe('en')) lang: string,
    @Query('lat') lat: number,
    @Query('long') long: number,
    @Query('warehouseType') warehouseType: WarehouseType,
    @Query(
      'overrideUserId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideUserId?: number,
    @Query(
      'overrideWarehouseId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideWarehouseId?: number,
  ) {
    const overrideUser = overrideUserId
      ? await this.usersService.getUserDetailsById(overrideUserId)
      : undefined;
    return this.cartService.addCartItem({
      addCartItemDto,
      lang,
      lat,
      long,
      warehouseType,
      overrideUser,
      overrideWarehouseId,
    });
  }

  @User()
  @Delete(':id')
  async deleteCartItem(
    @Param('id', ParseIntPipe) id: number,
    @Query('lat') lat: number,
    @Query('long') long: number,
    @Query('warehouseType') warehouseType: WarehouseType,
    @Query('lang', new DefaultValuePipe('en')) lang: string,
    @Query(
      'overrideWarehouseId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideWarehouseId?: number,
  ) {
    return this.cartService.deleteCartItem({
      id,
      lat,
      long,
      lang,
      warehouseType,
      overrideWarehouseId,
    });
  }

  @User()
  @Delete('/clear')
  async clearCart(
    @Query(
      'overrideUserId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideUserId?: number,
  ) {
    const overrideUser = overrideUserId
      ? await this.usersService.getUserDetailsById(overrideUserId)
      : undefined;
    return this.cartService.clearCart(overrideUser);
  }
}
