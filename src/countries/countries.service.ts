import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class CountriesService {
  constructor(private readonly prisma: PrismaService) {}

  async getCountries() {
    return this.prisma.country.findMany({
      orderBy: {
        name: 'asc',
      },
    });
  }

  async getCountryById(id: number) {
    const country = await this.prisma.country.findUniqueOrThrow({
      where: { id },
    });

    return country;
  }
}
