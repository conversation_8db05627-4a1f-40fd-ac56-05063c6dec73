import { Controller, Get, Param, ParseIntPipe, UsePipes } from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { CountriesService } from './countries.service';

@Controller('countries')
@UsePipes(ZodValidationPipe)
export class CountriesController {
  constructor(private countriesService: CountriesService) {}

  @Get()
  async getCountries() {
    return this.countriesService.getCountries();
  }

  @Get('/:id')
  async getCountryById(@Param('id', ParseIntPipe) id: number) {
    return this.countriesService.getCountryById(id);
  }
}
