import { HttpStatus, Injectable } from '@nestjs/common';
import { OrderStatus, Prisma } from '@prisma/client';
import { Context } from 'src/context';
import { Exception } from 'src/exceptions';
import { NotificationService } from 'src/admin/notification/notification.service';
import { PrismaService } from 'src/prisma/prisma.service';
import { UpdateOrderStatusDto } from './staffs.schema';
import { AdminOrderTransformer } from 'src/transformers/admin-order.transformer';
import { AdminOrderResponse } from 'src/types';
import { OrdersService } from 'src/admin/orders/orders.service';

@Injectable()
export class StaffsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly context: Context,
    private readonly notificationService: NotificationService,
    private readonly adminOrderTransformer: AdminOrderTransformer,
    private readonly ordersService: OrdersService,
  ) {}

  async getStaffDetails() {
    try {
      const staff = await this.prisma.warehouseStaff.findMany({
        where: {
          userId: this.context.user!.id,
        },
        include: {
          warehouse: true,
        },
      });

      return {
        warehouses: staff.map((warehouseStaff) => warehouseStaff.warehouse),
        user: this.context.user!,
      };
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new Exception(
            'You are not an authorized staff',
            HttpStatus.UNAUTHORIZED,
          );
        }

        throw new Exception(error.message);
      }
      throw error;
    }
  }

  async getWarehouseOrders(params: {
    page: number;
    pageSize: number;
    status?: string;
  }) {
    const { page = 1, pageSize = 10, status } = params;

    // Get the warehouses assigned to the staff
    const staffWarehouses = await this.prisma.warehouseStaff.findMany({
      where: {
        userId: this.context.user!.id,
      },
      select: {
        warehouseId: true,
      },
    });

    const warehouseIds = staffWarehouses.map((wh) => wh.warehouseId);

    if (warehouseIds.length === 0) {
      return {
        data: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0,
      };
    }

    // Build the where clause
    const where: Prisma.OrderWhereInput = {
      warehouseId: {
        in: warehouseIds,
      },
    };

    // Add status filter if provided
    if (status) {
      where.status = status as OrderStatus;
    }

    const [orders, total] = await this.prisma.$transaction([
      this.prisma.order.findMany({
        where,
        include: {
          items: {
            include: {
              product: {
                include: {
                  media: true,
                  category: true,
                },
              },
              inventory: {
                include: {
                  batch: {
                    include: {
                      warehouse: true,
                    },
                  },
                },
              },
            },
          },
          address: {
            include: {
              country: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              phoneCountry: true,
              profilePicture: true,
              countryId: true,
              createdAt: true,
              updatedAt: true,
              type: true,
              firebaseToken: true,
              tierExpiresAt: true,
              rewardTier: true,
              rewardTierId: true,
              referralCode: true,
              referredById: true,
            },
          },
          orderStatusHistory: true,
          warehouse: true,
          deliveryDriver: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              phoneCountry: true,
              profilePicture: true,
              countryId: true,
              createdAt: true,
              updatedAt: true,
              type: true,
              firebaseToken: true,
              tierExpiresAt: true,
              rewardTier: true,
              rewardTierId: true,
              referralCode: true,
              referredById: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
      this.prisma.order.count({
        where,
      }),
    ]);

    return {
      data: orders,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getOrderDetails(orderId: number): Promise<AdminOrderResponse> {
    // Get the warehouses assigned to the staff
    const staffWarehouses = await this.prisma.warehouseStaff.findMany({
      where: {
        userId: this.context.user!.id,
      },
      select: {
        warehouseId: true,
      },
    });

    const warehouseIds = staffWarehouses.map((wh) => wh.warehouseId);

    if (warehouseIds.length === 0) {
      throw new Exception(
        'You are not assigned to any warehouse',
        HttpStatus.FORBIDDEN,
      );
    }

    // Get the order details
    const order = await this.prisma.order.findFirst({
      where: {
        id: orderId,
        warehouseId: {
          in: warehouseIds,
        },
      },
      include: {
        items: {
          include: {
            product: {
              include: {
                media: true,
                category: true,
              },
            },
            variation: {
              include: {
                options: {
                  include: {
                    option: {
                      include: {
                        attribute: true,
                      },
                    },
                  },
                },
                media: true,
              },
            },
            inventory: {
              include: {
                batch: {
                  include: {
                    warehouse: true,
                  },
                },
              },
            },
          },
        },
        address: {
          include: {
            country: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            phoneCountry: true,
            profilePicture: true,
            countryId: true,
            createdAt: true,
            updatedAt: true,
            type: true,
            firebaseToken: true,
            tierExpiresAt: true,
            rewardTier: true,
            rewardTierId: true,
            referralCode: true,
            referredById: true,
            orders: true,
          },
        },
        orderStatusHistory: true,
        warehouse: true,
        deliveryDriver: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            phoneCountry: true,
            profilePicture: true,
            countryId: true,
            createdAt: true,
            updatedAt: true,
            type: true,
            firebaseToken: true,
            tierExpiresAt: true,
            rewardTier: true,
            rewardTierId: true,
            referralCode: true,
            referredById: true,
          },
        },
      },
    });

    if (!order) {
      throw new Exception(
        'Order not found or not assigned to your warehouses',
        HttpStatus.NOT_FOUND,
      );
    }

    const totalOrders = order.user.orders.length;
    const totalOrderPrice = order.user.orders.reduce(
      (acc, curr) => acc + +curr.totalAmount,
      0,
    );

    const averageOrderPrice = totalOrderPrice / totalOrders;

    // Process the order with user metrics
    const processedOrder = {
      ...order,
      user: {
        ...order.user,
        totalOrders: totalOrders,
        totalOrderPrice: totalOrderPrice.toFixed(2),
        averageOrderPrice: averageOrderPrice.toFixed(2),
      },
    };

    // Transform the order using the admin order transformer
    return await this.adminOrderTransformer.getAdminOrderResponse(
      processedOrder,
    );
  }

  async updateOrderStatus(
    orderId: number,
    dto: UpdateOrderStatusDto,
  ): Promise<AdminOrderResponse> {
    const { orderStatus, paymentStatus, deliveryDriverId } = dto;

    // Get the warehouses assigned to the staff
    const staffWarehouses = await this.prisma.warehouseStaff.findMany({
      where: {
        userId: this.context.user!.id,
      },
      select: {
        warehouseId: true,
      },
    });

    const warehouseIds = staffWarehouses.map((wh) => wh.warehouseId);

    if (warehouseIds.length === 0) {
      throw new Exception(
        'You are not assigned to any warehouse',
        HttpStatus.FORBIDDEN,
      );
    }

    // Check if the order belongs to one of the staff's warehouses
    const existingOrder = await this.prisma.order.findFirst({
      where: {
        id: orderId,
        warehouseId: {
          in: warehouseIds,
        },
      },
    });

    if (!existingOrder) {
      throw new Exception(
        'Order not found or not assigned to your warehouses',
        HttpStatus.NOT_FOUND,
      );
    }

    return await this.ordersService.updateOrderStatus(orderId, {
      orderStatus,
      paymentStatus,
      deliveryDriverId,
    });
  }
}
