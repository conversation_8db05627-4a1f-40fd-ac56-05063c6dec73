import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Query,
  UseGuards,
} from '@nestjs/common';
import { StaffsService } from './staffs.service';
import { AuthGuard } from 'src/auth/auth.guard';
import { WarehouseStaff } from 'src/auth/auth.decorator';
import { UpdateOrderStatusDto } from './staffs.schema';

@UseGuards(AuthGuard)
@Controller()
export class StaffsController {
  constructor(private readonly staffsService: StaffsService) {}

  @WarehouseStaff()
  @Get()
  async getStaffDetails() {
    return this.staffsService.getStaffDetails();
  }

  @WarehouseStaff()
  @Get('orders')
  async getWarehouseOrders(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
    @Query('status') status?: string,
  ) {
    return this.staffsService.getWarehouseOrders({
      page,
      pageSize,
      status,
    });
  }

  @WarehouseStaff()
  @Get('orders/:id')
  async getOrderDetails(@Param('id', ParseIntPipe) id: number) {
    return this.staffsService.getOrderDetails(id);
  }

  @WarehouseStaff()
  @Patch('orders/:id')
  async updateOrderStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOrderStatusDto: UpdateOrderStatusDto,
  ) {
    return this.staffsService.updateOrderStatus(id, updateOrderStatusDto);
  }
}
