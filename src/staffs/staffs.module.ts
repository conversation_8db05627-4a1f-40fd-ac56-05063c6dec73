import { Module } from '@nestjs/common';
import { StaffsService } from './staffs.service';
import { StaffsController } from './staffs.controller';
import { Context } from 'src/context';
import { PrismaService } from 'src/prisma/prisma.service';
import { NotificationService } from 'src/admin/notification/notification.service';
import { AdminOrderTransformer } from 'src/transformers/admin-order.transformer';
import { ProductTransformer } from 'src/transformers/product.transformer';
import { ProductsService } from 'src/admin/products/products.service';
import { WarehouseService } from 'src/admin/warehouse/warehouse.service';
import { InventoryService } from 'src/admin/inventories/inventories.service';
import { AlgoliaService } from 'src/algolia/algolia.service';
import { OrdersService } from 'src/admin/orders/orders.service';
import { RewardTiersService } from 'src/admin/reward-tiers/reward-tiers.service';
import { EmailService } from 'src/email/email.service';

@Module({
  providers: [
    StaffsService,
    Context,
    PrismaService,
    NotificationService,
    AdminOrderTransformer,
    ProductTransformer,
    InventoryService,
    ProductsService,
    WarehouseService,
    AlgoliaService,
    OrdersService,
    RewardTiersService,
    EmailService,
  ],
  controllers: [StaffsController],
})
export class StaffsModule {}
