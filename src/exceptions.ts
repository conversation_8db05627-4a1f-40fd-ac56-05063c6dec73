import { HttpException, HttpStatus } from '@nestjs/common';

export class Exception extends HttpException {
  constructor(
    error: string | Error,
    statusCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
  ) {
    const message = typeof error === 'string' ? error : error.message;
    super(message, statusCode);
  }
}

export class NoWarehouseException extends Exception {
  constructor() {
    super('No warehouse found');
  }
}
