import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as admin from 'firebase-admin';
import { patchNestJsSwagger } from 'nestjs-zod';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const decoded = Buffer.from(
    process.env.FIREBASE_SERVICE_ACCOUNT_BASE64!,
    'base64',
  ).toString('utf8');
  const serviceAccount = JSON.parse(decoded);

  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
  });

  app.enableCors({
    origin: [
      'http://localhost:3001',
      'https://vegmove-admin.shq.su',
      'https://vegmove-admin.vercel.app',
      'https://vegmove.techsfera.dev',
    ],
  });

  patchNestJsSwagger();

  const config = new DocumentBuilder()
    .setTitle('Vegmove API')
    .addTag('vegmove')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addSecurityRequirements('JWT-auth')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api-doc', app, document);

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
