import { Middleware<PERSON>onsumer, <PERSON><PERSON><PERSON>, Scope } from '@nestjs/common';
import { REQUEST, RouterModule } from '@nestjs/core';
import { AddressesController } from './addresses/addresses.controller';
import { AddressesService } from './addresses/addresses.service';
import { AdminModule } from './admin/admin.module';
import { NotificationService } from './admin/notification/notification.service';
import { OrdersService as AdminOrdersService } from './admin/orders/orders.service';
import { WarehouseService } from './admin/warehouse/warehouse.service';
import { AuthController } from './auth/auth.controller';
import { AuthMiddleware } from './auth/auth.middleware';
import { AuthService } from './auth/auth.service';
import { BannersController } from './banners/banners.controller';
import { BannersService } from './banners/banners.service';
import { CartController } from './cart/cart.controller';
import { CartService } from './cart/cart.service';
import { CategoriesController } from './categories/categories.controller';
import { CategoriesService } from './categories/categories.service';
import { Context } from './context';
import { CountriesController } from './countries/countries.controller';
import { CountriesService } from './countries/countries.service';
import { DeliveryDriversController } from './delivery-drivers/delivery-drivers.controller';
import { DeliveryDriversService } from './delivery-drivers/delivery-drivers.service';
import { InvoicesController } from './invoices/invoices.controller';
import { InvoicesService } from './invoices/invoices.service';
import { LanguagesController } from './languages/languages.controller';
import { LanguagesService } from './languages/languages.service';
import { MediaController } from './media/media.controller';
import { MediaService } from './media/media.service';
import { OrdersController } from './orders/orders.controller';
import { OrdersService } from './orders/orders.service';
import { PrismaService } from './prisma/prisma.service';
import { ProductsController } from './products/products.controller';
import { ProductsService } from './products/products.service';
import { RewardPointsController } from './reward-points/reward-points.controller';
import { RewardPointsService } from './reward-points/reward-points.service';
import { StaffsModule } from './staffs/staffs.module';
import { ProductTransformer } from './transformers/product.transformer';
import { UsersController } from './user/users.controller';
import { UsersService } from './user/users.service';
import { OrderTransformer } from './transformers/order.transformer';
import { AdminOrderTransformer } from './transformers/admin-order.transformer';
import { HomeSectionService } from './home-section/home-section.service';
import { HomeSectionController } from './home-section/home-section.controller';
import { ZonesController } from './zones/zones.controller';
import { ZonesService } from './admin/zone/zones.service';
import { RewardTiersService } from './reward-tiers/reward-tiers.service';
import { S3Service } from './s3/s3.service';
import { S3Controller } from './s3/s3.controller';
import { RewardTiersController } from './reward-tiers/reward-tiers.controller';
import { RewardTiersCron } from './reward-tiers/reward-tiers.cron';
import { DeliverySlotController } from './delivery-slot/delivery-slot.controller';
import { DeliverySlotService } from './admin/delivery-slot/delivery-slot.service';
import { RewardTiersService as AdminRewardTiersService } from './admin/reward-tiers/reward-tiers.service';
import { AlgoliaService } from './algolia/algolia.service';
import { SearchService } from './search/search.service';
import { SearchController } from './search/search.controller';
import { EmailService } from './email/email.service';

@Module({
  imports: [
    AdminModule,
    RouterModule.register([
      {
        path: 'admin',
        module: AdminModule,
      },
    ]),
    StaffsModule,
    RouterModule.register([
      {
        path: 'staffs',
        module: StaffsModule,
      },
    ]),
  ],
  controllers: [
    ProductsController,
    AuthController,
    CartController,
    OrdersController,
    MediaController,
    AddressesController,
    CountriesController,
    CategoriesController,
    UsersController,
    BannersController,
    RewardPointsController,
    LanguagesController,
    DeliveryDriversController,
    InvoicesController,
    HomeSectionController,
    ZonesController,
    S3Controller,
    RewardTiersController,
    DeliverySlotController,
    SearchController,
  ],
  providers: [
    ProductsService,
    {
      provide: Context,
      inject: [REQUEST],
      scope: Scope.REQUEST,
      useFactory: (request) => new Context(request),
    },
    AuthService,
    PrismaService,
    CartService,
    OrdersService,
    MediaService,
    AddressesService,
    CountriesService,
    CategoriesService,
    UsersService,
    BannersService,
    RewardPointsService,
    LanguagesService,
    DeliveryDriversService,
    InvoicesService,
    AdminOrdersService,
    WarehouseService,
    NotificationService,
    ProductTransformer,
    OrderTransformer,
    AdminOrderTransformer,
    HomeSectionService,
    ZonesService,
    RewardTiersService,
    S3Service,
    RewardTiersCron,
    DeliverySlotService,
    AdminRewardTiersService,
    AlgoliaService,
    SearchService,
    EmailService,
  ],
  exports: [Context, PrismaService, RewardPointsService, AlgoliaService],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AuthMiddleware).exclude('auth/*path').forRoutes('*');
  }
}
