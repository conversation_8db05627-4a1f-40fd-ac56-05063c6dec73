import { AddressType } from '@prisma/client';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const CreateAddressSchema = z.object({
  type: z.nativeEnum(AddressType),
  apartment: z.string().nullable(),
  block: z.string().nullable(),
  streetName: z.string().min(1, 'Street name is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  countryId: z.number().int(),
  zipCode: z.string().min(1, 'Zip Code is required'),
  lat: z.string(),
  long: z.string(),
  isDeleted: z.boolean().optional().default(false),
});

export class CreateAddressDto extends createZodDto(CreateAddressSchema) {}

export const EditAddressSchema = CreateAddressSchema.partial();

export class EditAddressDto extends createZodDto(EditAddressSchema) {}
