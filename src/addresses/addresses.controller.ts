import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
  UsePipes,
} from '@nestjs/common';
import { AddressesService } from './addresses.service';
import { CreateAddressDto, EditAddressDto } from './addresses.schema';
import { AuthGuard } from 'src/auth/auth.guard';
import { ZodValidationPipe } from 'nestjs-zod';
import { User } from 'src/auth/auth.decorator';
import { UsersService } from 'src/user/users.service';

@Controller('addresses')
@UseGuards(AuthGuard)
@UsePipes(ZodValidationPipe)
export class AddressesController {
  constructor(
    private readonly addressesService: AddressesService,
    private readonly usersService: UsersService,
  ) {}

  @User()
  @Get()
  async getAddresses(
    @Query(
      'overrideUserId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideUserId?: number,
  ) {
    const overrideUser = overrideUserId
      ? await this.usersService.getUserDetailsById(overrideUserId)
      : undefined;

    return this.addressesService.getAddresses(overrideUser);
  }

  @User()
  @Post()
  async createAddress(
    @Body() createAddressDto: CreateAddressDto,
    @Query(
      'overrideUserId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideUserId?: number,
  ) {
    const overrideUser = overrideUserId
      ? await this.usersService.getUserDetailsById(overrideUserId)
      : undefined;

    return this.addressesService.createAddress(createAddressDto, overrideUser);
  }

  @User()
  @Patch('/:id')
  async editAddress(
    @Param('id', ParseIntPipe) id: number,
    @Body() editAddressDto: EditAddressDto,
    @Query(
      'overrideUserId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideUserId?: number,
  ) {
    const overrideUser = overrideUserId
      ? await this.usersService.getUserDetailsById(overrideUserId)
      : undefined;

    return this.addressesService.editAddress(id, editAddressDto, overrideUser);
  }

  @User()
  @Delete('/:id')
  async deleteAddress(
    @Param('id', ParseIntPipe) id: number,
    @Query(
      'overrideUserId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideUserId?: number,
  ) {
    const overrideUser = overrideUserId
      ? await this.usersService.getUserDetailsById(overrideUserId)
      : undefined;

    return this.addressesService.deleteAddress(id, overrideUser);
  }
}
