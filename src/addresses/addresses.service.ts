import { Injectable } from '@nestjs/common';
import { Context } from 'src/context';
import { Exception } from 'src/exceptions';
import { PrismaService } from 'src/prisma/prisma.service';
import { UserSafe } from 'src/types';
import { isAllowedToOverrideUser } from 'src/util/utils';
import { CreateAddressDto, EditAddressDto } from './addresses.schema';

@Injectable()
export class AddressesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly context: Context,
  ) {}

  async getAddresses(overrideUser?: UserSafe) {
    isAllowedToOverrideUser(this.context.user!, overrideUser);
    const user = overrideUser ?? this.context.user!;

    return this.prisma.address.findMany({
      where: {
        userId: user.id,
        isDeleted: false,
      },
      include: {
        country: true,
      },
    });
  }

  async createAddress(
    createAddressDto: CreateAddressDto,
    overrideUser?: UserSafe,
  ) {
    isAllowedToOverrideUser(this.context.user!, overrideUser);
    const user = overrideUser ?? this.context.user!;

    const address = await this.prisma.address.create({
      data: {
        ...createAddressDto,
        userId: user.id,
      },
      include: {
        country: true,
      },
    });

    return address;
  }

  async editAddress(
    id: number,
    editAddressDto: EditAddressDto,
    overrideUser?: UserSafe,
  ) {
    isAllowedToOverrideUser(this.context.user!, overrideUser);
    const user = overrideUser ?? this.context.user!;

    const address = await this.prisma.address.update({
      where: {
        id: id,
        userId: user.id,
      },
      data: {
        ...editAddressDto,
      },
      include: {
        country: true,
      },
    });

    return address;
  }

  async deleteAddress(id: number, overrideUser?: UserSafe) {
    isAllowedToOverrideUser(this.context.user!, overrideUser);
    const user = overrideUser ?? this.context.user!;

    const address = await this.prisma.address.findFirst({
      where: {
        id: id,
        userId: user.id,
      },
    });

    if (!address) {
      throw new Exception(
        'Address not found or does not belong to the current user',
      );
    }

    const updatedAddress = await this.prisma.address.update({
      where: {
        id: id,
      },
      data: {
        isDeleted: true,
      },
      include: {
        country: true,
      },
    });

    return updatedAddress;
  }
}
