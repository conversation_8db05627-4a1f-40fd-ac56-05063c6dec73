import { Controller, Get, Query } from '@nestjs/common';
import { DeliverySlotService } from 'src/admin/delivery-slot/delivery-slot.service';

@Controller('delivery-slots')
export class DeliverySlotController {
  constructor(private readonly slotService: DeliverySlotService) {}

  @Get()
  async getSlots(@Query('date') dateStr: string) {
    const date = new Date(dateStr);
    return this.slotService.getSlotsForDate(date);
  }
}
