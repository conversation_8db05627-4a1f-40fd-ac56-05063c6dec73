import { z } from 'zod';
import { createZodDto } from 'nestjs-zod';

// Schema for updating driver location
const UpdateLocationSchema = z.object({
  lat: z.string().min(1, 'Latitude is required'),
  long: z.string().min(1, 'Longitude is required'),
});

// Schema for updating driver active status
const UpdateStatusSchema = z.object({
  isActive: z.boolean(),
});

// DTO for updating driver location
export class UpdateLocationDto extends createZodDto(UpdateLocationSchema) {}

// DTO for updating driver active status
export class UpdateStatusDto extends createZodDto(UpdateStatusSchema) {}
