import { Injectable } from '@nestjs/common';
import { subMinutes } from 'date-fns';
import { NotificationService } from 'src/admin/notification/notification.service';
import { OrdersService } from 'src/admin/orders/orders.service';
import { Context } from 'src/context';
import { PrismaService } from 'src/prisma/prisma.service';
import { AdminOrderTransformer } from 'src/transformers/admin-order.transformer';
import {
  calculateDistance,
  combineDateAndTime,
  getTravelTimeInMinutes,
} from 'src/util/utils';

@Injectable()
export class DeliveryDriversService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly context: Context,
    private readonly notificationService: NotificationService,
    private readonly ordersService: OrdersService,
    private readonly adminOrderTransformer: AdminOrderTransformer,
  ) {}

  async getAssignedOrders(params: { page: number; pageSize: number }) {
    const { page, pageSize } = params;

    const [orders, total] = await this.prisma.$transaction([
      this.prisma.order.findMany({
        where: {
          deliveryDriverId: this.context.user!.id,
          status: {
            in: ['CONFIRMED', 'READY', 'PACKING', 'SHIPPED', 'DELIVERED'],
          },
        },
        include: {
          items: {
            include: {
              product: true,
              inventory: {
                include: {
                  batch: {
                    include: {
                      warehouse: true,
                    },
                  },
                },
              },
            },
          },
          address: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              phoneCountry: true,
              profilePicture: true,
              countryId: true,
              createdAt: true,
              updatedAt: true,
              type: true,
            },
          },
          orderStatusHistory: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * pageSize,
        take: pageSize,
      }),
      this.prisma.order.count({
        where: {
          deliveryDriverId: this.context.user!.id,
        },
      }),
    ]);

    return {
      data: orders,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async getOrderDetail(orderId: number) {
    const order = await this.prisma.order.findFirstOrThrow({
      where: {
        id: orderId,
        deliveryDriverId: this.context.user!.id,
      },
      include: {
        items: {
          include: {
            product: {
              include: {
                thumbnail: true,
              },
            },
            variation: {
              include: {
                options: {
                  include: {
                    option: {
                      include: {
                        attribute: true,
                      },
                    },
                  },
                },
                media: true,
              },
            },
            inventory: {
              include: {
                batch: {
                  include: {
                    warehouse: true,
                  },
                },
              },
            },
          },
        },
        address: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            phoneCountry: true,
            countryId: true,
            profilePicture: true,
            createdAt: true,
            updatedAt: true,
            type: true,
          },
        },
        orderStatusHistory: true,
        warehouse: true,
      },
    });

    const warehouse = order.warehouse!;
    const address = order.address;

    const deliveryEnd = combineDateAndTime(
      order.deliveryDate,
      order.deliveryEndTime,
    );
    const deliveryDeadline = subMinutes(deliveryEnd, 5); // Deliver at least 5 mins before
    const travelTimeMins = await getTravelTimeInMinutes({
      fromLat: warehouse.lat,
      fromLong: warehouse.long,
      toLat: address.lat,
      toLong: address.long,
      mode: 'bicycling',
    });
    const pickupDeadline = subMinutes(deliveryDeadline, travelTimeMins + 10); // 10 min prep buffer

    // Transform the order using the admin order transformer
    const transformedOrder =
      await this.adminOrderTransformer.getAdminOrderResponse(order);

    // Add delivery-specific information
    return {
      ...transformedOrder,
      deliveryDeadline,
      pickupDeadline,
      travelTime: travelTimeMins,
    };
  }

  async markOrderAsDelivered(orderId: number) {
    const order = await this.ordersService.updateOrderStatus(orderId, {
      orderStatus: 'DELIVERED',
    });

    return order;
  }

  async markOrderAsShipped(orderId: number) {
    const order = await this.ordersService.updateOrderStatus(orderId, {
      orderStatus: 'SHIPPED',
    });

    return order;
  }

  async updateLocation(lat: string, long: string) {
    const driverId = this.context.user!.id;

    // Create or update location
    const location = await this.prisma.deliveryDriverLocation.create({
      data: {
        driverId,
        lat,
        long,
      },
    });

    return location;
  }

  async getLastLocation(driverId: number) {
    const lastLocation = await this.prisma.deliveryDriverLocation.findFirst({
      where: {
        driverId,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    return lastLocation;
  }

  async getDistanceFromWarehouse(warehouseId: number) {
    const driverId = this.context.user!.id;

    const [lastLocation, warehouse] = await Promise.all([
      this.prisma.deliveryDriverLocation.findFirst({
        where: {
          driverId,
        },
        orderBy: {
          updatedAt: 'desc',
        },
      }),
      this.prisma.warehouse.findUnique({
        where: { id: warehouseId },
      }),
    ]);

    if (!lastLocation || !warehouse) {
      return null;
    }

    const distance = calculateDistance(
      parseFloat(lastLocation.lat),
      parseFloat(lastLocation.long),
      parseFloat(warehouse.lat),
      parseFloat(warehouse.long),
    );

    return {
      distance: Math.round(distance * 100) / 100, // Round to 2 decimal places
      unit: 'km',
      lastUpdated: lastLocation.updatedAt,
    };
  }

  async getDriverProfile() {
    const driverId = this.context.user!.id;

    // Get or create driver profile
    let profile = await this.prisma.deliveryDriverProfile.findUnique({
      where: { userId: driverId },
      include: {
        user: {
          include: {
            phoneCountry: true,
          },
        },
      },
    });

    // Create profile if it doesn't exist (for backward compatibility)
    if (!profile) {
      profile = await this.prisma.deliveryDriverProfile.create({
        data: {
          userId: driverId,
          isActive: true,
        },
        include: {
          user: {
            include: {
              phoneCountry: true,
            },
          },
        },
      });
    }

    return profile;
  }

  async updateDriverStatus(isActive: boolean) {
    const driverId = this.context.user!.id;

    // Get or create driver profile
    const existingProfile = await this.prisma.deliveryDriverProfile.findUnique({
      where: { userId: driverId },
    });

    let profile;
    if (existingProfile) {
      // Update existing profile
      profile = await this.prisma.deliveryDriverProfile.update({
        where: { userId: driverId },
        data: { isActive },
        include: {
          user: {
            include: {
              phoneCountry: true,
            },
          },
        },
      });
    } else {
      // Create new profile (for backward compatibility)
      profile = await this.prisma.deliveryDriverProfile.create({
        data: {
          userId: driverId,
          isActive,
        },
        include: {
          user: {
            include: {
              phoneCountry: true,
            },
          },
        },
      });
    }

    return profile;
  }
}
