import {
  Body,
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
  UsePipes,
} from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { AuthGuard } from 'src/auth/auth.guard';
import { DeliveryDriver } from 'src/auth/auth.decorator';
import { DeliveryDriversService } from './delivery-drivers.service';
import { UpdateLocationDto, UpdateStatusDto } from './delivery-drivers.schema';

@Controller('delivery-drivers')
@UseGuards(AuthGuard)
@UsePipes(ZodValidationPipe)
export class DeliveryDriversController {
  constructor(
    private readonly deliveryDriversService: DeliveryDriversService,
  ) {}

  @DeliveryDriver()
  @Get('orders')
  async getAssignedOrders(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
  ) {
    return this.deliveryDriversService.getAssignedOrders({ page, pageSize });
  }

  @DeliveryDriver()
  @Get('orders/:id')
  async getOrderDetail(@Param('id', ParseIntPipe) id: number) {
    return this.deliveryDriversService.getOrderDetail(id);
  }

  @DeliveryDriver()
  @Patch('orders/:id/deliver')
  async markDelivered(@Param('id', ParseIntPipe) id: number) {
    return this.deliveryDriversService.markOrderAsDelivered(id);
  }

  @DeliveryDriver()
  @Patch('orders/:id/ship')
  async markAsShipped(@Param('id', ParseIntPipe) id: number) {
    return this.deliveryDriversService.markOrderAsShipped(id);
  }

  @DeliveryDriver()
  @Post('location')
  async updateLocation(@Body() locationData: UpdateLocationDto) {
    return this.deliveryDriversService.updateLocation(
      locationData.lat,
      locationData.long,
    );
  }

  @DeliveryDriver()
  @Get(':id/location')
  async getLastLocation(@Param('id', ParseIntPipe) id: number) {
    return this.deliveryDriversService.getLastLocation(id);
  }

  @DeliveryDriver()
  @Get('profile')
  async getProfile() {
    return this.deliveryDriversService.getDriverProfile();
  }

  @DeliveryDriver()
  @Patch('status')
  async updateStatus(@Body() statusData: UpdateStatusDto) {
    return this.deliveryDriversService.updateDriverStatus(statusData.isActive);
  }

  @DeliveryDriver()
  @Get('distance/:warehouseId')
  async getDistanceFromWarehouse(
    @Param('warehouseId', ParseIntPipe) warehouseId: number,
  ) {
    return this.deliveryDriversService.getDistanceFromWarehouse(warehouseId);
  }
}
