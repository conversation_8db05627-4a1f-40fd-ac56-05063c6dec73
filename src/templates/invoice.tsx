import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Image,
} from '@react-pdf/renderer';
import React from 'react';

const styles = StyleSheet.create({
  page: {
    fontSize: 12,
    padding: 40,
    fontFamily: 'Helvetica',
  },
  section: {
    marginBottom: 10,
  },
  logo: {
    width: 100,
    marginBottom: 20,
  },
  header: {
    fontSize: 20,
    marginBottom: 10,
    textTransform: 'uppercase',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  tableHeader: {
    backgroundColor: '#eee',
    fontWeight: 'bold',
    padding: 5,
  },
  tableRow: {
    padding: 5,
    borderBottom: '1px solid #ccc',
  },
  footer: {
    marginTop: 40,
    fontSize: 10,
    textAlign: 'center',
    color: '#999',
  },
});

export const InvoiceTemplate = ({ order }: { order: any }) => {
  return React.createElement(
    Document,
    null,
    React.createElement(
      Page,
      { size: 'A4', style: styles.page },

      // Logo + heading
      React.createElement(
        View,
        { style: styles.section },
        React.createElement(Image, {
          src: 'https://yourdomain.com/logo.png',
          style: styles.logo,
        }),
        React.createElement(Text, { style: styles.header }, 'Invoice'),
        React.createElement(Text, null, `Order ID: #${order.id}`),
        React.createElement(
          Text,
          null,
          `Date: ${new Date(order.createdAt).toLocaleDateString()}`,
        ),
        React.createElement(Text, null, `Status: ${order.status}`),
        React.createElement(
          Text,
          null,
          `Payment Status: ${order.paymentStatus}`,
        ),
      ),

      // Customer info
      React.createElement(
        View,
        { style: styles.section },
        React.createElement(
          Text,
          { style: { fontWeight: 'bold' } },
          'Customer Info',
        ),
        React.createElement(Text, null, order.user.name),
        React.createElement(Text, null, order.user.email),
        React.createElement(
          Text,
          null,
          `${order.address.streetName}, ${order.address.city}, ${order.address.state} - ${order.address.zipCode}`,
        ),
      ),

      // Table header
      React.createElement(
        View,
        { style: styles.section },
        React.createElement(
          Text,
          { style: { fontWeight: 'bold', marginBottom: 5 } },
          'Order Items',
        ),
        React.createElement(
          View,
          { style: [styles.row, styles.tableHeader] },
          React.createElement(Text, { style: { width: '40%' } }, 'Product'),
          React.createElement(Text, { style: { width: '15%' } }, 'Qty'),
          React.createElement(Text, { style: { width: '15%' } }, 'Price'),
          React.createElement(Text, { style: { width: '15%' } }, 'GST'),
          React.createElement(Text, { style: { width: '15%' } }, 'Total'),
        ),

        // Table rows
        ...order.items.map((item: any) => {
          const total =
            (parseFloat(item.price) + parseFloat(item.gstAmount)) *
            item.quantity;

          return React.createElement(
            View,
            { key: item.id, style: [styles.row, styles.tableRow] },
            React.createElement(
              Text,
              { style: { width: '40%' } },
              item.product.name,
            ),
            React.createElement(
              Text,
              { style: { width: '15%' } },
              `${item.quantity}`,
            ),
            React.createElement(
              Text,
              { style: { width: '15%' } },
              `${item.price}`,
            ),
            React.createElement(
              Text,
              { style: { width: '15%' } },
              `${item.gstAmount}`,
            ),
            React.createElement(
              Text,
              { style: { width: '15%' } },
              `${total.toFixed(2)}`,
            ),
          );
        }),
      ),

      // Totals
      React.createElement(
        View,
        { style: styles.section },
        React.createElement(
          View,
          { style: styles.row },
          React.createElement(Text, null, 'Subtotal'),
          React.createElement(
            Text,
            null,
            `₹${order.items
              .reduce(
                (acc: number, item: any) =>
                  acc +
                  (parseFloat(item.price) + parseFloat(item.gstAmount)) *
                    item.quantity,
                0,
              )
              .toFixed(2)}`,
          ),
        ),
        React.createElement(
          View,
          { style: styles.row },
          React.createElement(Text, null, 'Discount'),
          React.createElement(Text, null, `- ₹${order.discountAmount}`),
        ),
        React.createElement(
          View,
          { style: styles.row },
          React.createElement(Text, { style: { fontWeight: 'bold' } }, 'Total'),
          React.createElement(
            Text,
            { style: { fontWeight: 'bold' } },
            `₹${order.totalAmount}`,
          ),
        ),
      ),

      // Footer
      React.createElement(
        Text,
        { style: styles.footer },
        'Thanks for shopping with Vegmove!',
      ),
    ),
  );
};
