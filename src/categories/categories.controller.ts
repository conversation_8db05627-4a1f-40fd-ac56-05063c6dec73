import {
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseIntPipe,
  Query,
} from '@nestjs/common';
import { CategoriesService } from './categories.service';

@Controller('categories')
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Get()
  async getCategories(@Query('lang', new DefaultValuePipe('en')) lang: string) {
    return this.categoriesService.getCategories({ lang: lang });
  }

  @Get(':id')
  getCategoryById(
    @Param('id', ParseIntPipe) id: number,
    @Query('lang', new DefaultValuePipe('en')) lang: string,
  ) {
    return this.categoriesService.getCategoryById(id, lang);
  }
}
