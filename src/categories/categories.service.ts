import { Injectable, NotFoundException } from '@nestjs/common';
import { Category, CategoryType, Prisma } from '@prisma/client';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class CategoriesService {
  constructor(private readonly prisma: PrismaService) {}

  private translateCategory(
    category: Prisma.CategoryGetPayload<{
      include: {
        categoryTranslation: true;
      };
    }>,
  ): Category {
    return {
      ...category,
      name: category.categoryTranslation?.[0]?.name ?? category.name,
    };
  }

  async getCategories({ lang }: { lang: string }) {
    const language = await this.prisma.language.findUniqueOrThrow({
      where: { code: lang },
    });

    const categories = await this.prisma.category.findMany({
      where: { parent: null },
      include: {
        children: {
          include: {
            categoryTranslation: {
              where: {
                languageId: {
                  equals: language.id,
                },
              },
              take: 1,
            },
          },
        },
        categoryTranslation: {
          where: {
            languageId: {
              equals: language.id,
            },
          },
          take: 1,
        },
      },
    });

    const translatedCategories = categories.map((category) => ({
      ...category,
      name: category.categoryTranslation?.[0]?.name ?? category.name,
      children: category.children.map((child) => ({
        ...child,
        name: child.categoryTranslation?.[0]?.name ?? child.name,
      })),
    }));

    return translatedCategories;
  }

  async getCategoryById(id: number, lang: string) {
    const language = await this.prisma.language.findUniqueOrThrow({
      where: { code: lang },
    });

    const category = await this.prisma.category.findUnique({
      where: { id },
      include: {
        categoryTranslation: {
          where: { languageId: language.id },
          take: 1,
        },
        parent: {
          include: {
            categoryTranslation: {
              where: { languageId: language.id },
              take: 1,
            },
          },
        },
        children: {
          include: {
            categoryTranslation: {
              where: { languageId: language.id },
              take: 1,
            },
          },
        },
      },
    });

    if (!category) throw new NotFoundException('Category not found');

    // Translate the category name
    const translatedCategory = this.translateCategory(category);

    // Prepare the response based on category type
    const response: any = {
      id: translatedCategory.id,
      name: translatedCategory.name,
      type: translatedCategory.type,
      slug: translatedCategory.slug,
      isActive: translatedCategory.isActive,
      bannerUrl: translatedCategory.bannerUrl,
      iconUrl: translatedCategory.iconUrl,
    };

    // Add type-specific data
    if (translatedCategory.type === CategoryType.COLLECTION) {
      // For collections, include categories under it
      response.categories = category.children.map((child) => ({
        ...child,
        name: child.categoryTranslation?.[0]?.name ?? child.name,
      }));
    } else if (translatedCategory.type === CategoryType.CATEGORY) {
      // For categories, include parent collection
      if (category.parent) {
        response.parent = {
          ...category.parent,
          name:
            category.parent.categoryTranslation?.[0]?.name ??
            category.parent.name,
        };
      }
      response.segments = category.children.map((child) => ({
        ...child,
        name: child.categoryTranslation?.[0]?.name ?? child.name,
      }));
    } else if (translatedCategory.type === CategoryType.SEGMENT) {
      // For segments, include parent category
      if (category.parent) {
        response.parent = {
          ...category.parent,
          name:
            category.parent.categoryTranslation?.[0]?.name ??
            category.parent.name,
        };
      }
    }

    return response;
  }
}
