import { Body, Controller, Get, Param, Post, UsePipes } from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { LoginDto, RegisterDto } from './auth.schema';
import { AuthService } from './auth.service';

@Controller('auth')
@UsePipes(ZodValidationPipe)
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    return await this.authService.login(loginDto);
  }

  @Post('register')
  async register(@Body() registerDto: RegisterDto) {
    return await this.authService.register(registerDto);
  }

  @Get('validate-referral-code/:code')
  async validateReferralCode(@Param('code') code: string) {
    const isValid = await this.authService.validateReferralCode(code);
    return { valid: isValid };
  }
}
