import { Injectable, type NestMiddleware } from '@nestjs/common';
import * as jwt from 'jsonwebtoken';

import { PrismaService } from 'src/prisma/prisma.service';
import { type UserSafe } from 'src/types';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(private readonly prismaService: PrismaService) {}

  private async getUser({ token }: { token?: string }) {
    const prisma = this.prismaService;

    if (!token || token == 'null') return null;

    try {
      const payload = jwt.verify(token ?? '', process.env.JWT_SECRET!);

      if (!payload) return null;

      const user = await prisma.user.findUnique({
        where: {
          id: payload.sub as unknown as number,
        },
        select: {
          id: true,
          name: true,
          email: true,
          createdAt: true,
          updatedAt: true,
          type: true,
          phone: true,
          profilePicture: true,
          countryId: true,
          phoneCountry: true,
          rewardTierId: true,
          rewardTier: true,
          referralCode: true,
          referredById: true,
          firebaseToken: true,
          tierExpiresAt: true,
        },
      });
      return user;
    } catch (error) {
      console.log('AuthMiddleware', error);
      return null;
    }
  }

  async use(req: any, res: any, next: () => void) {
    const token = req.headers.authorization?.replace('Bearer ', '');
    const user: UserSafe | null = await this.getUser({
      token,
    });

    req.user = user;

    next();
  }
}
