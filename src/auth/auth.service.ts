import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import * as bcrypt from 'bcryptjs';
import { Exception } from 'src/exceptions';
import { signUserJWTToken } from 'src/util/jwt';
import { RegisterDto } from './auth.schema';
// import {Jwt} from 'jsonwebtoken';

@Injectable()
export class AuthService {
  constructor(private readonly prismaService: PrismaService) {}

  private async verifyLogin(email: string, password: string) {
    const user = await this.prismaService.user.findFirst({
      where: {
        email,
      },
    });
    if (!user) return null;

    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) return null;

    return user;
  }

  async login({
    email,
    password,
  }: {
    email: string;
    password: string;
  }): Promise<{ token?: string; tenantLoginUrl?: string }> {
    const user = await this.verifyLogin(email, password);

    if (!user) {
      throw new Exception('Invalid email or password', 401);
    }

    const token = signUserJWTToken(user);

    return {
      token,
    };
  }

  async register(registerDto: RegisterDto): Promise<{ token: string }> {
    if (!registerDto.email && !registerDto.phone) {
      throw new Exception('Email or phone is required');
    }

    const hashedPassword = await bcrypt.hash(registerDto.password, 10);

    const users = await this.prismaService.user.findMany({
      where: {
        OR: [
          {
            email: registerDto.email,
          },
          {
            phone: registerDto.phone,
          },
        ],
      },
    });

    if (users.length > 0) {
      throw new Exception('User already exists');
    }

    try {
      const referralCode = await this.generateUniqueReferralCode(
        registerDto.name,
      );

      let referredById: number | null = null;
      if (registerDto.referralCode) {
        const referrer = await this.prismaService.user.findFirst({
          where: {
            referralCode: registerDto.referralCode,
          },
        });

        if (referrer) {
          referredById = referrer.id;
        }
      }

      const user = await this.prismaService.user.create({
        data: {
          email:
            registerDto.email ??
            `${registerDto.name}.${new Date().getTime()}@example.com`,
          name: registerDto.name,
          password: hashedPassword,
          type: 'CUSTOMER',
          phone: registerDto.phone,
          countryId: registerDto.countryId,
          referralCode,
          referredById,
        },
      });

      const token = signUserJWTToken(user);

      return {
        token,
      };
    } catch (error) {
      console.log(error);
      throw new Exception('Something went wrong!');
    }
  }

  private async generateUniqueReferralCode(name: string): Promise<string> {
    const generateCode = (): string => {
      const now = new Date();

      const cleanName = name.replace(/\s+/g, '').toUpperCase();

      const namePart = cleanName.substring(0, 2).padEnd(2, 'X');

      const timePart = now.getSeconds().toString().padStart(2, '0');

      const randomLength = 7 - (namePart.length + timePart.length);

      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let randomPart = '';
      for (let i = 0; i < randomLength; i++) {
        randomPart += chars.charAt(Math.floor(Math.random() * chars.length));
      }

      return namePart + timePart + randomPart;
    };

    const maxRetries = 5;
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      const code = generateCode();

      const existingUser = await this.prismaService.user.findFirst({
        where: { referralCode: code },
      });

      if (!existingUser) {
        return code;
      }
    }

    throw new Error(
      'Unable to generate a unique referral code after maximum attempts.',
    );
  }

  async validateReferralCode(code: string): Promise<boolean> {
    if (!code) return false;
    try {
      const user = await this.prismaService.user.findFirst({
        where: {
          referralCode: code,
        },
      });

      return !!user;
    } catch (error) {
      console.error('Error validating referral code:', error);
      return false;
    }
  }
}
