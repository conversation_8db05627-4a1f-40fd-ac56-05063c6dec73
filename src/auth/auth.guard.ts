import {
  type CanActivate,
  type ExecutionContext,
  Injectable,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserType } from '@prisma/client';
import { type Observable } from 'rxjs';
import { UserSafe } from 'src/types';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: UserSafe | null = request.user;

    const userTypes = this.reflector.get<UserType[]>(
      'userTypes',
      context.getHandler(),
    );

    if (!userTypes) {
      return true;
    } else if (user == null) {
      return false;
    }

    return userTypes.includes(user.type) || userTypes.length == 0;
  }
}
