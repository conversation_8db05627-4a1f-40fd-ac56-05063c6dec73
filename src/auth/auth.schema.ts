import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string(),
});

export class LoginDto extends createZodDto(loginSchema) {}

const registerDto = z.object({
  name: z.string().min(4),
  email: z.string().email().optional(),
  password: z.string(),
  phone: z.string().optional(),
  countryId: z.number().optional(),
  referralCode: z.string().optional(),
});

export class RegisterDto extends createZodDto(registerDto) {}
