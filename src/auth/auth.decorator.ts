import { SetMetadata } from '@nestjs/common';
import { UserType } from '@prisma/client';

export const User = (...userTypes: UserType[]) =>
  SetMetadata('userTypes', userTypes);

export const Admin = () => User('ADMIN');
export const Customer = () => User('ADMIN', 'CUSTOMER');
export const DeliveryDriver = () => User('ADMIN', 'DELIVERY_PERSON');
export const WarehouseStaff = () => User('ADMIN', 'WAREHOUSE_STAFF');
