import { SetMetadata } from '@nestjs/common';
import { AccessLevel } from '@prisma/client';

export interface PermissionRequirement {
  feature: string;
  accessLevel: AccessLevel | 'ANY';
}

export const PERMISSION_KEY = 'permissions';

/**
 * Decorator to require specific permissions for a route
 * @param feature - The feature name (e.g., "Order", "Product")
 * @param accessLevel - The required access level (VIEW, MANAGE, DELETE)
 */
export const RequirePermission = (
  feature: string,
  accessLevel: AccessLevel | 'ANY',
) =>
  SetMetadata(PERMISSION_KEY, {
    feature,
    accessLevel,
  } as PermissionRequirement);

/**
 * Convenience decorators for common permission patterns
 */
export const CanView = (feature: string) =>
  RequirePermission(feature, AccessLevel.VIEW);
export const CanManage = (feature: string) =>
  RequirePermission(feature, AccessLevel.MANAGE);
export const CanDelete = (feature: string) =>
  RequirePermission(feature, AccessLevel.DELETE);

/**
 * Decorator to require any permission for a feature (VIEW, MANAGE, or DELETE)
 * @param feature - The feature name
 */
export const RequireAnyPermission = (feature: string) =>
  RequirePermission(feature, 'ANY');
