import {
  type CanActivate,
  type ExecutionContext,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { type Observable } from 'rxjs';
import { UserSafe } from 'src/types';
import { PermissionsService } from 'src/admin/permissions/permissions.service';
import { PERMISSION_KEY, PermissionRequirement } from './permission.decorator';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private permissionsService: PermissionsService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: UserSafe | null = request.user;

    // If no user is authenticated, deny access
    if (!user) {
      throw new ForbiddenException('Authentication required');
    }

    // Get permission requirement from decorator
    const permissionRequirement = this.reflector.get<PermissionRequirement>(
      PERMISSION_KEY,
      context.getHandler(),
    );

    // If no permission requirement is set, allow access
    if (!permissionRequirement) {
      return true;
    }

    const { feature, accessLevel } = permissionRequirement;

    try {
      // Check if user has the required permission
      if (accessLevel === 'ANY') {
        // Check if user has any permission for the feature
        const hasAnyPermission = await this.permissionsService.hasAnyPermission(
          user.id,
          feature,
        );

        if (!hasAnyPermission) {
          throw new ForbiddenException(
            `Access denied. You need any permission for ${feature}.`,
          );
        }
      } else {
        // Check specific access level
        const hasPermission = await this.permissionsService.hasPermission(
          user.id,
          feature,
          accessLevel,
        );

        if (!hasPermission) {
          throw new ForbiddenException(
            `Access denied. You need ${accessLevel} permission for ${feature}.`,
          );
        }
      }

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      // Log unexpected errors but don't expose them
      console.error('Permission check failed:', error);
      throw new ForbiddenException('Permission check failed');
    }
  }
}
