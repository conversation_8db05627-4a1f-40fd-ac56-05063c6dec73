import axios from 'axios';
import { Exception } from 'src/exceptions';
import { PaginatedResponse, TravelMode, UserSafe } from 'src/types';

export const ROLLING_DAYS = 30;

export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
): number {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180);
}

export function combineDateAndTime(date: Date, time: string): Date {
  const [hours, minutes] = time.split(':').map(Number);
  const result = new Date(date);
  result.setHours(hours, minutes, 0, 0);
  return result;
}

export async function getTravelTimeInMinutes({
  fromLat,
  fromLong,
  toLat,
  toLong,
  mode = 'driving',
}: {
  fromLat: string;
  fromLong: string;
  toLat: string;
  toLong: string;
  mode: TravelMode;
}): Promise<number> {
  const apiKey = process.env.GOOGLE_MAPS_API_KEY;
  const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${fromLat},${fromLong}&destination=${toLat},${toLong}&mode=${mode}&key=${apiKey}`;

  try {
    const res = await axios.get(url, { timeout: 5000 }); // optional timeout for responsiveness
    console.log(res);

    const durationSecs = res.data.routes?.[0]?.legs?.[0]?.duration?.value;

    if (!durationSecs) {
      console.warn('Google Maps returned no duration data. Falling back...');
      return fallbackDistanceEstimate(fromLat, fromLong, toLat, toLong, mode);
    }

    return Math.ceil(durationSecs / 60); // convert seconds to minutes
  } catch (error) {
    console.error('Error calling Google Maps:', (error as Error).message);
    return fallbackDistanceEstimate(fromLat, fromLong, toLat, toLong, mode);
  }
}

function fallbackDistanceEstimate(
  fromLat: string,
  fromLong: string,
  toLat: string,
  toLong: string,
  mode: TravelMode,
): number {
  const distanceKm = calculateDistance(
    parseFloat(fromLat),
    parseFloat(fromLong),
    parseFloat(toLat),
    parseFloat(toLong),
  );

  const averageSpeedMap: Record<TravelMode, number> = {
    driving: 40, // km/h
    walking: 5,
    bicycling: 15,
    transit: 25,
  };

  const speed = averageSpeedMap[mode] || 40;
  const estimatedDurationHours = distanceKm / speed;

  return Math.ceil(estimatedDurationHours * 60); // return minutes
}

export function createPaginatedResponse<T>({
  data,
  total,
  page,
  pageSize,
}: {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}): PaginatedResponse<T> {
  return {
    data: data,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize),
  };
}

export function isAllowedToOverrideUser(
  user: UserSafe,
  overrideUser?: UserSafe,
) {
  if (overrideUser && user?.type !== 'ADMIN') {
    throw new Exception('You are not allowed to override user');
  }
}
