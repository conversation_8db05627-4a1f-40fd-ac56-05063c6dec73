import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import moment from "moment";
import { OrderStatus, PaymentStatus } from "@/frontend-types";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: Date | undefined) {
  if (!date) return "N/A";
  return moment(date).format("MMM D, YYYY h:mm A");
}

export function formatCurrency(
  amount: number,
  locale: string = "en-IN",
  currency: string = "INR"
): string {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
  }).format(amount);
}

export const getOrderStatusColor = (status: OrderStatus) => {
  const colors = {
    PENDING: "bg-yellow-500/15 text-yellow-600",
    CONFIRMED: "bg-blue-500/15 text-blue-600",
    SHIPPED: "bg-purple-500/15 text-purple-600",
    DELIVERED: "bg-green-500/15 text-green-600",
    CANCELLED: "bg-red-500/15 text-red-600",
  };
  return colors[status] || "bg-secondary text-secondary-foreground";
};

export const getPaymentStatusColor = (status: PaymentStatus) => {
  const colors = {
    PAID: "bg-green-500/15 text-green-600",
    PENDING: "bg-yellow-500/15 text-yellow-600",
    FAILED: "bg-red-500/15 text-red-600",
    REFUNDED: "bg-secondary text-secondary-foreground",
  };
  return colors[status] || "bg-secondary text-secondary-foreground";
};
