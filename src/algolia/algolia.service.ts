import { Injectable, Logger } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { Algoliasearch, algoliasearch } from 'algoliasearch';

export interface AlgoliaProductRecord extends Record<string, unknown> {
  createdAt: number;
  updatedAt: number;
  objectID: string;
  productId: number;
  name: string;
  description: string;
  slug: string;
  barcode: string;
  categoryId: number;
  categoryName: string;
  categorySlug: string;
  active: boolean;
  thumbnailUrl?: string;
  weight: number;
  weightUnit: string;
  searchableText: string;
}

export interface AlgoliaSearchResult {
  hits: AlgoliaProductRecord[];
  nbHits: number;
  page: number;
  nbPages: number;
  hitsPerPage: number;
  processingTimeMS: number;
}

@Injectable()
export class AlgoliaService {
  private readonly logger = new Logger(AlgoliaService.name);
  private client: Algoliasearch | null = null;
  private indexName: string;

  constructor() {
    const appId = process.env.ALGOLIA_APP_ID;
    const apiKey = process.env.ALGOLIA_API_KEY;
    this.indexName = process.env.ALGOLIA_PRODUCTS_INDEX || 'products';

    if (!appId || !apiKey) {
      this.logger.warn(
        'Algolia credentials not found. Search functionality will be limited.',
      );
      return;
    }

    this.client = algoliasearch(appId, apiKey);
    this.configureIndex();
  }

  private async configureIndex() {
    if (!this.client) return;

    try {
      await this.client.setSettings({
        indexName: this.indexName,
        indexSettings: {
          searchableAttributes: [
            'name',
            'description',
            'barcode',
            'categoryName',
            'searchableText',
          ],
          attributesForFaceting: [
            'categoryId',
            'categoryName',
            'categorySlug',
            'active',
            'weightUnit',
          ],
          ranking: [
            'typo',
            'geo',
            'words',
            'filters',
            'proximity',
            'attribute',
            'exact',
            'custom',
          ],
          customRanking: ['desc(active)', 'desc(createdAt)'],
          attributesToRetrieve: ['*'],
          attributesToHighlight: ['name', 'description', 'categoryName'],
          hitsPerPage: 20,
          maxValuesPerFacet: 100,
        },
      });
      this.logger.log('Algolia index configured successfully');
    } catch (error) {
      this.logger.error('Failed to configure Algolia index:', error);
    }
  }

  async indexProduct(productRecord: AlgoliaProductRecord): Promise<void> {
    if (!this.client) {
      this.logger.warn('Algolia not configured. Skipping product indexing.');
      return;
    }

    try {
      await this.client.saveObject({
        indexName: this.indexName,
        body: productRecord,
      });
      this.logger.debug(`Product indexed: ${productRecord.objectID}`);
    } catch (error) {
      this.logger.error(
        `Failed to index product ${productRecord.objectID}:`,
        error,
      );
      throw error;
    }
  }

  async indexProducts(productRecords: AlgoliaProductRecord[]): Promise<void> {
    if (!this.client) {
      this.logger.warn('Algolia not configured. Skipping products indexing.');
      return;
    }

    try {
      await this.client.saveObjects({
        indexName: this.indexName,
        objects: productRecords,
      });
      this.logger.log(`${productRecords.length} products indexed successfully`);
    } catch (error) {
      this.logger.error('Failed to index products:', error);
      throw error;
    }
  }

  async updateProduct(productRecord: AlgoliaProductRecord): Promise<void> {
    return this.indexProduct(productRecord);
  }

  async deleteProduct(objectID: string): Promise<void> {
    if (!this.client) {
      this.logger.warn('Algolia not configured. Skipping product deletion.');
      return;
    }

    try {
      await this.client.deleteObject({
        indexName: this.indexName,
        objectID,
      });
      this.logger.debug(`Product deleted from index: ${objectID}`);
    } catch (error) {
      this.logger.error(`Failed to delete product ${objectID}:`, error);
      throw error;
    }
  }

  async deleteProducts(objectIDs: string[]): Promise<void> {
    if (!this.client) {
      this.logger.warn('Algolia not configured. Skipping products deletion.');
      return;
    }

    try {
      await this.client.deleteObjects({
        indexName: this.indexName,
        objectIDs,
      });
      this.logger.log(`${objectIDs.length} products deleted from index`);
    } catch (error) {
      this.logger.error('Failed to delete products:', error);
      throw error;
    }
  }

  async searchProducts(
    query: string,
    options: {
      page?: number;
      hitsPerPage?: number;
      filters?: string;
      facetFilters?: string[][];
      attributesToRetrieve?: string[];
    } = {},
  ): Promise<AlgoliaSearchResult> {
    if (!this.client) {
      this.logger.warn(
        'Algolia not configured. Returning empty search results.',
      );
      return {
        hits: [],
        nbHits: 0,
        page: 0,
        nbPages: 0,
        hitsPerPage: 0,
        processingTimeMS: 0,
      };
    }

    try {
      const result = await this.client.search({
        requests: [
          {
            indexName: this.indexName,
            query,
            page: options.page || 0,
            hitsPerPage: options.hitsPerPage || 20,
            filters: options.filters,
            facetFilters: options.facetFilters,
            attributesToRetrieve: options.attributesToRetrieve,
          },
        ],
      });

      const searchResult = result.results[0] as any;
      return {
        hits: searchResult.hits as AlgoliaProductRecord[],
        nbHits: searchResult.nbHits || 0,
        page: searchResult.page || 0,
        nbPages: searchResult.nbPages || 0,
        hitsPerPage: searchResult.hitsPerPage || 20,
        processingTimeMS: searchResult.processingTimeMS || 0,
      };
    } catch (error) {
      this.logger.error('Failed to search products:', error);
      throw error;
    }
  }

  async clearIndex(): Promise<void> {
    if (!this.client) {
      this.logger.warn('Algolia not configured. Skipping index clearing.');
      return;
    }

    try {
      await this.client.clearObjects({
        indexName: this.indexName,
      });
      this.logger.log('Algolia index cleared successfully');
    } catch (error) {
      this.logger.error('Failed to clear Algolia index:', error);
      throw error;
    }
  }

  // Helper method to generate object ID
  static generateObjectId(productId: number): string {
    return `product_${productId}`;
  }

  // Helper method to create searchable text
  static createSearchableText(
    product: Prisma.ProductGetPayload<{
      include: {
        category: true;
      };
    }>,
  ): string {
    const texts = [
      product.name,
      product.description,
      product.barcode,
      product.category?.name,
    ];

    return texts.filter(Boolean).join(' ').toLowerCase();
  }

  // Helper method to transform product to Algolia record
  static transformProductToAlgoliaRecord(
    product: Prisma.ProductGetPayload<{
      include: {
        category: true;
        thumbnail: true;
      };
    }>,
  ): AlgoliaProductRecord {
    return {
      objectID: this.generateObjectId(product.id),
      productId: product.id,
      name: product.name,
      description: product.description,
      barcode: product.barcode,
      categoryId: product.categoryId,
      categoryName: product.category?.name || '',
      categorySlug: product.category?.slug || '',
      active: product.active,
      thumbnailUrl: product.thumbnail?.url,
      weight: product.weight,
      weightUnit: product.weightUnit,
      slug: product.slug!,
      searchableText: this.createSearchableText(product),
      createdAt: new Date(product.createdAt).getTime(),
      updatedAt: new Date(product.updatedAt).getTime(),
    } satisfies AlgoliaProductRecord;
  }
}
