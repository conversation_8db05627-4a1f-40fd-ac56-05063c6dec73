"use client";

import { LoadingSpinner } from "@/components/LoadingSpinner";
import { useAuth } from "@/context";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";

export default function Layout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading && isAuthenticated()) {
      router.replace("/");
    }
  }, [isAuthenticated, router, isLoading]);

  // If not authenticated, don't render anything
  if (isLoading) {
    return (
      <main className="flex flex-row items-center justify-center h-screen">
        <LoadingSpinner className="" />
      </main>
    );
  }

  return <div>{children}</div>;
}
