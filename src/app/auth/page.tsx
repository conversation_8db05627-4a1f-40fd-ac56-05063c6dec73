"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { useAuth } from "@/context";
import Image from "next/image";

import logo from "@/assets/vegmove_original.png";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

function AuthPage() {
  const { login, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isAuthenticated() && !isLoading) {
      router.replace("/");
    }
  }, [isAuthenticated, router, isLoading]);

  const handleLogin = async () => {
    try {
      setError(null); // Clear previous errors
      await login(email, password); // Call login function with email and password
      console.log("Login successful!");
    } catch (err) {
      console.error("Login failed", err);
      setError("Invalid email or password");
    }
  };

  return (
    <>
      <div className="p-4 h-screen w-screen flex flex-col items-center content-center mt-48">
        <img
          src="/vegmove_original.png"
          alt="VegMove"
          width={100}
          className="mb-4 h-auto"
        />
        <div className="flex flex-row mb-4 justify-between items-center">
          <Card className="w-[400px]">
            <CardHeader>
              <CardTitle>Login</CardTitle>
              <CardDescription>Login to your account.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="space-y-1">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="password">Password</Label>
                <Input
                  type="password"
                  id="password"
                  name="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="••••••••"
                />
              </div>
              {error && <p className="text-red-500">{error}</p>}
            </CardContent>
            <CardFooter className="flex items-end justify-end">
              <Button onClick={handleLogin} disabled={isLoading}>
                {isLoading ? "Logging in..." : "Login"}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </>
  );
}

export default AuthPage;
