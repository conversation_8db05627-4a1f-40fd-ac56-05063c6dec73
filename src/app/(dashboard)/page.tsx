"use client";

import { apiService } from "@/api";
import TitleText from "@/components/TitleText";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { FormEvent, useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON>hart,
  CartesianGrid,
  Cell,
  Label,
  Legend,
  Line,
  LineChart,
  Pie,
  <PERSON><PERSON>,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

interface SummaryData {
  totalUsers: number;
  totalOrders: number;
  totalOrderAmount: number;
  totalWarehouses: number;
  totalWarehouseStaffs: number;
  totalDeliveryDrivers: number;
  totalCompletedOrders: number;
  totalPendingOrders: number;
}

interface ChartDatum {
  date: string;
  count: number;
}

interface TopProduct {
  id: number;
  name: string;
  totalSold: number;
}

interface CategoryRevenue {
  categoryId: number;
  categoryName: string;
  revenue: number;
}

// Color palette for charts
const COLORS = [
  "#3b82f6", // blue
  "#10b981", // green
  "#f59e0b", // amber
  "#ef4444", // red
  "#8b5cf6", // purple
  "#ec4899", // pink
  "#06b6d4", // cyan
  "#f97316", // orange
  "#6366f1", // indigo
  "#14b8a6", // teal
];

function AnalyticsPage() {
  const [summary, setSummary] = useState<SummaryData | null>(null);
  const [customerData, setCustomerData] = useState<ChartDatum[]>([]);
  const [orderData, setOrderData] = useState<ChartDatum[]>([]);
  const [stockData, setStockData] = useState<ChartDatum[]>([]);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [categoryRevenue, setCategoryRevenue] = useState<CategoryRevenue[]>([]);
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  // Set default date filter: last 30 days
  useEffect(() => {
    const today = new Date();
    // Default start date: 30 days prior to today
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    setStartDate(lastMonth.toISOString().split("T")[0]);
    setEndDate(today.toISOString().split("T")[0]);
  }, []);

  // Fetch summary data on initial load
  useEffect(() => {
    fetchSummaryData();
  }, []);

  // Fetch chart data on initial load and when date filters change
  useEffect(() => {
    if (startDate && endDate) {
      fetchChartData();
    }
  }, [startDate, endDate]);

  // Fetch top products and category revenue on initial load
  useEffect(() => {
    fetchProductsAndRevenueData();
  }, []);

  const fetchSummaryData = async () => {
    try {
      const response = await apiService.get("admin/analytics/summary");
      setSummary(response.data as SummaryData);
    } catch (error) {
      toast.error("Error fetching summary", {
        description: (error as Error).message,
      });
    }
  };

  const fetchChartData = async () => {
    if (!startDate || !endDate) return;

    setIsLoading(true);
    try {
      const [customersRes, ordersRes, stocksRes] = await Promise.all([
        apiService.get(
          `admin/analytics/customers?startDate=${startDate}&endDate=${endDate}`
        ),
        apiService.get(
          `admin/analytics/orders?startDate=${startDate}&endDate=${endDate}`
        ),
        apiService.get(
          `admin/analytics/stocks?startDate=${startDate}&endDate=${endDate}`
        ),
      ]);
      setCustomerData(customersRes.data);
      setOrderData(ordersRes.data);
      setStockData(stocksRes.data);
    } catch (error) {
      toast.error("Error fetching chart data", {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchProductsAndRevenueData = async () => {
    try {
      const [topProductsRes, categoryRevenueRes] = await Promise.all([
        apiService.get("admin/analytics/top-products?limit=10"),
        apiService.get("admin/analytics/category-revenue"),
      ]);
      setTopProducts(topProductsRes.data);
      setCategoryRevenue(categoryRevenueRes.data);
    } catch (error) {
      toast.error("Error fetching products and revenue data", {
        description: (error as Error).message,
      });
    }
  };

  // Line chart renderer with improved styling
  const renderLineChart = (data: ChartDatum[], yAxisLabel: string) => (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart
        data={data}
        margin={{ top: 20, right: 30, left: 30, bottom: 30 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
        <XAxis
          dataKey="date"
          stroke="#6b7280"
          tick={{ fontSize: 12 }}
          tickFormatter={(date) =>
            new Date(date).toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
            })
          }
        >
          <Label
            value="Date"
            offset={-10}
            position="insideBottom"
            style={{ fontSize: 12, fill: "#6b7280" }}
          />
        </XAxis>
        <YAxis stroke="#6b7280" tick={{ fontSize: 12 }}>
          <Label
            value={yAxisLabel}
            angle={-90}
            position="insideLeft"
            style={{ textAnchor: "middle", fontSize: 12, fill: "#6b7280" }}
          />
        </YAxis>
        <Tooltip
          contentStyle={{
            backgroundColor: "white",
            borderRadius: "6px",
            boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
          }}
          formatter={(value) => [`${value} ${yAxisLabel}`, ""]}
          labelFormatter={(date) =>
            new Date(date).toLocaleDateString("en-US", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            })
          }
        />
        <Line
          type="monotone"
          dataKey="count"
          stroke="#3b82f6"
          strokeWidth={2}
          dot={{ r: 4, strokeWidth: 2 }}
          activeDot={{ r: 8 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );

  // Bar chart for top products
  const renderTopProductsChart = () => (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart
        data={topProducts}
        layout="vertical"
        margin={{ top: 20, right: 30, left: 100, bottom: 20 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
        <XAxis type="number" stroke="#6b7280" tick={{ fontSize: 12 }}>
          <Label
            value="Units Sold"
            offset={-10}
            position="insideBottom"
            style={{ fontSize: 12, fill: "#6b7280" }}
          />
        </XAxis>
        <YAxis
          dataKey="name"
          type="category"
          stroke="#6b7280"
          tick={{ fontSize: 12 }}
          width={90}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: "white",
            borderRadius: "6px",
            boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
          }}
          formatter={(value) => [`${value} units`, "Total Sold"]}
        />
        <Bar dataKey="totalSold" fill="#3b82f6" radius={[0, 4, 4, 0]}>
          {topProducts.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  );

  // Pie chart for category revenue
  const renderCategoryRevenueChart = () => (
    <ResponsiveContainer width="100%" height={350}>
      <PieChart>
        <Pie
          data={categoryRevenue}
          cx="50%"
          cy="50%"
          labelLine={false}
          outerRadius={120}
          innerRadius={60}
          dataKey="revenue"
          nameKey="categoryName"
          label={({ name, percent }) =>
            `${name}: ${(percent * 100).toFixed(0)}%`
          }
        >
          {categoryRevenue.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip
          contentStyle={{
            backgroundColor: "white",
            borderRadius: "6px",
            boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
          }}
          formatter={(value) => [
            `${formatCurrency(+value.toString())}`,
            "Revenue",
          ]}
        />
        <Legend
          verticalAlign="bottom"
          height={36}
          layout="horizontal"
          formatter={(value) => <span style={{ fontSize: 12 }}>{value}</span>}
        />
      </PieChart>
    </ResponsiveContainer>
  );

  // Format currency
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
    }).format(value);
  };

  return (
    <div className="p-6 space-y-6">
      <TitleText title="Analytics" />
      <Separator />

      {/* Summary Cards */}
      <section className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        {summary ? (
          <>
            <Card className="bg-blue-600">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white">
                  Total Users
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl text-white font-bold">
                  {summary.totalUsers}
                </p>
              </CardContent>
            </Card>
            <Card className="bg-green-600">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white">
                  Total Orders
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl text-white font-bold">
                  {summary.totalOrders}
                </p>
              </CardContent>
            </Card>
            <Card className="bg-amber-600">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white">
                  Total Revenue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl text-white font-bold">
                  {formatCurrency(summary.totalOrderAmount)}
                </p>
              </CardContent>
            </Card>
            <Card className="bg-purple-600">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-white">
                  Completed Orders
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl text-white font-bold">
                  {summary.totalCompletedOrders}
                </p>
              </CardContent>
            </Card>
          </>
        ) : (
          <div className="col-span-4 flex justify-center p-8">
            <p className="text-gray-500">Loading summary...</p>
          </div>
        )}
      </section>

      {/* Sales and Revenue Charts */}
      <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Top Selling Products</CardTitle>
          </CardHeader>
          <CardContent>
            {topProducts.length > 0 ? (
              renderTopProductsChart()
            ) : (
              <div className="h-64 flex items-center justify-center">
                <p className="text-gray-500">No data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Revenue by Category</CardTitle>
          </CardHeader>
          <CardContent>
            {categoryRevenue.length > 0 ? (
              renderCategoryRevenueChart()
            ) : (
              <div className="h-64 flex items-center justify-center">
                <p className="text-gray-500">No data available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </section>

      <Separator />

      {/* Chart Filters */}
      <section className=" p-4 rounded-lg shadow-sm border">
        <h2 className="text-lg font-medium mb-4">Date Range Filter</h2>
        <form
          className="flex flex-wrap items-end gap-4"
          onSubmit={(e: FormEvent) => {
            e.preventDefault();
            fetchChartData();
          }}
        >
          <div>
            <label className="text-sm font-medium text-gray-700 block mb-1">
              Start Date
            </label>
            <Input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-40"
            />
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700 block mb-1">
              End Date
            </label>
            <Input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-40"
            />
          </div>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Loading..." : "Update Charts"}
          </Button>
        </form>
      </section>

      {/* Time Series Charts */}
      <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Customer Signups</CardTitle>
          </CardHeader>
          <CardContent>
            {customerData.length > 0 ? (
              renderLineChart(customerData, "Signups")
            ) : (
              <div className="h-64 flex items-center justify-center">
                <p className="text-gray-500">No data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Orders</CardTitle>
          </CardHeader>
          <CardContent>
            {orderData.length > 0 ? (
              renderLineChart(orderData, "Orders")
            ) : (
              <div className="h-64 flex items-center justify-center">
                <p className="text-gray-500">No data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle>Stock Changes</CardTitle>
          </CardHeader>
          <CardContent>
            {stockData.length > 0 ? (
              renderLineChart(stockData, "Units")
            ) : (
              <div className="h-64 flex items-center justify-center">
                <p className="text-gray-500">No data available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </section>

      {/* Additional Stats */}
      <section className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        {summary ? (
          <>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Warehouses
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xl font-bold">{summary.totalWarehouses}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Warehouse Staff
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xl font-bold">
                  {summary.totalWarehouseStaffs}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Delivery Drivers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xl font-bold">
                  {summary.totalDeliveryDrivers}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Pending Orders
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-xl font-bold">
                  {summary.totalPendingOrders}
                </p>
              </CardContent>
            </Card>
          </>
        ) : null}
      </section>
    </div>
  );
}

export default AnalyticsPage;
