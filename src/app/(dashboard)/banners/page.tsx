"use client";

import { apiService } from "@/api";
import { toast } from "sonner";
import { formatDate } from "@/lib/utils";
import { useEffect, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { zodResolver } from "@hookform/resolvers/zod";
import { Edit, Globe, ImageIcon, PlusIcon, Trash2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { ImageUpload } from "@/components/image-upload";

// Define a constant for the global option value - we'll use "global" instead of empty string
const GLOBAL_VALUE = "global";

// Define the schema for banner form - make languageId optional
const bannerSchema = z.object({
  languageId: z.string().optional(),
  active: z.boolean().default(true),
});

interface Language {
  id: number;
  name: string;
  code: string;
  flagUrl?: string;
}

interface Banner {
  id: number;
  active: boolean;
  mediaId: number;
  languageId: number | null;
  createdAt: string;
  updatedAt: string;
  media: {
    id: number;
    url: string;
    mediaType: string;
  } | null;
  language?: Language;
}

export default function BannersPage() {
  const [banners, setBanners] = useState<Banner[]>([]);
  const [languages, setLanguages] = useState<Language[]>([]);
  const [loading, setLoading] = useState(true);

  // Add-banner dialog state
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newBannerFile, setNewBannerFile] = useState<File | null>(null);
  const [newBannerPreview, setNewBannerPreview] = useState<string>("");

  // Edit dialog state
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [bannerToEdit, setBannerToEdit] = useState<Banner | null>(null);

  // Delete dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [bannerToDelete, setBannerToDelete] = useState<Banner | null>(null);

  // Form for creating banner
  const createForm = useForm<z.infer<typeof bannerSchema>>({
    resolver: zodResolver(bannerSchema),
    defaultValues: {
      languageId: GLOBAL_VALUE,
      active: true,
    },
  });

  // Form for editing banner
  const editForm = useForm<z.infer<typeof bannerSchema>>({
    resolver: zodResolver(bannerSchema),
    defaultValues: {
      languageId: GLOBAL_VALUE,
      active: true,
    },
  });

  // Fetch list of banners
  const fetchBanners = async () => {
    setLoading(true);
    try {
      const res = await apiService.get("admin/banners");
      setBanners(res.data as Banner[]);
    } catch (err) {
      console.error(err);
      toast.error("Error", {
        description: "Failed to load banners",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch languages
  const fetchLanguages = async () => {
    try {
      const res = await apiService.get("languages");
      setLanguages(res.data || []);
    } catch (error) {
      toast.error("Failed to load languages", {
        description: (error as Error).message,
      });
    }
  };

  useEffect(() => {
    fetchBanners();
    fetchLanguages();
  }, []);

  // Add new banner
  const handleAddBanner = async (values: z.infer<typeof bannerSchema>) => {
    if (!newBannerFile) {
      toast.error("No image selected", {
        description: "Please pick an image before submitting.",
      });
      return;
    }

    let uploadedUrl: string;
    try {
      const formData = new FormData();
      formData.append("file", newBannerFile);

      const uploadRes = await apiService.post("admin/media/upload", formData, {
        ContentType: "multipart/form-data",
      });
      uploadedUrl = uploadRes.data.url;
    } catch (err: any) {
      console.error("Image upload failed:", err);
      toast("Upload failed", {
        description: err.message || "Could not upload image.",
      });
      return;
    }

    // Prepare payload - if languageId is global, don't include it
    const payload: any = {
      imageUrl: uploadedUrl,
      active: values.active,
    };

    // Only add languageId to payload if it's not global
    if (values.languageId && values.languageId !== GLOBAL_VALUE) {
      payload.languageId = Number(values.languageId);
    }

    try {
      await apiService.post("admin/banners", payload);

      toast.success("Success", {
        description: "Banner added.",
      });
      setIsAddDialogOpen(false);
      // reset dialog state
      setNewBannerFile(null);
      setNewBannerPreview("");
      createForm.reset({
        languageId: GLOBAL_VALUE,
        active: true,
      });
      // refresh table
      fetchBanners();
    } catch (err) {
      console.error("Adding banner failed:", err);
      toast.error("Error", {
        description: "Failed to add banner.",
      });
    }
  };

  // Open edit dialog
  const openEditDialog = (banner: Banner) => {
    setBannerToEdit(banner);
    editForm.reset({
      languageId: banner.languageId ? String(banner.languageId) : GLOBAL_VALUE,
      active: banner.active,
    });
    setIsEditDialogOpen(true);
  };

  // Update banner
  const handleUpdateBanner = async (values: z.infer<typeof bannerSchema>) => {
    if (!bannerToEdit) return;

    // Prepare payload
    const payload: any = {
      active: values.active,
    };

    // If languageId is not global, include it - otherwise set it to null explicitly
    if (values.languageId && values.languageId !== GLOBAL_VALUE) {
      payload.languageId = Number(values.languageId);
    } else {
      payload.languageId = null;
    }

    try {
      await apiService.put(`admin/banners/${bannerToEdit.id}`, payload);

      toast.success("Updated", {
        description: "Banner updated successfully.",
      });
      setIsEditDialogOpen(false);
      fetchBanners();
    } catch (err) {
      console.error(err);
      toast.error("Error", {
        description: "Could not update banner.",
      });
    }
  };

  // Toggle active flag
  const handleToggleActive = async (id: number, active: boolean) => {
    try {
      await apiService.put(`admin/banners/${id}`, { active: !active });
      toast.success("Updated", { description: "Banner status changed." });
      fetchBanners();
    } catch (err) {
      console.error(err);
      toast.error("Error", {
        description: "Could not update banner.",
      });
    }
  };

  // Delete
  const openDeleteDialog = (banner: Banner) => {
    setBannerToDelete(banner);
    setDeleteDialogOpen(true);
  };

  const handleDeleteBanner = async () => {
    if (!bannerToDelete) return;
    try {
      await apiService.delete(`admin/banners/${bannerToDelete.id}`);
      toast.success("Deleted", { description: "Banner removed." });
      setDeleteDialogOpen(false);
      fetchBanners();
    } catch (err) {
      console.error(err);
      toast.error("Error", {
        description: "Could not delete banner.",
      });
    }
  };

  // Function to render language name or Global indicator
  const renderLanguage = (languageId: number | null) => {
    if (!languageId) {
      return (
        <div className="flex items-center gap-1 text-blue-600">
          <Globe className="h-4 w-4" />
          <span>Global</span>
        </div>
      );
    }
    return languages.find((l) => l.id === languageId)?.name || "Unknown";
  };

  return (
    <div className="p-4">
      {/* Header + Add button */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Banners</h1>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="default">
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Banner
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-gray-50 max-w-3xl">
            <DialogHeader>
              <DialogTitle>New Banner</DialogTitle>
              <DialogDescription>
                Upload an image to create a banner.
              </DialogDescription>
            </DialogHeader>

            <Form {...createForm}>
              <form
                onSubmit={createForm.handleSubmit(handleAddBanner)}
                className="space-y-4 py-2"
              >
                <FormField
                  control={createForm.control}
                  name="languageId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Language (Optional)</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || GLOBAL_VALUE}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select language" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={GLOBAL_VALUE}>
                              <div className="flex items-center gap-2">
                                <Globe className="h-4 w-4" />
                                <span>Global (All Languages)</span>
                              </div>
                            </SelectItem>
                            {languages.map((lang) => (
                              <SelectItem
                                key={lang.id}
                                value={lang.id.toString()}
                              >
                                <div className="flex items-center gap-2">
                                  {lang.flagUrl && (
                                    <img
                                      src={lang.flagUrl}
                                      alt={lang.code}
                                      width={16}
                                      height={16}
                                      className="w-4 h-4"
                                    />
                                  )}
                                  <span>{lang.name}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div>
                  <ImageUpload
                    value={newBannerPreview}
                    onChange={(url, file) => {
                      setNewBannerPreview(url);
                      setNewBannerFile(file || null);
                    }}
                    label="Choose Banner Image"
                    id="banner-image-upload"
                  />
                </div>

                <FormField
                  control={createForm.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Active</FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button type="submit">Create Banner</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <Separator />

      {/* Table */}
      {loading ? (
        <div className="text-center py-10">Loading…</div>
      ) : (
        <div className="mt-4 rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Image</TableHead>
                <TableHead>Language</TableHead>
                <TableHead>Active</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {banners.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-6">
                    No banners yet.
                  </TableCell>
                </TableRow>
              )}
              {banners.map((b) => (
                <TableRow key={b.id}>
                  <TableCell>{b.id}</TableCell>
                  <TableCell>
                    {b.media?.url ? (
                      <div className="w-24 h-12 relative">
                        <img
                          src={b.media.url}
                          alt={`Banner ${b.id}`}
                          className="object-cover rounded"
                        />
                      </div>
                    ) : (
                      <div className="w-24 h-12 bg-gray-100 flex items-center justify-center rounded">
                        <ImageIcon className="h-5 w-5 text-gray-400" />
                      </div>
                    )}
                  </TableCell>
                  <TableCell>{renderLanguage(b.languageId)}</TableCell>
                  <TableCell>
                    <Switch
                      checked={b.active}
                      onCheckedChange={() => handleToggleActive(b.id, b.active)}
                    />
                  </TableCell>
                  <TableCell>{formatDate(new Date(b.createdAt))}</TableCell>
                  <TableCell className="flex gap-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => openEditDialog(b)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="icon"
                      onClick={() => openDeleteDialog(b)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog
        open={isEditDialogOpen}
        onOpenChange={(open) => {
          if (!open) setBannerToEdit(null);
          setIsEditDialogOpen(open);
        }}
      >
        <DialogContent className="bg-gray-50 max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Banner</DialogTitle>
            <DialogDescription>Update banner details.</DialogDescription>
          </DialogHeader>

          <Form {...editForm}>
            <form
              onSubmit={editForm.handleSubmit(handleUpdateBanner)}
              className="space-y-4 py-2"
            >
              <FormField
                control={editForm.control}
                name="languageId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Language (Optional)</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value || GLOBAL_VALUE}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={GLOBAL_VALUE}>
                            <div className="flex items-center gap-2">
                              <Globe className="h-4 w-4" />
                              <span>Global (All Languages)</span>
                            </div>
                          </SelectItem>
                          {languages.map((lang) => (
                            <SelectItem
                              key={lang.id}
                              value={lang.id.toString()}
                            >
                              <div className="flex items-center gap-2">
                                {lang.flagUrl && (
                                  <img
                                    src={lang.flagUrl}
                                    alt={lang.code}
                                    width={16}
                                    height={16}
                                    className="w-4 h-4"
                                  />
                                )}
                                <span>{lang.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>Active</FormLabel>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="submit">Save Changes</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete confirm */}
      <Dialog
        open={deleteDialogOpen}
        onOpenChange={(open) => {
          if (!open) setBannerToDelete(null);
          setDeleteDialogOpen(open);
        }}
      >
        <DialogContent className="bg-gray-50 max-w-3xl">
          <DialogHeader>
            <DialogTitle>Delete Banner</DialogTitle>
            <DialogDescription>
              {" Are you sure? This action can't be undone."}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="space-x-2">
            <Button
              variant="outline"
              onClick={() => setDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteBanner}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
