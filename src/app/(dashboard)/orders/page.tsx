"use client";

import { apiService } from "@/api";
import { OrderTable } from "@/components/orders/OrderTable";
import { OrderPreviewDialog } from "@/components/orders/OrderPreviewDialog";
import PaginationBar from "@/components/PaginationBar";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { Order, PaginationData } from "@/frontend-types";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";

function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 1,
  });
  const [previewOrder, setPreviewOrder] = useState<Order | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const router = useRouter();

  useEffect(() => {
    async function fetchOrders() {
      try {
        const response = await apiService.get("admin/orders", {
          page: pagination.page,
          pageSize: pagination.pageSize,
        });

        if (!response.data) {
          throw new Error("Could not get orders.");
        }

        const { data: orders, ...paginationData } = response.data;

        setOrders(orders as Order[]);
        setPagination(paginationData as PaginationData);
      } catch (error) {
        toast.error((error as Error).message, {
          description: `${error}`,
        });
      }
    }

    fetchOrders();
  }, [pagination.page, pagination.pageSize]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const handlePreview = (order: Order) => {
    setPreviewOrder(order);
    setIsPreviewOpen(true);
  };

  return (
    <div className="p-4">
      <div className="flex flex-row mb-4 justify-between items-center">
        <h1 className="font-bold text-2xl">Orders</h1>
        <Button onClick={() => router.push("/orders/create")}>
          Create Order
        </Button>
      </div>
      <Separator />
      <div>
        <OrderTable
          orders={orders}
          onClick={(order) => {
            router.push(`/orders/${order.id}`);
          }}
          onPreview={handlePreview}
        />

        <PaginationBar
          pagination={pagination}
          handlePageChange={handlePageChange}
          className="mt-12"
        />
      </div>

      <OrderPreviewDialog
        order={previewOrder}
        open={isPreviewOpen}
        onOpenChange={setIsPreviewOpen}
      />
    </div>
  );
}

export default OrdersPage;
