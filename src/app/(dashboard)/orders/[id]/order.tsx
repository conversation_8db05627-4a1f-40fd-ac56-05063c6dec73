"use client";

import { apiService } from "@/api";
import ItemPicker from "@/components/ItemPicker";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { formatCurrency, formatDate } from "@/lib/utils";
import {
  Order,
  OrderStatus,
  PaginatedResponse,
  PaymentStatus,
  User,
} from "@/frontend-types";
import {
  Calendar,
  ChevronLeft,
  Clock,
  CreditCard,
  Download,
  Edit,
  History,
  HistoryIcon,
  MapPin,
  NotebookPen,
  Package,
  Truck,
  User as UserIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function OrderDetails({ id }: { id: string }) {
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditingDelivery, setIsEditingDelivery] = useState(false);
  const [deliveryForm, setDeliveryForm] = useState({
    deliveryDate: "",
    deliveryStartTime: "",
    deliveryEndTime: "",
  });
  const router = useRouter();

  useEffect(() => {
    if (!id) return;

    async function fetchOrderDetails() {
      try {
        setIsLoading(true);
        const response = await apiService.get(`admin/orders/${id}`);
        if (!response.data) throw new Error("Could not get order details.");
        const orderData = response.data as Order;
        setOrder(orderData);

        // Initialize delivery form with current values
        setDeliveryForm({
          deliveryDate: new Date(orderData.deliveryDate)
            .toISOString()
            .split("T")[0],
          deliveryStartTime: orderData.deliveryStartTime,
          deliveryEndTime: orderData.deliveryEndTime,
        });
      } catch (error) {
        console.error(error);
        toast.error("Error fetching order", {
          description: `${error}`,
        });
      } finally {
        setIsLoading(false);
      }
    }
    fetchOrderDetails();
  }, [id]);

  const handleUpdateStatus = async (status: OrderStatus) => {
    try {
      const response = await apiService.patch(`admin/orders/${id}`, {
        orderStatus: status,
      });

      if (!response.data) throw new Error("Could not update order status.");

      setOrder((prev) =>
        prev
          ? {
              ...prev,
              status,
              orderStatusHistory: (response.data as Order).orderStatusHistory,
            }
          : null
      );
      toast.success("Status Updated", {
        description: `Order status has been updated to ${status}`,
      });
    } catch (error) {
      console.error(error);
      toast.error("Error updating status", {
        description: `${error}`,
      });
    }
  };

  const handleUpdatePaymentStatus = async (status: PaymentStatus) => {
    try {
      const response = await apiService.patch(`admin/orders/${id}`, {
        paymentStatus: status,
      });

      if (!response.data) throw new Error("Could not update payment status.");

      setOrder((prev) =>
        prev
          ? {
              ...prev,
              paymentStatus: status,
              orderStatusHistory: (response.data as Order).orderStatusHistory,
            }
          : null
      );
      toast.success("Payment Status Updated", {
        description: `Payment status has been updated to ${status}`,
      });
    } catch (error) {
      console.error(error);
      toast.error("Error updating payment status", {
        description: `${error}`,
      });
    }
  };

  const handleAssignDriver = async (driverId: number) => {
    try {
      const response = await apiService.patch(`admin/orders/${id}`, {
        deliveryDriverId: driverId,
      });

      if (!response.data) throw new Error("Could not assign delivery driver.");

      setOrder((prev) =>
        prev ? { ...prev, deliveryDriver: response.data.deliveryDriver } : null
      );

      toast.success("Driver Assigned", {
        description: `Assigned to ${response.data.deliveryDriver.name}`,
      });
    } catch (error) {
      toast.error("Error assigning driver", {
        description: `${error}`,
      });
    }
  };

  // Utility function to ensure time is in 24-hour format (HH:mm)
  const formatTimeTo24Hour = (timeString: string): string => {
    // If the time is already in HH:mm format, return as is
    if (/^([01]\d|2[0-3]):([0-5]\d)$/.test(timeString)) {
      return timeString;
    }

    // If it's in 12-hour format, convert to 24-hour
    const time12h = timeString.match(/^(\d{1,2}):(\d{2})\s*(AM|PM)$/i);
    if (time12h) {
      let hours = parseInt(time12h[1]);
      const minutes = time12h[2];
      const period = time12h[3].toUpperCase();

      if (period === "PM" && hours !== 12) {
        hours += 12;
      } else if (period === "AM" && hours === 12) {
        hours = 0;
      }

      return `${hours.toString().padStart(2, "0")}:${minutes}`;
    }

    return timeString; // Return as is if format is unrecognized
  };

  const handleUpdateDelivery = async () => {
    try {
      // Ensure times are in 24-hour format
      const startTime24h = formatTimeTo24Hour(deliveryForm.deliveryStartTime);
      const endTime24h = formatTimeTo24Hour(deliveryForm.deliveryEndTime);

      // Validate time format
      const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
      if (!timeRegex.test(startTime24h) || !timeRegex.test(endTime24h)) {
        toast.error("Invalid time format", {
          description: "Please use 24-hour format (HH:mm)",
        });
        return;
      }

      // Validate that start time is before end time
      const [startHour, startMin] = startTime24h.split(":").map(Number);
      const [endHour, endMin] = endTime24h.split(":").map(Number);
      const startMinutes = startHour * 60 + startMin;
      const endMinutes = endHour * 60 + endMin;

      if (startMinutes >= endMinutes) {
        toast.error("Invalid time range", {
          description: "Start time must be before end time",
        });
        return;
      }

      const response = await apiService.patch(`admin/orders/${id}/delivery`, {
        deliveryDate: new Date(deliveryForm.deliveryDate),
        deliveryStartTime: startTime24h,
        deliveryEndTime: endTime24h,
      });

      if (!response.data) throw new Error("Could not update delivery details.");

      setOrder((prev) =>
        prev
          ? {
              ...prev,
              deliveryDate: new Date(deliveryForm.deliveryDate),
              deliveryStartTime: startTime24h,
              deliveryEndTime: endTime24h,
            }
          : null
      );

      setIsEditingDelivery(false);
      toast.success("Delivery Updated", {
        description: "Delivery date and time have been updated successfully",
      });
    } catch (error) {
      console.error(error);
      toast.error("Error updating delivery", {
        description: `${error}`,
      });
    }
  };

  const fetchDeliveryMen = async (
    page: number,
    pageSize: number,
    search: string
  ): Promise<PaginatedResponse<User>> => {
    const resp = await apiService.get("admin/delivery-drivers", {
      page,
      pageSize,
      search,
      warehouseId: order?.warehouse?.id,
    });
    return resp.data as PaginatedResponse<User>;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (!order) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>Order not found</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-[1600px] mx-auto p-6">
        <Button variant="ghost" onClick={() => router.back()} className="mb-6">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Orders
        </Button>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Main Content - Left Side */}
          <div className="flex-grow space-y-6 max-w-[1000px]">
            <div className="bg-card p-6 border rounded-lg">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h1 className="text-2xl font-semibold mb-1">
                    Order #{order.id}
                  </h1>
                  <div className="flex items-center text-muted-foreground text-sm">
                    <Calendar className="h-4 w-4 mr-1" />
                    {formatDate(order.createdAt)}
                  </div>
                  <div className="mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1 text-xs"
                      onClick={() => {
                        const token = localStorage.getItem("token");
                        // window.open(
                        //   `${process.env.NEXT_PUBLIC_API_URL}/invoices/${order.id}`,
                        //   "_blank"
                        // );
                        fetch(
                          `${process.env.NEXT_PUBLIC_API_URL}/invoices/${order.id}`,
                          {
                            headers: {
                              Authorization: `Bearer ${token}`,
                            },
                          }
                        )
                          .then((res) => res.blob())
                          .then((blob) => {
                            const url = window.URL.createObjectURL(blob);
                            window.open(url, "_blank");
                          });
                      }}
                    >
                      <Download className="h-3 w-3" />
                      Download Invoice
                    </Button>
                  </div>
                  {order.note && (
                    <>
                      <div className="mt-8 flex items-center text-foreground text-base font-semibold">
                        <NotebookPen className="h-4 w-4 mr-2" />
                        Order Note
                      </div>
                      <div className="space-y-2">
                        {" "}
                        <p className="font-medium">{order.note}</p>
                      </div>
                    </>
                  )}
                </div>
                <Badge>{order.status}</Badge>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h2 className="flex items-center text-base font-semibold mb-3">
                    <UserIcon className="h-4 w-4 mr-2" />
                    Customer Details
                  </h2>
                  <div className="space-y-2">
                    <p className="font-medium">{order.user.name}</p>
                    <p className="text-muted-foreground">{order.user.email}</p>
                    <p className="text-muted-foreground">{order.user.phone}</p>
                    <p className="text-sm text-muted-foreground mt-4">
                      Customer since {formatDate(order.user.createdAt)}
                    </p>
                  </div>
                </div>

                <div>
                  <h2 className="flex items-center text-base font-semibold mb-3">
                    <MapPin className="h-4 w-4 mr-2" />
                    Delivery Address
                  </h2>
                  <div className="space-y-1 text-muted-foreground">
                    <p>{order.address.apartment}</p>
                    <p>{order.address.block}</p>
                    <p>{order.address.streetName}</p>
                    <p>
                      {order.address.city}, {order.address.state}{" "}
                      {order.address.zipCode}
                    </p>
                  </div>
                </div>

                <div>
                  <h2 className="flex items-center text-base font-semibold mb-3">
                    <HistoryIcon className="h-4 w-4 mr-2" />
                    Order History
                  </h2>
                  <div className="space-y-1 text-muted-foreground">
                    <p>
                      <b>Total orders: </b>
                      {order.user.totalOrders}
                    </p>
                    <p>
                      <b>Total order value: </b>
                      Rs. {order.user.totalOrderPrice}
                    </p>
                    <p>
                      <b>Average order value: </b> Rs.{" "}
                      {order.user.averageOrderPrice}
                    </p>
                  </div>
                </div>

                {order.warehouse && (
                  <div>
                    <h2 className="flex items-center text-base font-semibold mb-3">
                      <Package className="h-4 w-4 mr-2" />
                      Warehouse
                    </h2>
                    <div className="space-y-1 text-muted-foreground">
                      <p>
                        <b>Name: </b>
                        {order.warehouse.name}
                      </p>
                      <p>
                        <b>Type: </b>
                        {order.warehouse.type}
                      </p>
                      <p>
                        <b>Location: </b>
                        {order.warehouse.lat}, {order.warehouse.long}
                      </p>
                      <p>
                        <b>Status: </b>
                        <span
                          className={
                            order.warehouse.active
                              ? "text-green-600"
                              : "text-red-600"
                          }
                        >
                          {order.warehouse.active ? "Active" : "Inactive"}
                        </span>
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-card rounded-lg border">
              <div className="p-6 border-b">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="flex items-center text-base font-semibold">
                    <Package className="h-4 w-4 mr-2" />
                    Order Items
                  </h2>
                  <p className="text-muted-foreground text-sm">
                    Total: {formatCurrency(+order.totalAmount)}
                  </p>
                </div>
              </div>

              <div className="divide-y">
                {order.items.map((item) => (
                  <div key={item.id} className="p-6">
                    <div className="flex gap-4">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <div className="h-20 w-20 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden">
                          {item.product.thumbnail ? (
                            <img
                              src={item.product.thumbnail.url}
                              alt={item.product.name}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <Package className="h-10 w-10 text-gray-400" />
                          )}
                        </div>
                      </div>

                      {/* Product Details */}
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <div className="space-y-3">
                            <div>
                              <p className="font-medium text-lg">
                                {item.product.name}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {item.product.category.name}
                              </p>
                            </div>

                            {/* Variation Information */}
                            {item.variation &&
                              item.variation.options &&
                              item.variation.options.length > 0 && (
                                <div className="space-y-2">
                                  <p className="text-sm font-medium text-gray-700">
                                    Variation:
                                  </p>
                                  <div className="flex flex-wrap gap-3">
                                    {item.variation.options.map(
                                      (optionMapping) => (
                                        <div
                                          key={optionMapping.option.id}
                                          className="flex items-center space-x-2 bg-gray-50 px-3 py-1 rounded-md"
                                        >
                                          <span className="text-sm text-gray-600">
                                            {
                                              optionMapping.option.attribute
                                                .name
                                            }
                                            :
                                          </span>
                                          <div className="flex items-center space-x-1">
                                            {optionMapping.option.colorCode && (
                                              <div
                                                className="w-4 h-4 rounded-full border border-gray-300 shadow-sm"
                                                style={{
                                                  backgroundColor:
                                                    optionMapping.option
                                                      .colorCode,
                                                }}
                                                title={
                                                  optionMapping.option.name
                                                }
                                              />
                                            )}
                                            <span className="text-sm font-medium text-gray-800">
                                              {optionMapping.option.name}
                                            </span>
                                          </div>
                                        </div>
                                      )
                                    )}
                                  </div>
                                </div>
                              )}

                            <div className="flex gap-8 text-sm text-muted-foreground">
                              <div>
                                <p>Slug: {item.product.slug}</p>
                                <p>Quantity: {item.quantity}</p>
                              </div>
                              <div>
                                <p>Stock: {item.inventory.quantity}</p>
                                <p>Batch: {item.inventory.batch.name}</p>
                                <p>
                                  Warehouse:{" "}
                                  {item.inventory.batch.warehouse?.name}
                                </p>
                              </div>
                              {item.inventory.expiryDate && (
                                <div>
                                  <p>
                                    Expires:{" "}
                                    {formatDate(item.inventory.expiryDate)}
                                  </p>
                                </div>
                              )}
                            </div>
                            <div className="flex gap-2 text-sm">
                              <Badge variant="secondary">
                                W: {item.product.weight}g
                              </Badge>
                              <Badge variant="secondary">
                                L: {item.product.length}cm
                              </Badge>
                              <Badge variant="secondary">
                                W: {item.product.width}cm
                              </Badge>
                              <Badge variant="secondary">
                                H: {item.product.height}cm
                              </Badge>
                            </div>
                          </div>

                          {/* Pricing Information */}
                          <div className="text-right space-y-2">
                            <div>
                              <p className="text-sm text-muted-foreground">
                                Price
                              </p>
                              <div className="space-y-1">
                                <p className="font-medium text-lg">
                                  {formatCurrency(+item.price)}
                                </p>
                                {Number(item.originalPrice) >
                                  Number(item.price) && (
                                  <p className="text-sm text-muted-foreground line-through">
                                    {formatCurrency(+item.originalPrice)}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">
                                GST
                              </p>
                              <p className="font-medium">
                                {formatCurrency(+item.gstAmount)}
                              </p>
                            </div>
                            <div className="pt-2 border-t">
                              <p className="text-sm text-muted-foreground">
                                Item Total (x{item.quantity})
                              </p>
                              <p className="font-bold text-lg">
                                {formatCurrency(+item.price)}
                              </p>
                              {Number(item.originalPrice) >
                                Number(item.price) && (
                                <p className="text-sm text-muted-foreground line-through">
                                  {formatCurrency(+item.originalPrice)}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="p-6 border-t bg-muted/50">
                <div className="space-y-4">
                  {/* Pricing Breakdown */}
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Subtotal:</span>
                      <span>{formatCurrency(+order.subtotal)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">
                        Handling Charge:
                      </span>
                      <span>{formatCurrency(+order.handlingCharge)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">
                        Delivery Fee:
                      </span>
                      <span>{formatCurrency(+order.deliveryFee)}</span>
                    </div>
                    {Number(order.discountAmount) > 0 && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>Discount:</span>
                        <span>-{formatCurrency(+order.discountAmount)}</span>
                      </div>
                    )}
                    {Number(order.couponDiscount) > 0 && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>Coupon Discount:</span>
                        <span>-{formatCurrency(+order.couponDiscount)}</span>
                      </div>
                    )}
                    {Number(order.rewardDiscount) > 0 && (
                      <div className="flex justify-between text-sm text-green-600">
                        <span>Reward Discount:</span>
                        <span>-{formatCurrency(+order.rewardDiscount)}</span>
                      </div>
                    )}
                    <div className="border-t pt-3">
                      <div className="flex justify-between font-semibold text-lg">
                        <span>Total Amount:</span>
                        <span>{formatCurrency(+order.totalAmount)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Cash Payment Information */}
                  {Number(order.cashToPay) > 0 && (
                    <div className="mt-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                      <h3 className="font-semibold text-orange-800 mb-3 flex items-center">
                        <CreditCard className="h-4 w-4 mr-2" />
                        Cash Payment Details
                      </h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-orange-700">
                            Customer Cash:
                          </span>
                          <span className="font-medium">
                            {formatCurrency(+order.cashToPay)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-orange-700">Order Total:</span>
                          <span className="font-medium">
                            {formatCurrency(+order.totalAmount)}
                          </span>
                        </div>
                        <div className="border-t border-orange-200 pt-2">
                          <div className="flex justify-between font-semibold">
                            <span className="text-orange-800">
                              Change to Return:
                            </span>
                            <span className="text-green-700 bg-green-100 px-2 py-1 rounded">
                              {formatCurrency(
                                +order.cashToPay - +order.totalAmount
                              )}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Status Management - Right Side */}
          <div className="w-full lg:w-80 space-y-6">
            {/* Delivery Details Section */}
            <div className="bg-card p-6 rounded-lg border">
              <div className="flex items-center justify-between mb-4">
                <h2 className="flex items-center font-semibold">
                  <Truck className="h-4 w-4 mr-2" />
                  Delivery Details
                </h2>
                <Dialog
                  open={isEditingDelivery}
                  onOpenChange={setIsEditingDelivery}
                >
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-3xl">
                    <DialogHeader>
                      <DialogTitle>Update Delivery Details</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="deliveryDate">Delivery Date</Label>
                        <Input
                          id="deliveryDate"
                          type="date"
                          value={deliveryForm.deliveryDate}
                          onChange={(e) =>
                            setDeliveryForm((prev) => ({
                              ...prev,
                              deliveryDate: e.target.value,
                            }))
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="deliveryStartTime">
                          Start Time{" "}
                          <span className="text-xs text-muted-foreground">
                            (24h format)
                          </span>
                        </Label>
                        <Input
                          id="deliveryStartTime"
                          type="time"
                          value={deliveryForm.deliveryStartTime}
                          onChange={(e) =>
                            setDeliveryForm((prev) => ({
                              ...prev,
                              deliveryStartTime: e.target.value,
                            }))
                          }
                          placeholder="13:00"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Example: 13:00 for 1 PM, 09:30 for 9:30 AM
                        </p>
                      </div>
                      <div>
                        <Label htmlFor="deliveryEndTime">
                          End Time{" "}
                          <span className="text-xs text-muted-foreground">
                            (24h format)
                          </span>
                        </Label>
                        <Input
                          id="deliveryEndTime"
                          type="time"
                          value={deliveryForm.deliveryEndTime}
                          onChange={(e) =>
                            setDeliveryForm((prev) => ({
                              ...prev,
                              deliveryEndTime: e.target.value,
                            }))
                          }
                          placeholder="15:00"
                        />
                        <p className="text-xs text-muted-foreground mt-1">
                          Example: 15:00 for 3 PM, 18:30 for 6:30 PM
                        </p>
                      </div>
                      <div className="flex gap-2 pt-4">
                        <Button
                          onClick={handleUpdateDelivery}
                          className="flex-1"
                        >
                          Update Delivery
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => setIsEditingDelivery(false)}
                          className="flex-1"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Date</p>
                  <p className="font-medium">
                    {new Date(order.deliveryDate).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground mb-1">
                    Time Window
                  </p>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <p className="font-medium">
                      {order.deliveryStartTime} - {order.deliveryEndTime}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-card p-6 rounded-lg border">
              <h2 className="font-semibold mb-4">Order Status</h2>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Current Status
                  </p>
                  <Badge>{order.status}</Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Update Status
                  </p>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full">
                        Change Status
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      {OrderStatus.map((status) => (
                        <DropdownMenuItem
                          key={status}
                          onClick={() => handleUpdateStatus(status)}
                          className="cursor-pointer"
                        >
                          {status}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            <div className="bg-card p-6 rounded-lg border">
              <h2 className="flex items-center font-semibold mb-4">
                <CreditCard className="h-4 w-4 mr-2" />
                Payment Status
              </h2>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Current Status
                  </p>
                  <Badge>{order.paymentStatus}</Badge>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Update Status
                  </p>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full">
                        Change Payment Status
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      {(
                        [
                          "PENDING",
                          "PAID",
                          "FAILED",
                          "REFUNDED",
                        ] as PaymentStatus[]
                      ).map((status) => (
                        <DropdownMenuItem
                          key={status}
                          onClick={() => handleUpdatePaymentStatus(status)}
                          className="cursor-pointer"
                        >
                          {status}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            <div className="bg-card p-6 rounded-lg border">
              <h2 className="font-semibold mb-4">Delivery Man</h2>
              <div className="space-y-4">
                <ItemPicker<User>
                  title={"Select Delivery Driver"}
                  selectionMode="single"
                  columns={[
                    {
                      header: "Name",
                      render: (driver) => {
                        return (
                          <span className="h-full flex items-center">
                            {driver.name}
                          </span>
                        );
                      },
                    },
                    {
                      header: "Email",
                      render: (driver) => {
                        return (
                          <span className="h-full flex items-center">
                            {driver.email}
                          </span>
                        );
                      },
                    },
                    {
                      header: "Active",
                      render: (driver) => {
                        return (
                          <span className="h-full flex items-center">
                            {driver.deliveryDriverProfile?.isActive
                              ? "Active"
                              : "Inactive"}
                          </span>
                        );
                      },
                    },
                    {
                      header: "Location & Distance",
                      render: (driver) => {
                        const location = driver.lastLocation;
                        const distance = driver.distanceFromWarehouse;

                        if (!location) {
                          return (
                            <span className="text-sm text-muted-foreground">
                              No location data
                            </span>
                          );
                        }

                        return (
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-bold">Last Location:</span>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 text-xs"
                              onClick={() => {
                                const googleMapsUrl = `https://www.google.com/maps?q=${location.lat},${location.long}`;
                                window.open(googleMapsUrl, "_blank");
                              }}
                            >
                              <MapPin className="h-3 w-3 mr-1" />
                              View on Maps
                            </Button>
                            <div className="text-xs text-muted-foreground">
                              Updated:{" "}
                              {new Date(location.updatedAt).toLocaleString()}
                            </div>
                            {distance && (
                              <div className="mt-2 p-2 bg-blue-50 rounded">
                                <div className="font-bold text-blue-800">
                                  Distance: {distance.distance} {distance.unit}
                                </div>
                                <div className="text-xs text-blue-600">
                                  From warehouse
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      },
                    },
                    {
                      header: "Performance",
                      render: (driver) => {
                        const perf = driver.performance;
                        if (!perf) {
                          return (
                            <span className="text-sm text-muted-foreground">
                              No performance data
                            </span>
                          );
                        }
                        return (
                          <div className="space-y-1 text-sm">
                            <div>
                              <span className="font-bold">Orders:</span>{" "}
                              {perf.totalOrderCount}
                            </div>
                            <div>
                              <span className="font-bold">Pickup Avg:</span>{" "}
                              {perf.avgPickupMins.toFixed(1)} min
                            </div>
                            <div>
                              <span className="font-bold">Delivery Avg:</span>{" "}
                              {perf.avgDeliveryMins.toFixed(1)} min
                            </div>
                            <div>
                              <span className="font-bold">Late:</span>{" "}
                              {perf.lateDeliveryCount}
                            </div>
                            <div>
                              <span className="font-bold">Late Avg:</span>{" "}
                              {perf.avgLateMins} min
                            </div>
                          </div>
                        );
                      },
                    },
                  ]}
                  fetchItems={fetchDeliveryMen}
                  initialSelectedItems={
                    order.deliveryDriver
                      ? [
                          {
                            id: order.deliveryDriver.id,
                            name: order.deliveryDriver.name,
                          } as User,
                        ]
                      : []
                  }
                  onConfirmSelection={(item) => {
                    handleAssignDriver((item as User).id);
                  }}
                  renderTrigger={() => (
                    <Button variant="outline">
                      {order.deliveryDriver
                        ? "Update Delivery Man"
                        : "Select Delivery Man"}
                    </Button>
                  )}
                />

                {order.deliveryDriver ? (
                  <div className="flex items-center gap-4 p-3 rounded-md border bg-muted">
                    {order.deliveryDriver.profilePicture ? (
                      <img
                        src={order.deliveryDriver.profilePicture}
                        alt={order.deliveryDriver.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium">
                        {order.deliveryDriver.name.charAt(0)}
                      </div>
                    )}
                    <div>
                      <p className="font-medium">{order.deliveryDriver.name}</p>
                      <p className="text-muted-foreground text-sm">
                        {order.deliveryDriver.email}
                      </p>
                      <p className="text-muted-foreground text-sm">
                        {order.deliveryDriver.phoneCountry.dialCode}{" "}
                        {order.deliveryDriver.phone}
                      </p>
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground text-sm">Not Assigned</p>
                )}
              </div>
            </div>

            <div className="bg-card p-6 rounded-lg border">
              <h2 className="flex items-center font-semibold mb-4">
                <History className="h-4 w-4 mr-2" />
                Status Timeline
              </h2>
              <div className="space-y-4">
                {order.orderStatusHistory.map((history, index) => (
                  <div key={history.id} className="flex items-start gap-3">
                    <div className="relative">
                      <div className="w-2 h-2 rounded-full bg-primary mt-2" />
                      {index !== order.orderStatusHistory.length - 1 && (
                        <div className="absolute top-3 left-1 w-px h-full bg-border" />
                      )}
                    </div>
                    <div className="flex-1 pb-4">
                      <Badge>{history.status}</Badge>
                      <p className="text-xs text-muted-foreground mt-1">
                        {formatDate(history.createdAt)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
