"use client";

import { apiService } from "@/api";
import { WarehouseEditor } from "@/components/warehouses/WarehouseEditor";
import { toast } from "sonner";
import { Warehouse } from "@/frontend-types";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function EditWarehousePage({ id }: { id: string }) {
  const [warehouse, setWarehouse] = useState<Warehouse | undefined>();
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function fetchData() {
      if (!id) return;
      try {
        setIsLoading(true);
        const response = await apiService.get(`admin/warehouses/${id}`);
        if (!response.data) {
          throw new Error("Failed to fetch warehouse");
        }
        setWarehouse(response.data);
      } catch (error) {
        toast.error("Error", {
          description: (error as Error).message,
        });
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [id]);

  const onEdit = async (data: Warehouse) => {
    try {
      setIsLoading(true);

      await apiService.patch(`admin/warehouses/${id}`, data);
      toast.success("Success", {
        description: "Warehouse edited successfully",
      });
      router.push("/warehouses");
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4">
      <WarehouseEditor
        warehouse={warehouse}
        isLoading={isLoading}
        onEdit={(warehouse: Warehouse) => {
          onEdit(warehouse);
        }}
      />
    </div>
  );
}
