"use client";

import { apiService } from "@/api";
import PaginationBar from "@/components/PaginationBar";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { WarehouseTable } from "@/components/warehouses/WarehouseTable";
import { toast } from "sonner";
import { PaginatedResponse, PaginationData, Warehouse } from "@/frontend-types";
import { PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

function WarehousesPage() {
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 1,
  });

  const router = useRouter();

  useEffect(() => {
    async function fetchWarehouses() {
      try {
        const response = await apiService.get("admin/warehouses", {
          page: pagination.page,
          pageSize: pagination.pageSize,
        });

        if (!response.data) {
          throw new Error("Could not get warehouses.");
        }

        const { data: warehouses, ...paginationData } =
          response.data as PaginatedResponse<Warehouse>;

        setWarehouses(warehouses as Warehouse[]);
        setPagination(paginationData as PaginationData);
      } catch (error) {
        toast.error((error as Error).message, {
          description: `${error}`,
        });
      }
    }

    fetchWarehouses();
  }, [pagination.page, pagination.pageSize]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const handleDelete = async (id: number) => {
    try {
      await apiService.delete(`admin/warehouses/${id}`);
      toast.success("Success", {
        description: "Warehouse deleted successfully",
      });

      // Refresh the list
      const response = await apiService.get("admin/warehouses", {
        page: pagination.page,
        pageSize: pagination.pageSize,
      });

      if (response.data) {
        const { data: warehouses, ...paginationData } =
          response.data as PaginatedResponse<Warehouse>;
        setWarehouses(warehouses as Warehouse[]);
        setPagination(paginationData as PaginationData);
      }
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    }
  };

  return (
    <div className="p-4">
      <div className="flex flex-row mb-4 justify-between items-center">
        <h1 className="font-bold text-2xl">Warehouses</h1>
        <Button
          onClick={() => router.push("/warehouses/add")}
          className="flex items-center gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          Add Warehouse
        </Button>
      </div>
      <Separator className="my-4" />
      <WarehouseTable
        warehouses={warehouses}
        onClick={(coupon) => {
          console.log(coupon);
          // router.push(`/warehouses/${coupon.id}`);
        }}
        onEdit={(coupon) => {
          router.push(`/warehouses/${coupon.id}/edit`);
        }}
        onDelete={(coupon) => {
          handleDelete(coupon.id);
        }}
      />

      <div>
        <PaginationBar
          pagination={pagination}
          handlePageChange={handlePageChange}
          className="mt-12"
        />
      </div>
    </div>
  );
}

export default WarehousesPage;
