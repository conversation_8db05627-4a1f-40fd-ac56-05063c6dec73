"use client";

import { apiService } from "@/api";
import { WarehouseEditor } from "@/components/warehouses/WarehouseEditor";
import { toast } from "sonner";
import { Warehouse } from "@/frontend-types";
import { useRouter } from "next/navigation";
import { useState } from "react";

function AddWarehousePage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const onCreate = async (data: Warehouse) => {
    try {
      setIsLoading(true);

      await apiService.post("admin/warehouses", {
        name: data.name,
        lat: data.lat,
        long: data.long,
        type: data.type,
        active: data.active,
      });
      toast.success("Success", {
        description: "Warehouse created successfully",
      });
      router.push("/warehouses");
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4">
      <WarehouseEditor
        isLoading={isLoading}
        onCreate={(warehouse) => {
          onCreate(warehouse);
        }}
      />
    </div>
  );
}

export default AddWarehousePage;
