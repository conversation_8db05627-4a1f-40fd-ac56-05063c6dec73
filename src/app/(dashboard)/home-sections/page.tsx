"use client";

import { apiService } from "@/api";
import { HomeSectionTable } from "@/components/home-sections/HomeSectionTable";
import PaginationBar from "@/components/PaginationBar";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  HomeSection,
  PaginatedResponse,
  PaginationData,
  Warehouse,
} from "@/frontend-types";
import { PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function HomeSectionsPage() {
  const [homeSections, setHomeSections] = useState<HomeSection[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState<string>("0");
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 1,
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [sectionToDelete, setSectionToDelete] = useState<HomeSection | null>(
    null
  );

  const router = useRouter();

  useEffect(() => {
    async function fetchWarehouses() {
      try {
        const response = await apiService.get("admin/warehouses");
        if (response.data && response.data.data) {
          setWarehouses(response.data.data);
        }
      } catch (error) {
        toast.error("Failed to load warehouses", {
          description: (error as Error).message,
        });
      }
    }

    fetchWarehouses();
  }, []);

  useEffect(() => {
    async function fetchHomeSections() {
      try {
        const response = await apiService.get("admin/home-sections", {
          page: pagination.page,
          pageSize: pagination.pageSize,
          warehouseId: selectedWarehouse,
        });

        if (!response.data) {
          throw new Error("Could not get home sections.");
        }

        const { data: sections, ...paginationData } =
          response.data as PaginatedResponse<HomeSection>;

        setHomeSections(sections);
        setPagination(paginationData as PaginationData);
      } catch (error) {
        toast.error((error as Error).message, {
          description: `${error}`,
        });
      }
    }

    fetchHomeSections();
  }, [pagination.page, pagination.pageSize, selectedWarehouse]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const handleWarehouseChange = (value: string) => {
    setSelectedWarehouse(value);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const handleDelete = async () => {
    if (!sectionToDelete) return;

    try {
      await apiService.delete(`admin/home-sections/${sectionToDelete.id}`);
      toast.success("Success", {
        description: "Home section deleted successfully",
      });

      // Refresh the list
      const response = await apiService.get("admin/home-sections", {
        page: pagination.page,
        pageSize: pagination.pageSize,
        warehouseId: selectedWarehouse,
      });

      if (response.data) {
        const { data: sections, ...paginationData } =
          response.data as PaginatedResponse<HomeSection>;
        setHomeSections(sections);
        setPagination(paginationData as PaginationData);
      }
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setDeleteDialogOpen(false);
      setSectionToDelete(null);
    }
  };

  return (
    <div className="p-4">
      <div className="flex flex-row mb-4 justify-between items-center">
        <h1 className="font-bold text-2xl">Home Sections</h1>
        <Button
          onClick={() => router.push("/home-sections/add")}
          className="flex items-center gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          Add Home Section
        </Button>
      </div>

      <div className="mb-4">
        <Select value={selectedWarehouse} onValueChange={handleWarehouseChange}>
          <SelectTrigger className="w-[250px]">
            <SelectValue placeholder="Filter by warehouse" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="0">All Warehouses</SelectItem>
            {warehouses.map((warehouse) => (
              <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
                {warehouse.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Separator className="my-4" />

      <HomeSectionTable
        homeSections={homeSections}
        onView={(section) => router.push(`/home-sections/${section.id}`)}
        onEdit={(section) => router.push(`/home-sections/${section.id}/edit`)}
        onDelete={(section) => {
          setSectionToDelete(section);
          setDeleteDialogOpen(true);
        }}
        onTranslate={(section) =>
          router.push(`/home-sections/${section.id}/translate`)
        }
      />

      <PaginationBar
        pagination={pagination}
        handlePageChange={handlePageChange}
        className="mt-12"
      />

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              {`This will permanently delete the home section "${sectionToDelete?.title}". This action cannot be undone.`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-500">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
