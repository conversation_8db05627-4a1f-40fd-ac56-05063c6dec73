"use client";

import { apiService } from "@/api";
import { HomeSectionEditor } from "@/components/home-sections/HomeSectionEditor";
import { toast } from "sonner";
import { HomeSection, Warehouse } from "@/frontend-types";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function EditHomeSectionPage({ id }: { id: string }) {
  const [isLoading, setIsLoading] = useState(false);
  const [homeSection, setHomeSection] = useState<HomeSection | null>(null);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const router = useRouter();

  useEffect(() => {
    async function fetchData() {
      if (!id) return;

      try {
        const [homeSectionRes, warehousesRes] = await Promise.all([
          apiService.get(`admin/home-sections/${id}`),
          apiService.get("admin/warehouses"),
        ]);

        if (homeSectionRes.data) {
          setHomeSection(homeSectionRes.data);
        }

        if (warehousesRes.data && warehousesRes.data.data) {
          setWarehouses(warehousesRes.data.data);
        }
      } catch (error) {
        toast.error("Error", {
          description: (error as Error).message,
        });
      }
    }

    fetchData();
  }, [id]);

  const handleEdit = async (data: {
    title: string;
    type: "CATEGORY" | "PRODUCT";
    onlyDiscount: boolean;
    warehouseId: number | null;
    displayOrder: number;
  }) => {
    if (!id) return;

    try {
      setIsLoading(true);

      await apiService.patch(`admin/home-sections/${id}`, {
        title: data.title,
        type: data.type,
        onlyDiscount: data.onlyDiscount,
        warehouseId: data.warehouseId,
        displayOrder: data.displayOrder,
      });

      toast.success("Success", {
        description: "Home section updated successfully",
      });

      router.push("/home-sections");
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!homeSection) {
    return <div className="p-4">Loading...</div>;
  }

  return (
    <div className="p-4">
      <HomeSectionEditor
        homeSection={homeSection}
        warehouses={warehouses}
        isLoading={isLoading}
        onEdit={handleEdit}
      />
    </div>
  );
}
