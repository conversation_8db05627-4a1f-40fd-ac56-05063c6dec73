"use client";

"use client";

import { apiService } from "@/api";
import ItemPicker from "@/components/ItemPicker";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";
import { Category, HomeSection, PaginatedResponse } from "@/frontend-types";
import { ArrowLeft, Edit, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function HomeSectionDetailsPage({ id }: { id: string }) {
  const router = useRouter();
  const [homeSection, setHomeSection] = useState<HomeSection | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<string | null>(null);

  const fetchHomeSection = async () => {
    if (!id) return;

    try {
      const response = await apiService.get(`admin/home-sections/${id}`);
      if (response.data) {
        setHomeSection(response.data);
      }
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    }
  };

  useEffect(() => {
    fetchHomeSection();
  }, [id]);

  const handleAddCategory = async (selected: Category | Category[]) => {
    if (!id) return;

    // Handle single category selection
    const selectedCategory = Array.isArray(selected) ? selected[0] : selected;
    if (!selectedCategory) return;

    try {
      await apiService.post(`admin/home-sections/${id}/categories`, {
        categoryId: selectedCategory.id,
        displayOrder: homeSection?.homeSectionCategory?.length || 0,
      });

      toast.success("Success", {
        description: "Category added successfully",
      });

      // Refresh the home section data
      fetchHomeSection();
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    }
  };

  const handleDeleteCategory = async () => {
    if (!id || !categoryToDelete) return;

    try {
      await apiService.delete(
        `admin/home-sections/${id}/categories/${categoryToDelete}`
      );

      toast.success("Success", {
        description: "Category removed successfully",
      });

      // Refresh the home section data
      fetchHomeSection();
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setDeleteDialogOpen(false);
      setCategoryToDelete(null);
    }
  };

  const fetchCategories = async (
    page: number,
    pageSize: number,
    searchQuery: string
  ) => {
    try {
      const response = await apiService.get("admin/categories", {
        page,
        pageSize,
        search: searchQuery,
      });
      return response.data as PaginatedResponse<Category>;
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
      return {
        data: [],
        total: 0,
        page: 1,
        pageSize: 10,
        totalPages: 1,
      };
    }
  };

  if (!homeSection) {
    return <div className="p-4">Loading...</div>;
  }

  return (
    <div className="p-4">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/home-sections")}
          className="mr-2"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold">Home Section Details</h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">Title</p>
              <p>{homeSection.title}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Type</p>
              <p>{homeSection.type}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Warehouse</p>
              <p>{homeSection.warehouse?.name || "Global"}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Display Order</p>
              <p>{homeSection.displayOrder}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Only Discount</p>
              <p>{homeSection.onlyDiscount ? "Yes" : "No"}</p>
            </div>
          </div>
          <div className="mt-4">
            <Button
              variant="outline"
              onClick={() => router.push(`/home-sections/${id}/edit`)}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Categories</h2>
        <ItemPicker<Category>
          title="Select Category"
          selectionMode="single"
          columns={[
            { header: "ID", accessorKey: "id" },
            { header: "Name", accessorKey: "name" },
            { header: "Type", accessorKey: "type" },
          ]}
          fetchItems={fetchCategories}
          onConfirmSelection={handleAddCategory}
          renderTrigger={() => <Button>Add Category</Button>}
        />
      </div>

      <Separator className="mb-4" />

      {homeSection.homeSectionCategory &&
      homeSection.homeSectionCategory.length > 0 ? (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Category Name</TableHead>
              <TableHead>Display Order</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {homeSection.homeSectionCategory.map((item) => (
              <TableRow key={item.id}>
                <TableCell>{item.categoryId}</TableCell>
                <TableCell>{item.category?.name || "Unknown"}</TableCell>
                <TableCell>{item.displayOrder}</TableCell>
                <TableCell>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      setCategoryToDelete(item.id);
                      setDeleteDialogOpen(true);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          {
            'No categories added yet. Use the "Add Category" button to add categories.'
          }
        </div>
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will remove the category from this home section.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteCategory}
              className="bg-red-500"
            >
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
