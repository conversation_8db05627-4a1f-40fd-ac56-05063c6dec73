"use client";

import { apiService } from "@/api";
import HomeSectionTranslationDialog from "@/components/home-sections/HomeSectionTranslationDialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";
import { HomeSection, HomeSectionTranslation } from "@/frontend-types";
import { ArrowLeft, Pencil, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function HomeSectionTranslationPage({ id }: { id: string }) {
  const router = useRouter();
  const [homeSection, setHomeSection] = useState<HomeSection | null>(null);
  const [translations, setTranslations] = useState<HomeSectionTranslation[]>(
    []
  );
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selected, setSelected] = useState<HomeSectionTranslation | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [translationToDelete, setTranslationToDelete] = useState<number | null>(
    null
  );

  const fetchData = async () => {
    if (!id) return;

    try {
      const res = await apiService.get(`admin/home-sections/${id}`);
      setHomeSection(res.data);

      if (res.data.translations) {
        setTranslations(res.data.translations);
      }
    } catch (error) {
      toast.error("Failed to load", {
        description: (error as Error).message,
      });
    }
  };

  useEffect(() => {
    fetchData();
  }, [id]);

  const handleSave = async (data: { languageId: number; title: string }) => {
    if (!id) return;

    try {
      await apiService.post(`admin/home-sections/${id}/translation`, data);
      toast.success("Translation saved successfully");
      setDialogOpen(false);
      setSelected(null);
      fetchData();
    } catch (error) {
      toast.error("Error saving translation", {
        description: (error as Error).message,
      });
    }
  };

  const handleDelete = async () => {
    if (!id || translationToDelete === null) return;

    try {
      await apiService.delete(
        `admin/home-sections/${id}/translation?languageId=${translationToDelete}`
      );
      toast.success("Translation deleted successfully");
      fetchData();
    } catch (error) {
      toast.error("Error deleting translation", {
        description: (error as Error).message,
      });
    } finally {
      setDeleteDialogOpen(false);
      setTranslationToDelete(null);
    }
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex flex-row items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/home-sections")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">
            Manage Home Section Translations
          </h1>
        </div>
        <Button
          onClick={() => {
            setSelected(null);
            setDialogOpen(true);
          }}
        >
          Add Translation
        </Button>
      </div>

      {homeSection && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold">{homeSection.title}</h2>
          <p className="text-sm text-muted-foreground">
            Type: {homeSection.type}
          </p>
        </div>
      )}

      <Separator className="mb-4" />

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">Language</TableHead>
            <TableHead>Translated Title</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {translations.map((t) => (
            <TableRow key={t.languageId}>
              <TableCell className="flex items-center gap-2">
                {/* <Image
                  src={t.language.flagUrl}
                  alt={t.language.code}
                  width={20}
                  height={20}
                  className="rounded-sm"
                /> */}
                {t.language.name} ({t.language.code})
              </TableCell>
              <TableCell>{t.title}</TableCell>
              <TableCell className="text-right space-x-2">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => {
                    setSelected(t);
                    setDialogOpen(true);
                  }}
                >
                  <Pencil className="w-4 h-4 mr-1" />
                  Edit
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => {
                    setTranslationToDelete(t.languageId);
                    setDeleteDialogOpen(true);
                  }}
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  Delete
                </Button>
              </TableCell>
            </TableRow>
          ))}
          {translations.length === 0 && (
            <TableRow>
              <TableCell colSpan={3} className="text-center py-4">
                No translations found. Add a translation to get started.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <HomeSectionTranslationDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onSubmit={handleSave}
        initialData={selected}
      />

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this translation.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-500">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
