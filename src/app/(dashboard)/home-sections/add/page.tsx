"use client";

import { apiService } from "@/api";
import { HomeSectionEditor } from "@/components/home-sections/HomeSectionEditor";
import { toast } from "sonner";
import { Warehouse } from "@/frontend-types";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function AddHomeSectionPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const router = useRouter();

  useEffect(() => {
    async function fetchWarehouses() {
      try {
        const response = await apiService.get("admin/warehouses");
        if (response.data && response.data.data) {
          setWarehouses(response.data.data);
        }
      } catch (error) {
        toast.error("Failed to load warehouses", {
          description: (error as Error).message,
        });
      }
    }

    fetchWarehouses();
  }, []);

  const handleCreate = async (data: {
    title: string;
    type: "CATEGORY" | "PRODUCT";
    onlyDiscount: boolean;
    warehouseId: number | null;
    displayOrder: number;
  }) => {
    try {
      setIsLoading(true);

      // Create the home section
      const response = await apiService.post("admin/home-sections", {
        title: data.title,
        type: data.type,
        onlyDiscount: data.onlyDiscount,
        warehouseId: data.warehouseId,
        displayOrder: data.displayOrder,
        categories: [], // Initially empty, categories will be added in the view page
      });

      toast.success("Success", {
        description: "Home section created successfully",
      });

      // Navigate to the view page to add categories
      router.push(`/home-sections/${response.data.id}`);
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4">
      <HomeSectionEditor
        warehouses={warehouses}
        isLoading={isLoading}
        onCreate={handleCreate}
      />
    </div>
  );
}
