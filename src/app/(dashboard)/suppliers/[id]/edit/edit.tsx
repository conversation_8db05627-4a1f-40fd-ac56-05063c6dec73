"use client";

import { apiService } from "@/api";
import { SupplierEditor } from "@/components/suppliers/SupplierEditor";
import { toast } from "sonner";
import { Country, Supplier } from "@/frontend-types";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function EditSupplier({ id }: { id: string }) {
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [countries, setCountries] = useState<Country[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    async function fetchData() {
      try {
        const [supplierRes, countriesRes] = await Promise.all([
          apiService.get(`admin/suppliers/${id}`),
          apiService.get("admin/countries"),
        ]);
        setSupplier(supplierRes.data);
        setCountries(countriesRes.data);
      } catch (error) {
        console.log(error);
        toast.error("Error", {
          description: "Failed to load data",
        });
      }
    }
    if (id) fetchData();
  }, [id]);

  const handleEdit = async (supplier: Supplier) => {
    try {
      setIsLoading(true);
      await apiService.patch(`admin/suppliers/${id}`, {
        name: supplier.name,
        email: supplier.email,
        phone: supplier.phone,
        address: supplier.address,
        countryId: supplier.countryId,
      });
      toast.success("Success", {
        description: "Supplier updated successfully",
      });
      router.push(`/suppliers`);
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!supplier) return null;

  return (
    <div className="p-4">
      <SupplierEditor
        supplier={supplier}
        countries={countries}
        isLoading={isLoading}
        onEdit={handleEdit}
      />
    </div>
  );
}
