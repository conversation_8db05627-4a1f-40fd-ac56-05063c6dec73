"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { apiService } from "@/api";
import { PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Supplier } from "@/frontend-types";

function SuppliersPage() {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const router = useRouter();

  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        const response = await apiService.get("admin/suppliers");
        setSuppliers(response.data as Supplier[]);
      } catch (error) {
        toast.error("Error", {
          description: (error as Error).message,
        });
      }
    };

    fetchSuppliers();
  }, []);

  return (
    <div className="p-4">
      <div className="flex flex-row mb-4 justify-between items-center">
        <h1 className="font-bold text-2xl">Suppliers</h1>
        <Button
          onClick={() => router.push("/suppliers/add")}
          className="flex items-center gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          New Supplier
        </Button>
      </div>
      <Separator className="my-4" />

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Phone</TableHead>
            <TableHead>Address</TableHead>
            <TableHead>Country</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {suppliers.map((supplier) => (
            <TableRow
              key={supplier.id}
              className="cursor-pointer hover:bg-muted"
              onClick={() => router.push(`/suppliers/${supplier.id}/edit`)}
            >
              <TableCell>{supplier.name}</TableCell>
              <TableCell>{supplier.email}</TableCell>
              <TableCell>{supplier.phone}</TableCell>
              <TableCell>{supplier.address}</TableCell>
              <TableCell>{supplier.country.name}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

export default SuppliersPage;
