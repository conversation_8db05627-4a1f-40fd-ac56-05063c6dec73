"use client";

import { apiService } from "@/api";
import { SupplierEditor } from "@/components/suppliers/SupplierEditor";
import { toast } from "sonner";
import { Country, Supplier } from "@/frontend-types";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

function AddSupplierPage() {
  const [countries, setCountries] = useState<Country[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();

  useEffect(() => {
    async function fetchCountries() {
      try {
        const response = await apiService.get("admin/countries");
        setCountries(response.data);
      } catch (error) {
        console.log(error);
        toast.error("Error", {
          description: "Failed to fetch countries",
        });
      }
    }
    fetchCountries();
  }, []);

  const handleCreate = async (supplier: Supplier) => {
    try {
      setIsLoading(true);
      await apiService.post("admin/suppliers", {
        name: supplier.name,
        email: supplier.email,
        phone: supplier.phone,
        address: supplier.address,
        countryId: supplier.countryId,
      });
      toast.success("Success", {
        description: "Supplier created successfully",
      });
      router.push("/suppliers");
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4">
      <SupplierEditor
        isLoading={isLoading}
        countries={countries}
        onCreate={handleCreate}
      />
    </div>
  );
}

export default AddSupplierPage;
