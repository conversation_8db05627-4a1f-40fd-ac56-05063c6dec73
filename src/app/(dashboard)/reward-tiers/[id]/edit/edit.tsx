"use client";

import { apiService } from "@/api";
import {
  RewardTierEditor,
  RewardTierFormValues,
} from "@/components/reward-tiers/RewardTierEditor";
import { toast } from "sonner";
import { RewardTier } from "@/frontend-types";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function EditRewardTier({ id }: { id: string }) {
  const [tier, setTier] = useState<RewardTier | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    async function fetchData() {
      try {
        const rewardTierRes = await apiService.get(`admin/reward-tiers/${id}`);
        setTier(rewardTierRes.data);
      } catch (error) {
        console.log(error);
        toast.error("Error", {
          description: "Failed to load data",
        });
      }
    }
    if (id) fetchData();
  }, [id]);

  const handleEdit = async (rewardTier: RewardTierFormValues) => {
    try {
      setIsLoading(true);
      await apiService.patch(`admin/reward-tiers/${id}`, {
        name: rewardTier.name,
        requiredSpending: rewardTier.requiredSpending,
        earnPercentage: rewardTier.earnPercentage,
        redeemPercentage: rewardTier.redeemPercentage,
        maxDiscountValue: rewardTier.maxDiscountValue,
        minOrderAmountForBonus: rewardTier.minOrderAmountForBonus,
      });
      toast.success("Success", {
        description: "Reward tier updated successfully",
      });
      router.push(`/reward-tiers`);
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!tier) return null;

  return (
    <div className="p-4">
      <RewardTierEditor
        tier={tier}
        isLoading={isLoading}
        onSubmit={handleEdit}
      />
    </div>
  );
}
