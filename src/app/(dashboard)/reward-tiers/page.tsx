"use client";

import { apiService } from "@/api";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";
import { RewardTier } from "@/frontend-types";
import { MoreHorizontal } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function RewardTiersPage() {
  const [tiers, setTiers] = useState<RewardTier[]>([]);
  const router = useRouter();

  useEffect(() => {
    apiService
      .get("admin/reward-tiers")
      .then((res) => setTiers(res.data))
      .catch((error) => {
        toast.error("Error loading tiers", {
          description: error.message,
        });
      });
  }, []);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Reward Tiers</h1>
        {/* <Button onClick={() => router.push("/reward-tiers/add")}>
          <PlusIcon />
          Add Tier
        </Button> */}
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Required spending</TableHead>
            <TableHead>Earn %</TableHead>
            <TableHead>Redeem %</TableHead>
            <TableHead>Max Discount</TableHead>
            <TableHead>Min order value</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tiers.map((tier) => (
            <TableRow key={tier.id}>
              <TableCell>{tier.name}</TableCell>
              <TableCell>{tier.requiredRollingSpend}</TableCell>
              <TableCell>{tier.earnPercentage}%</TableCell>
              <TableCell>{tier.redeemPercentage}%</TableCell>
              <TableCell>Rs. {tier.maxDiscountValue}</TableCell>
              <TableCell>Rs. {tier.minOrderAmountForBonus ?? 0}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/reward-tiers/${tier.id}/edit`);
                      }}
                    >
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/reward-tiers/${tier.id}/discounts`);
                      }}
                    >
                      Product Discounts
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
