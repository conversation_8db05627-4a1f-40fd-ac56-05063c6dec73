"use client";

import { apiService } from "@/api";
import { CategoryEditor } from "@/components/categories/CategoryEditor";
import { toast } from "sonner";
import { Category, ErrorResponse } from "@/frontend-types";
import { AxiosError } from "axios";
import { useRouter } from "next/navigation";
import { useState } from "react";

function AddCategoryPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const onCreate = async (
    data: Category & { files?: { banner?: File; icon?: File } }
  ) => {
    try {
      setIsLoading(true);

      if (data.files) {
        // Upload banner image if present
        if (data.files.banner) {
          try {
            console.log("Uploading banner file:", data.files.banner.name);
            const formData = new FormData();
            formData.append("file", data.files.banner);

            const response = await apiService.post(
              "admin/media/upload",
              formData,
              { ContentType: "multipart/form-data" }
            );

            console.log("Banner upload response:", response.data);
            if (response.data?.url) {
              data.bannerUrl = response.data.url;
              console.log("Banner URL set to:", data.bannerUrl);
            }
          } catch (error) {
            console.error("Error uploading banner:", error);
            toast.error("Banner Upload Failed", {
              description: (error as Error).message,
            });
          }
        }

        // Upload icon image if present
        if (data.files.icon) {
          try {
            console.log("Uploading icon file:", data.files.icon.name);
            const formData = new FormData();
            formData.append("file", data.files.icon);

            const response = await apiService.post(
              "admin/media/upload",
              formData,
              { ContentType: "multipart/form-data" }
            );

            console.log("Icon upload response:", response.data);
            if (response.data?.url) {
              data.iconUrl = response.data.url;
              console.log("Icon URL set to:", data.iconUrl);
            }
          } catch (error) {
            console.error("Error uploading icon:", error);
            toast("Icon Upload Failed", {
              description: (error as Error).message,
            });
          }
        }

        // Remove files property before sending to API
        delete data.files;
      }

      await apiService.post("admin/categories", data);
      toast.success("Success", {
        description: "Category created successfully",
      });
      router.push("/categories");
    } catch (error) {
      toast.error("Error", {
        description: ((error as AxiosError).response?.data as ErrorResponse)
          .message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4">
      <CategoryEditor
        isLoading={isLoading}
        onEdit={(
          category: Category & { files?: { banner?: File; icon?: File } }
        ) => {
          console.log(category);
          onCreate(category);
        }}
        onCreate={(
          category: Category & { files?: { banner?: File; icon?: File } }
        ) => {
          console.log(category);
          onCreate(category);
        }}
      />
    </div>
  );
}

export default AddCategoryPage;
