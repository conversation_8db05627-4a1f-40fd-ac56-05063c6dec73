"use client";

import { apiService } from "@/api";
import TranslationDialog from "@/components/categories/CategoryTranslationDialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Category, CategoryTranslation } from "@/frontend-types";
import { ArrowLeft, Pencil } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function CategoryTranslationPage({ id }: { id: string }) {
  const router = useRouter();
  const [category, setCategory] = useState<Category | null>(null);
  const [translations, setTranslations] = useState<CategoryTranslation[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selected, setSelected] = useState<CategoryTranslation | null>(null);

  const fetchData = async () => {
    try {
      const res = await apiService.get(`admin/categories/${id}`);
      setCategory(res.data);

      const translationsRes = await apiService.get(
        `admin/categories/${id}/translation`
      );
      setTranslations(translationsRes.data || []);
    } catch (error) {
      toast.error("Failed to load", {
        description: (error as Error).message,
      });
    }
  };

  useEffect(() => {
    if (id) fetchData();
  }, [id]);

  const handleSubmit = async (data: { languageId: number; name: string }) => {
    try {
      await apiService.patch(`admin/categories/${id}/translation`, data);
      toast.success("Saved");
      setDialogOpen(false);
      setSelected(null);
      fetchData();
    } catch (error) {
      toast.error("Error saving", {
        description: (error as Error).message,
      });
    }
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex flex-row items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/categories")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Manage Category Translations</h1>
        </div>
        <Button
          onClick={() => {
            setSelected(null);
            setDialogOpen(true);
          }}
        >
          Add Translation
        </Button>
      </div>

      {category && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold">{category.name}</h2>
          <p className="text-sm text-muted-foreground">Slug: {category.slug}</p>
        </div>
      )}

      <Separator className="mb-4" />

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">Language</TableHead>
            <TableHead>Translated Name</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {translations.map((t) => (
            <TableRow key={t.languageId}>
              <TableCell className="flex items-center gap-2">
                {t.language.flagUrl && (
                  <img
                    src={t.language.flagUrl}
                    alt={t.language.code}
                    className="w-5 h-5 rounded-sm"
                  />
                )}
                {t.language.name} ({t.language.code})
              </TableCell>
              <TableCell>{t.name}</TableCell>
              <TableCell className="text-right">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => {
                    setSelected(t);
                    setDialogOpen(true);
                  }}
                >
                  <Pencil className="w-4 h-4 mr-1" />
                  Edit
                </Button>
              </TableCell>
            </TableRow>
          ))}
          {translations.length === 0 && (
            <TableRow>
              <TableCell
                colSpan={3}
                className="text-center py-6 text-muted-foreground"
              >
                No translations yet.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <TranslationDialog
        open={dialogOpen}
        initialData={selected}
        onClose={() => {
          setDialogOpen(false);
          setSelected(null);
        }}
        onSubmit={handleSubmit}
      />
    </div>
  );
}
