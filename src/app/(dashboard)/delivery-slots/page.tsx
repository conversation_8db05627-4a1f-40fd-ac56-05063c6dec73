"use client";

import { apiService } from "@/api";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { DeliverySlot, Weekday } from "@/frontend-types";
import { format, isSameDay } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { AlertTriangle, Plus, Trash2 } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

// Get the local timezone
const LOCAL_TIMEZONE = Intl.DateTimeFormat().resolvedOptions().timeZone;

export default function DeliverySlotAdminPage() {
  // State for different slot types
  const [defaultSlots, setDefaultSlots] = useState<DeliverySlot[]>([]);
  const [weekdaySlots, setWeekdaySlots] = useState<DeliverySlot[]>([]);
  const [dateSlots, setDateSlots] = useState<DeliverySlot[]>([]);
  const [holidays, setHolidays] = useState<DeliverySlot[]>([]);
  const [allSlots, setAllSlots] = useState<DeliverySlot[]>([]);

  // UI state
  const [activeTab, setActiveTab] = useState("default");
  const [selectedWeekday, setSelectedWeekday] = useState<Weekday>("MONDAY");
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [isSelectedDateHoliday, setIsSelectedDateHoliday] = useState(false);
  const [holidayReason, setHolidayReason] = useState("");

  // Dialog state
  const [slotDialogOpen, setSlotDialogOpen] = useState(false);
  const [holidayDialogOpen, setHolidayDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [slotToDelete, setSlotToDelete] = useState<DeliverySlot | null>(null);

  // New slot state
  const [newSlot, setNewSlot] = useState({
    startTime: "09:00",
    endTime: "11:00",
    type: "default" as "default" | "weekday" | "date",
  });

  // New holiday state
  const [newHoliday, setNewHoliday] = useState({
    date: new Date(),
    reason: "",
  });

  const weekdayNames = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  // Load all slots
  const loadAllData = useCallback(async () => {
    try {
      const res = await apiService.get("admin/delivery-slots/all");
      const slots = res.data as DeliverySlot[];

      // Process slots with proper timezone handling
      const processedSlots = slots.map((slot) => ({
        ...slot,
        date: slot.date
          ? toZonedTime(new Date(slot.date), LOCAL_TIMEZONE)
          : null,
      }));

      setAllSlots(processedSlots);

      // Filter slots by type
      setDefaultSlots(
        processedSlots.filter(
          (slot) => slot.type === "REGULAR" && !slot.date && !slot.weekday
        )
      );

      setHolidays(processedSlots.filter((slot) => slot.type === "HOLIDAY"));

      // Update weekday and date slots based on selection
      updateWeekdaySlots(processedSlots, selectedWeekday);
      updateDateSlots(processedSlots, selectedDate);
      checkIfHoliday(processedSlots, selectedDate);
    } catch (error) {
      toast.error("Failed to load delivery slots", {
        description:
          "There was an error loading the delivery slots. Please try again.",
      });
    }
  }, [selectedWeekday, selectedDate]);

  // Update weekday slots when weekday changes
  const updateWeekdaySlots = (slots: DeliverySlot[], weekday: Weekday) => {
    const filtered = slots.filter(
      (slot) => slot.type === "REGULAR" && slot.weekday === weekday
    );
    setWeekdaySlots(filtered);
  };

  // Update date slots when date changes
  const updateDateSlots = (slots: DeliverySlot[], date: Date) => {
    const filtered = slots.filter(
      (slot) =>
        slot.type === "REGULAR" && slot.date && isSameDay(slot.date, date)
    );
    setDateSlots(filtered);
  };

  // Check if selected date is a holiday
  const checkIfHoliday = (slots: DeliverySlot[], date: Date) => {
    const holiday = slots.find(
      (slot) =>
        slot.type === "HOLIDAY" && slot.date && isSameDay(slot.date, date)
    );

    setIsSelectedDateHoliday(!!holiday);
    setHolidayReason(holiday?.reason || "");
  };

  // Handle weekday change
  const handleWeekdayChange = (weekday: Weekday) => {
    setSelectedWeekday(weekday);
    updateWeekdaySlots(allSlots, weekday);
  };

  // Handle date change
  const handleDateChange = (date: Date | undefined) => {
    if (!date) return;

    setSelectedDate(date);
    updateDateSlots(allSlots, date);
    checkIfHoliday(allSlots, date);
  };

  // Create a new slot
  const createSlot = async () => {
    try {
      const data: any = {
        startTime: newSlot.startTime,
        endTime: newSlot.endTime,
      };

      // Add the appropriate date or weekday based on slot type
      if (newSlot.type === "weekday") {
        data.weekday = selectedWeekday;
      } else if (newSlot.type === "date") {
        // Convert local date to UTC for the API
        data.date = format(selectedDate, "yyyy-MM-dd");
      }

      await apiService.post("admin/delivery-slots/slot", data);

      toast.success("Slot created", {
        description: "The delivery slot has been created successfully.",
      });

      setSlotDialogOpen(false);
      loadAllData();
    } catch (error) {
      console.error(error);
      toast.error("Failed to create slot", {
        description:
          "There was an error creating the slot. Please check your inputs and try again.",
      });
    }
  };

  // Create a new holiday
  const createHoliday = async () => {
    try {
      // Convert local date to UTC for the API
      const data = {
        date: format(selectedDate, "yyyy-MM-dd"),
        reason: newHoliday.reason,
      };

      await apiService.post("admin/delivery-slots/holiday", data);

      toast.success("Holiday created", {
        description: "The holiday has been marked successfully.",
      });

      setHolidayDialogOpen(false);
      loadAllData();
    } catch (error) {
      toast.error("Failed to create holiday", {
        description:
          "There was an error marking the holiday. Please try again.",
      });
    }
  };

  // Delete a slot
  const deleteSlot = async () => {
    if (!slotToDelete) return;

    try {
      await apiService.delete(`admin/delivery-slots/${slotToDelete.id}`);

      toast.success("Slot deleted", {
        description: "The delivery slot has been deleted successfully.",
      });

      setDeleteDialogOpen(false);
      setSlotToDelete(null);
      loadAllData();
    } catch (error) {
      toast.error("Failed to delete slot", {
        description: "There was an error deleting the slot. Please try again.",
      });
    }
  };

  // Format time for display
  const formatTime = (time: string | null) => {
    if (!time) return "";
    return time;
  };

  // Load data on component mount
  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  return (
    <div className="p-4 max-w-7xl ">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Delivery Slots Management</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
        <TabsList className="grid grid-cols-4 mb-6">
          <TabsTrigger value="default">Default Slots</TabsTrigger>
          <TabsTrigger value="weekday">Weekday Slots</TabsTrigger>
          <TabsTrigger value="date">Date-Specific Slots</TabsTrigger>
          <TabsTrigger value="holidays">Holidays</TabsTrigger>
        </TabsList>

        {/* Default Slots Tab */}
        <TabsContent value="default">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Default Delivery Slots</CardTitle>
              <Button
                onClick={() => {
                  setNewSlot({
                    startTime: "09:00",
                    endTime: "11:00",
                    type: "default",
                  });
                  setSlotDialogOpen(true);
                }}
              >
                <Plus className="mr-2 h-4 w-4" /> Add Default Slot
              </Button>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Default slots are used when no specific weekday or date slots
                are defined.
              </p>

              {defaultSlots.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Start Time</TableHead>
                      <TableHead>End Time</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {defaultSlots.map((slot) => (
                      <TableRow key={slot.id}>
                        <TableCell>{formatTime(slot.startTime)}</TableCell>
                        <TableCell>{formatTime(slot.endTime)}</TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => {
                              setSlotToDelete(slot);
                              setDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg">
                  <p className="text-muted-foreground mb-4">
                    No default slots defined
                  </p>
                  <Button
                    onClick={() => {
                      setNewSlot({
                        startTime: "09:00",
                        endTime: "11:00",
                        type: "default",
                      });
                      setSlotDialogOpen(true);
                    }}
                  >
                    <Plus className="mr-2 h-4 w-4" /> Add Default Slot
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Weekday Slots Tab */}
        <TabsContent value="weekday">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center gap-4">
                <CardTitle>Weekday Delivery Slots</CardTitle>
                <Select
                  value={selectedWeekday}
                  onValueChange={(value) =>
                    handleWeekdayChange(value as Weekday)
                  }
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select Weekday" />
                  </SelectTrigger>
                  <SelectContent>
                    {[
                      "SUNDAY",
                      "MONDAY",
                      "TUESDAY",
                      "WEDNESDAY",
                      "THURSDAY",
                      "FRIDAY",
                      "SATURDAY",
                    ].map((w) => (
                      <SelectItem key={w} value={w}>
                        {w}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button
                onClick={() => {
                  setNewSlot({
                    startTime: "09:00",
                    endTime: "11:00",
                    type: "weekday",
                  });
                  setSlotDialogOpen(true);
                }}
              >
                <Plus className="mr-2 h-4 w-4" /> Add Weekday Slot
              </Button>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Weekday slots are used for specific days of the week and
                override default slots.
              </p>

              {weekdaySlots.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Start Time</TableHead>
                      <TableHead>End Time</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {weekdaySlots.map((slot) => (
                      <TableRow key={slot.id}>
                        <TableCell>{formatTime(slot.startTime)}</TableCell>
                        <TableCell>{formatTime(slot.endTime)}</TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => {
                              setSlotToDelete(slot);
                              setDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg">
                  <p className="text-muted-foreground mb-4">
                    No slots defined for {selectedWeekday}
                  </p>
                  <Button
                    onClick={() => {
                      setNewSlot({
                        startTime: "09:00",
                        endTime: "11:00",
                        type: "weekday",
                      });
                      setSlotDialogOpen(true);
                    }}
                  >
                    <Plus className="mr-2 h-4 w-4" /> Add Weekday Slot
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Date-Specific Slots Tab */}
        <TabsContent value="date">
          <Card>
            <CardHeader>
              <CardTitle>Date-Specific Delivery Slots</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm text-muted-foreground mb-4">
                    Select a date to manage its specific delivery slots.
                  </p>
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={(date) => date && handleDateChange(date)}
                    className="border rounded-md"
                    initialFocus
                  />

                  {isSelectedDateHoliday && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
                      <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
                      <div>
                        <p className="text-red-600 font-medium">
                          This date is marked as a holiday
                        </p>
                        {holidayReason && (
                          <p className="text-sm text-red-500">
                            Reason: {holidayReason}
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium">
                      Slots for {format(selectedDate, "EEEE, MMMM d, yyyy")}
                    </h3>

                    <div className="flex gap-2">
                      {!isSelectedDateHoliday ? (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setHolidayDialogOpen(true)}
                          >
                            Mark as Holiday
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => {
                              setNewSlot({
                                startTime: "09:00",
                                endTime: "11:00",
                                type: "date",
                              });
                              setSlotDialogOpen(true);
                            }}
                          >
                            <Plus className="mr-2 h-4 w-4" /> Add Slot
                          </Button>
                        </>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={async () => {
                            const holiday = allSlots.find(
                              (s) =>
                                s.type === "HOLIDAY" &&
                                s.date &&
                                isSameDay(s.date, selectedDate)
                            );
                            if (holiday) {
                              await apiService.delete(
                                `admin/delivery-slots/${holiday.id}`
                              );
                              loadAllData();
                            }
                          }}
                        >
                          Remove Holiday Status
                        </Button>
                      )}
                    </div>
                  </div>

                  {!isSelectedDateHoliday && (
                    <>
                      {dateSlots.length > 0 ? (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Start Time</TableHead>
                              <TableHead>End Time</TableHead>
                              <TableHead className="text-right">
                                Actions
                              </TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {dateSlots.map((slot) => (
                              <TableRow key={slot.id}>
                                <TableCell>
                                  {formatTime(slot.startTime)}
                                </TableCell>
                                <TableCell>
                                  {formatTime(slot.endTime)}
                                </TableCell>
                                <TableCell className="text-right">
                                  <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={() => {
                                      setSlotToDelete(slot);
                                      setDeleteDialogOpen(true);
                                    }}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      ) : (
                        <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg">
                          <p className="text-muted-foreground mb-4">
                            No specific slots for this date
                          </p>
                          <p className="text-sm text-muted-foreground mb-4">
                            {weekdayNames[selectedDate.getDay()]} slots or
                            default slots will be used instead.
                          </p>
                          <Button
                            onClick={() => {
                              setNewSlot({
                                startTime: "09:00",
                                endTime: "11:00",
                                type: "date",
                              });
                              setSlotDialogOpen(true);
                            }}
                          >
                            <Plus className="mr-2 h-4 w-4" /> Add Date-Specific
                            Slot
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Holidays Tab */}
        <TabsContent value="holidays">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Holidays</CardTitle>
              <Button onClick={() => setHolidayDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" /> Add Holiday
              </Button>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Holidays are days when no delivery slots are available.
              </p>

              {holidays.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Reason</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {holidays.map((holiday) => (
                      <TableRow key={holiday.id}>
                        <TableCell>
                          {holiday.date
                            ? format(holiday.date, "EEEE, MMMM d, yyyy")
                            : ""}
                        </TableCell>
                        <TableCell>
                          {holiday.reason || "No reason provided"}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => {
                              setSlotToDelete(holiday);
                              setDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg">
                  <p className="text-muted-foreground mb-4">
                    No holidays defined
                  </p>
                  <Button onClick={() => setHolidayDialogOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" /> Add Holiday
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Slot Dialog */}
      <Dialog open={slotDialogOpen} onOpenChange={setSlotDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>
              {newSlot.type === "default"
                ? "Add Default Slot"
                : newSlot.type === "weekday"
                ? `Add ${selectedWeekday} Slot`
                : `Add Slot for ${format(selectedDate, "MMM d, yyyy")}`}
            </DialogTitle>
            <DialogDescription>
              Specify the time range for the delivery slot
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col space-y-2">
                <Label htmlFor="startTime">Start Time (HH:MM)</Label>
                <Input
                  id="startTime"
                  placeholder="09:00"
                  value={newSlot.startTime}
                  onChange={(e) =>
                    setNewSlot({ ...newSlot, startTime: e.target.value })
                  }
                  pattern="[0-9]{2}:[0-9]{2}"
                />
                <p className="text-xs text-muted-foreground">
                  Format: 24-hour (e.g., 14:30)
                </p>
              </div>

              <div className="flex flex-col space-y-2">
                <Label htmlFor="endTime">End Time (HH:MM)</Label>
                <Input
                  id="endTime"
                  placeholder="11:00"
                  value={newSlot.endTime}
                  onChange={(e) =>
                    setNewSlot({ ...newSlot, endTime: e.target.value })
                  }
                  pattern="[0-9]{2}:[0-9]{2}"
                />
                <p className="text-xs text-muted-foreground">
                  Format: 24-hour (e.g., 16:30)
                </p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setSlotDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={createSlot}>Save Slot</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Holiday Dialog */}
      <Dialog open={holidayDialogOpen} onOpenChange={setHolidayDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Mark as Holiday</DialogTitle>
            <DialogDescription>
              Mark {format(selectedDate, "MMMM d, yyyy")} as a holiday with no
              delivery slots
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="flex flex-col space-y-2">
              <Label htmlFor="holidayReason">Reason (Optional)</Label>
              <Input
                id="holidayReason"
                placeholder="e.g., National Holiday, Eid, etc."
                value={newHoliday.reason}
                onChange={(e) =>
                  setNewHoliday({ ...newHoliday, reason: e.target.value })
                }
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setHolidayDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button onClick={createHoliday}>Mark as Holiday</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              {slotToDelete?.type === "HOLIDAY"
                ? "This will remove the holiday and allow delivery slots for this date."
                : "This will delete the delivery slot permanently."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={deleteSlot} className="bg-red-600">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
