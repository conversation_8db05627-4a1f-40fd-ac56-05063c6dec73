"use client";

import { apiService } from "@/api";
import { CouponTable } from "@/components/coupons/CouponTable";
import PaginationBar from "@/components/PaginationBar";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { Coupon, PaginatedResponse, PaginationData } from "@/frontend-types";
import { PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function CouponsPage() {
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 1,
  });

  const router = useRouter();

  useEffect(() => {
    async function fetchCoupons() {
      try {
        const response = await apiService.get("admin/coupons", {
          page: pagination.page,
          pageSize: pagination.pageSize,
        });

        if (!response.data) {
          throw new Error("Could not get coupons.");
        }

        const { data: coupons, ...paginationData } =
          response.data as PaginatedResponse<Coupon>;

        setCoupons(coupons);
        setPagination(paginationData as PaginationData);
      } catch (error) {
        toast.error((error as Error).message, {
          description: `${error}`,
        });
      }
    }

    fetchCoupons();
  }, [pagination.page, pagination.pageSize]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  return (
    <div className="p-4">
      <div className="flex flex-row mb-4 justify-between items-center">
        <h1 className="font-bold text-2xl">Coupons</h1>
        <Button
          onClick={() => router.push("/coupons/add")}
          className="flex items-center gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          Create Coupon
        </Button>
      </div>
      <Separator className="my-4" />
      <CouponTable
        coupons={coupons}
        onClick={(coupon) => {
          console.log(coupon);
          // router.push(`/coupons/${coupon.id}`);
        }}
        onEdit={(coupon) => {
          router.push(`/coupons/${coupon.id}/edit`);
        }}
        onDelete={(coupon) => {
          console.log(coupon);
        }}
      />

      <PaginationBar
        pagination={pagination}
        handlePageChange={handlePageChange}
        className="mt-12"
      />
    </div>
  );
}
