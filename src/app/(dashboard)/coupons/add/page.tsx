"use client";

import { apiService } from "@/api";
import { CouponEditor } from "@/components/coupons/CouponEditor";
import { toast } from "sonner";
import { Coupon } from "@/frontend-types";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function AddCouponPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const onCreate = async (data: Coupon) => {
    try {
      setIsLoading(true);
      await apiService.post("admin/coupons", data);
      toast.success("Success", {
        description: "Coupon created successfully",
      });
      router.push("/coupons");
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4">
      <CouponEditor
        isLoading={isLoading}
        onCreate={(coupon) => {
          onCreate(coupon);
        }}
      />
    </div>
  );
}
