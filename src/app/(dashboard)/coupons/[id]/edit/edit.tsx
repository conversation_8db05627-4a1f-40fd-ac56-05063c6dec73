"use client";

import { apiService } from "@/api";
import { CouponEditor } from "@/components/coupons/CouponEditor";
import { toast } from "sonner";
import { Coupon } from "@/frontend-types";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function EditCoupon({ id }: { id: string }) {
  const [coupon, setCoupon] = useState<Coupon | undefined>();
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    async function fetchData() {
      if (!id) return;
      try {
        setIsLoading(true);
        const response = await apiService.get(`admin/coupons/${id}`);
        if (!response.data) {
          throw new Error("Failed to fetch coupon");
        }
        setCoupon(response.data);
      } catch (error) {
        toast.error("Error", {
          description: (error as Error).message,
        });
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [id]);

  const onEdit = async (data: Coupon) => {
    try {
      setIsLoading(true);

      await apiService.patch(`admin/coupons/${id}`, data);
      toast.success("Success", {
        description: "Coupon edited successfully",
      });
      router.push("/coupons");
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4">
      <CouponEditor
        coupon={coupon}
        isLoading={isLoading}
        onEdit={(coupon: Coupon) => {
          onEdit(coupon);
        }}
      />
    </div>
  );
}
