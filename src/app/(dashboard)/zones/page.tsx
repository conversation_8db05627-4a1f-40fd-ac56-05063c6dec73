"use client";

import { apiService } from "@/api";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { Zone } from "@/frontend-types";
import {
  DrawingManager,
  GoogleMap,
  Libraries,
  Polygon,
  useJsApiLoader,
} from "@react-google-maps/api";
import { Plus, Trash2 } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";

const containerStyle = {
  width: "100%",
  height: "650px",
};

const libraries: Libraries = [
  "drawing",
  "geometry",
  "places",
  // "maps",
  // "core",
];

const mapIds = ["d9b0e56450b5f0af"];

export default function ZonesPage() {
  // Check if the Google Maps API key is available
  const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAP_KEY;

  // Log a warning if the API key is missing
  useEffect(() => {
    if (!googleMapsApiKey) {
      console.warn(
        "Google Maps API key is missing. Please check your environment variables."
      );
    }
  }, [googleMapsApiKey]);

  const { isLoaded, loadError } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: googleMapsApiKey || "",
    libraries: libraries,
    mapIds: mapIds,
  });

  const polygonRef = useRef<google.maps.Polygon | null>(null);
  const mapRef = useRef<google.maps.Map | null>(null);
  const [mode, setMode] = useState<"view" | "draw">("view");
  const [center, setCenter] = useState<{
    lat: number;
    lng: number;
  }>({
    lat: -3.745,
    lng: -38.523,
  });
  const [zones, setZones] = useState<Zone[]>([]);
  const [zone, setZone] = useState<Zone | undefined>();
  const [input, setInput] = useState("");
  const [newZone, setNewZone] = useState<Zone | null>(null);
  const [isNewZoneLoading, setIsNewZoneLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [zoneToDelete, setZoneToDelete] = useState<Zone | null>(null);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);

  const [query, setQuery] = useState("");
  const [searchResults, setSearchResults] = useState<
    google.maps.places.AutocompletePrediction[] | null
  >([]);

  const autocompleteService =
    useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);

  const [temp, setTemp] = useState(false);

  useEffect(() => {
    // Only initialize services if Google Maps is loaded and the map is available
    if (isLoaded && mapRef.current && window.google && window.google.maps) {
      try {
        autocompleteService.current =
          new window.google.maps.places.AutocompleteService();

        // Only create PlacesService if map is available
        if (mapRef.current) {
          placesService.current = new window.google.maps.places.PlacesService(
            mapRef.current
          );
        }
      } catch (error) {
        console.error("Error initializing Google Maps services:", error);
      }
    }
  }, [isLoaded, temp]);

  const onEdit = useCallback(() => {
    if (polygonRef.current && zone) {
      const polygon = polygonRef.current;
      const currentZone: Zone = { ...zone, coordinates: [] };

      polygon.getPath().forEach((path) => {
        const line = {
          lat: path.lat().toString(),
          long: path.lng().toString(),
        };
        currentZone.coordinates.push(line);
      });

      console.log("Updated Zone", currentZone);
      setZone(currentZone);
    }
  }, [zone]);

  // Bind refs to current Polygon and listeners
  const onPolygonLoad = useCallback(
    (polygon: google.maps.Polygon) => {
      polygonRef.current = polygon;
      const path = polygon.getPath();

      path.addListener("set_at", onEdit);
      path.addListener("insert_at", onEdit);
      path.addListener("remove_at", onEdit);
    },
    [onEdit]
  );

  // Clean up refs
  const onPolygonUnmount = useCallback(() => {
    polygonRef.current = null;
  }, []);

  const onLoad = useCallback(
    (map: google.maps.Map) => {
      try {
        // Make sure Google Maps is fully loaded
        if (window.google && window.google.maps) {
          const bounds = new window.google.maps.LatLngBounds(center);
          map.fitBounds(bounds);

          setTemp(!temp);
          mapRef.current = map;
        }
      } catch (error) {
        console.error("Error in map onLoad:", error);
      }
    },
    [center, temp]
  );

  const onUnmount = useCallback((map: google.maps.Map) => {
    console.log("unmount", map);
    mapRef.current = null;
  }, []);

  useEffect(() => {
    apiService
      .get("admin/zones")
      .then((response) => setZones(response.data as Zone[]))
      .catch((error) => console.error(error));
  }, []);

  async function createZone(newZone: Zone) {
    setIsNewZoneLoading(true);
    const response = await apiService.post("admin/zones", newZone);
    const zone: Zone = response.data as Zone;
    setZones((zones) => [...zones, zone]);
    setNewZone(null);
    setIsNewZoneLoading(false);
  }

  async function handleDeleteZone() {
    if (!zoneToDelete) return;

    try {
      setIsDeleteLoading(true);
      await apiService.delete(`admin/zones/${zoneToDelete.id}`);

      // Remove the deleted zone from the state
      setZones((currentZones) =>
        currentZones.filter((z) => z.id !== zoneToDelete.id)
      );

      // If the currently selected zone is the one being deleted, clear it
      if (zone && zone.id === zoneToDelete.id) {
        setZone(undefined);
      }

      toast.success("Success", {
        description: `Zone "${zoneToDelete.name}" deleted successfully`,
      });
    } catch (error) {
      console.error("Error deleting zone:", error);
      toast.error("Error", {
        description: `Failed to delete zone: ${(error as Error).message}`,
      });
    } finally {
      setIsDeleteLoading(false);
      setDeleteDialogOpen(false);
      setZoneToDelete(null);
    }
  }

  const handleSearch = (
    query: string,
    service: React.RefObject<google.maps.places.AutocompleteService | null>
  ) => {
    if (service.current && query.length > 0) {
      try {
        service.current.getPlacePredictions({ input: query }, (predictions) => {
          if (predictions) {
            setSearchResults(predictions);
          }
        });
      } catch (error) {
        console.error("Error getting place predictions:", error);
        setSearchResults([]);
      }
    } else {
      setSearchResults([]);
    }
  };

  const handlePlaceSelect = (placeId: string) => {
    try {
      // Only proceed if Google Maps is fully loaded
      if (!window.google || !window.google.maps) {
        console.warn("Google Maps API not fully loaded");
        return;
      }

      colorFeatureLayer(placeId, google.maps.FeatureType.POSTAL_CODE);

      if (placesService.current) {
        placesService.current.getDetails({ placeId }, (place, status) => {
          try {
            if (status === google.maps.places.PlacesServiceStatus.OK && place) {
              if (place.geometry?.location && place.geometry?.viewport) {
                const bounds = new window.google.maps.LatLngBounds();
                bounds.union(place.geometry.viewport);

                if (mapRef.current) {
                  mapRef.current.fitBounds(bounds);
                }

                if (place.geometry && place.geometry.viewport) {
                  console.log("Place boundaries:", place.geometry.viewport);
                }
              }
            } else {
              console.warn("Place details not found or error:", status);
            }
          } catch (error) {
            console.error("Error processing place details:", error);
          }
        });
      }
    } catch (error) {
      console.error("Error in handlePlaceSelect:", error);
    }
  };

  const colorFeatureLayer = (
    placeId: string,
    featureType: google.maps.FeatureType
  ) => {
    try {
      if (!mapRef.current || !window.google || !window.google.maps) return;

      // Check if getFeatureLayer is available
      if (typeof mapRef.current.getFeatureLayer !== "function") {
        console.warn(
          "getFeatureLayer is not available in this version of Google Maps"
        );
        return;
      }

      const featureLayer = mapRef.current.getFeatureLayer(featureType);
      if (!featureLayer) return;

      const featureStyleOptions: google.maps.FeatureStyleOptions = {
        strokeColor: "#ff7859",
        strokeOpacity: 1.0,
        strokeWeight: 3.0,
        fillColor: "#f5655b",
        fillOpacity: 0.5,
      };

      // Use a try-catch block for the style assignment
      try {
        //@ts-expect-error Feature layer style API might not be fully typed
        featureLayer.style = (options: { feature: { placeId: string } }) => {
          if (options.feature.placeId == placeId) {
            return featureStyleOptions;
          }
          return null;
        };
      } catch (error) {
        console.error("Error setting feature layer style:", error);
      }
    } catch (error) {
      console.error("Error in colorFeatureLayer:", error);
    }
  };

  if (loadError) {
    console.error("Google Maps failed to load:", loadError);
    return <div>Unable to load map</div>;
  }

  return isLoaded ? (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="font-bold text-2xl">Delivery Zones</h1>
        </div>
        <Button onClick={() => setMode("draw")}>
          <Plus className="mr-2 h-4 w-4" /> New Zone
        </Button>
      </div>

      <Separator className="my-4" />

      {/* Search bar */}
      <div className="relative w-full md:w-1/3">
        <Input
          placeholder="Search a place…"
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            handleSearch(e.target.value, autocompleteService);
          }}
          className="mb-2"
        />
        {searchResults && searchResults.length > 0 && (
          <ul className="absolute bg-white shadow rounded w-full z-10 max-h-60 overflow-auto">
            {searchResults.map((r) => (
              <li
                key={r.place_id}
                className="px-4 py-2 hover:bg-gray-200 cursor-pointer bg-gray-50"
                onClick={() => {
                  handlePlaceSelect(r.place_id);
                  setQuery(r.description);
                  setSearchResults([]);
                }}
              >
                {r.description}
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Map area */}
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-0 relative">
              <GoogleMap
                mapContainerStyle={containerStyle}
                center={center}
                zoom={8}
                onLoad={onLoad}
                onUnmount={onUnmount}
                options={{ mapId: mapIds[0], disableDefaultUI: true }}
              >
                {mode === "draw" && (
                  <DrawingManager
                    drawingMode={google.maps.drawing.OverlayType.POLYGON}
                    options={{
                      drawingControl: true,
                      drawingControlOptions: {
                        position: google.maps.ControlPosition.TOP_CENTER,
                        drawingModes: [google.maps.drawing.OverlayType.POLYGON],
                      },
                      polygonOptions: {
                        editable: true,
                        draggable: true,
                      },
                    }}
                    onPolygonComplete={(poly) => {
                      setMode("view");
                      const coords: Zone["coordinates"] = [];
                      poly.getPath().forEach((pt) =>
                        coords.push({
                          lat: pt.lat().toString(),
                          long: pt.lng().toString(),
                        })
                      );
                      poly.setMap(null);
                      setNewZone({ id: 0, name: "", coordinates: coords });
                    }}
                  />
                )}

                {mode === "view" && zone && (
                  <Polygon
                    paths={zone.coordinates.map((c) => ({
                      lat: +c.lat,
                      lng: +c.long,
                    }))}
                    editable
                    onLoad={onPolygonLoad}
                    onUnmount={onPolygonUnmount}
                    onMouseUp={onEdit}
                    options={{
                      strokeColor: "#2563EB",
                      fillColor: "#BFDBFE",
                      fillOpacity: 0.4,
                      strokeWeight: 2,
                    }}
                  />
                )}
              </GoogleMap>
            </CardContent>
          </Card>
        </div>

        {/* Zones list */}
        <div className="lg:col-span-1">
          <Card className="h-full">
            <CardHeader className="pb-2">
              <CardTitle>Zones List</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y divide-gray-200 overflow-auto max-h-[600px]">
                {zones.length === 0 && (
                  <div className="p-6 text-center text-gray-500">
                    No zones defined
                  </div>
                )}
                {zones.map((z) => (
                  <div
                    key={z.id}
                    className={`flex justify-between items-center px-4 py-3 cursor-pointer ${
                      z.id === zone?.id ? "bg-blue-50" : "hover:bg-gray-200"
                    }`}
                    onClick={() => {
                      setZone(z);
                      setCenter({
                        lat: +z.coordinates[0].lat,
                        lng: +z.coordinates[0].long,
                      });
                      setMode("view");
                    }}
                  >
                    <span className="text-black font-medium">{z.name}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        setZoneToDelete(z);
                        setDeleteDialogOpen(true);
                      }}
                      className="h-8 w-8"
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Save new zone dialog */}
      <Dialog
        open={!!newZone}
        onOpenChange={(open) => !open && setNewZone(null)}
      >
        <DialogContent className="max-w-md space-y-4 p-6 bg-gray-50">
          <DialogHeader>
            <DialogTitle>Save Zone</DialogTitle>
            <DialogDescription>
              Enter a name for this new delivery zone.
            </DialogDescription>
          </DialogHeader>
          <Input
            placeholder="Zone Name"
            value={input}
            onChange={(e) => setInput(e.target.value)}
          />
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setNewZone(null)}>
              Cancel
            </Button>
            <Button
              onClick={() =>
                newZone &&
                input.trim() &&
                createZone({ ...newZone, name: input })
              }
              disabled={isNewZoneLoading}
            >
              {isNewZoneLoading ? "Saving…" : "Save"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the zone &ldquo;{zoneToDelete?.name}
              &rdquo;. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteZone}
              className="bg-red-500"
              disabled={isDeleteLoading}
            >
              {isDeleteLoading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  ) : (
    <></>
  );
}
