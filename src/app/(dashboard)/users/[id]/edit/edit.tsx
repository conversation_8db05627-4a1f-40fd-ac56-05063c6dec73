"use client";

import { apiService } from "@/api";
import AssignWarehouses from "@/components/AssignWarehouses";
import { AssignRoles } from "@/components/permissions/assign-roles";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { User, USER_TYPES, UserType, Warehouse } from "@/frontend-types";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function EditUser({ id }: { id: string }) {
  const [user, setUser] = useState<User | null>(null);
  const [assignedWarehouses, setAssignedWarehouses] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (!id) return;
    async function fetchUser() {
      try {
        const response = await apiService.get(`admin/users/${id}`);
        const userToEdit = response.data;
        if (!userToEdit) throw new Error("User not found");
        setUser(userToEdit);

        if (userToEdit.type === "WAREHOUSE_STAFF") {
          const assigned = await apiService.get(`admin/users/${id}/warehouses`);
          setAssignedWarehouses(assigned.data.map((w: Warehouse) => w.id));
        }
      } catch (error) {
        toast("Error", { description: (error as Error).message });
      }
    }
    fetchUser();
  }, [id]);

  const handleSubmit = async () => {
    if (!user) return;
    try {
      setLoading(true);
      await apiService.patch(`admin/users/${id}`, user);

      if (user.type === "WAREHOUSE_STAFF") {
        await apiService.patch("admin/warehouses/assign-staff", {
          userId: user.id,
          warehouseIds: assignedWarehouses,
        });
      }

      toast.success("User updated successfully");
      router.push("/users");
    } catch (error) {
      toast.error("Update failed", { description: (error as Error).message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex flex-row mb-4 items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/users")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="font-bold text-2xl">Edit User</h1>
      </div>
      <Separator className="mb-6" />

      {user && (
        <div className="space-y-4 max-w-2xl">
          <div>
            <Label>Name</Label>
            <Input
              value={user.name}
              onChange={(e) => setUser({ ...user, name: e.target.value })}
            />
          </div>

          <div>
            <Label>Phone</Label>
            <Input
              value={user.phone}
              onChange={(e) => setUser({ ...user, phone: e.target.value })}
            />
          </div>

          <div>
            <Label>User Type</Label>
            <Select
              value={user.type}
              onValueChange={(value) =>
                setUser({ ...user, type: value as UserType })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select user type" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(USER_TYPES).map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {user.type === "WAREHOUSE_STAFF" && (
            <AssignWarehouses
              selected={assignedWarehouses}
              onChange={setAssignedWarehouses}
            />
          )}

          <AssignRoles userId={user.id} />

          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? "Saving..." : "Save"}
          </Button>
        </div>
      )}
    </div>
  );
}
