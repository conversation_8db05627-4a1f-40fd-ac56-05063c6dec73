"use client";

import { apiService } from "@/api";
import PaginationBar from "@/components/PaginationBar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";
import { PaginatedResponse, PaginationData, User } from "@/frontend-types";
import { Copy, Download, MoreHorizontal } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 1,
  });
  const [search, setSearch] = useState("");

  const router = useRouter();

  useEffect(() => {
    async function fetchUsers() {
      try {
        const response = await apiService.get("admin/users", {
          page: pagination.page,
          pageSize: pagination.pageSize,
          search,
        });

        const { data: users, ...paginationData } =
          response.data as PaginatedResponse<User>;
        setUsers(users);
        setPagination(paginationData);
      } catch (error) {
        toast.error((error as Error).message, {
          description: `${error}`,
        });
      }
    }

    fetchUsers();
  }, [pagination.page, pagination.pageSize, search]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const handleDownloadExcel = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/admin/users/download/excel`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to download Excel file");
      }

      // Get the filename from the response headers
      const contentDisposition = response.headers.get("content-disposition");
      const filename = contentDisposition
        ? contentDisposition.split("filename=")[1]?.replace(/"/g, "")
        : `users-export-${new Date().toISOString().split("T")[0]}.xlsx`;

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success("Excel file downloaded successfully");
    } catch (error) {
      toast.error("Failed to download Excel file", {
        description: `${error}`,
      });
    }
  };

  return (
    <div className="p-4">
      <div className="flex flex-row mb-4 justify-between items-center">
        <h1 className="font-bold text-2xl">Users</h1>
        <div className="flex items-center gap-4">
          <Input
            className="w-80"
            placeholder="Search name, email or phone..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          <Button
            variant="outline"
            onClick={handleDownloadExcel}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Download Excel
          </Button>
        </div>
      </div>
      <Separator className="my-4" />

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Address</TableHead>
            <TableHead>Referral Code</TableHead>
            <TableHead>Referrals</TableHead>
            <TableHead>Reward Points</TableHead>
            <TableHead>Reward Tier</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Total orders</TableHead>
            <TableHead>Total spend</TableHead>
            <TableHead>AOV</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id}>
              <TableCell>{user.name}</TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>{user.type}</TableCell>
              <TableCell>{user.addresses[0]?.streetName ?? "-"}</TableCell>
              <TableCell>
                {user.referralCode ? (
                  <div className="flex items-center gap-2">
                    <span className="font-mono bg-gray-100 px-2 py-1 rounded text-black">
                      {user.referralCode}
                    </span>
                    <Button
                      variant={"ghost"}
                      size={"icon"}
                      onClick={(e) => {
                        e.stopPropagation();
                        navigator.clipboard.writeText(user.referralCode || "");
                        toast("Copied to clipboard", {
                          description: "Referral code copied to clipboard",
                        });
                      }}
                    >
                      <Copy />
                    </Button>
                  </div>
                ) : (
                  <span className="text-gray-400">Not generated</span>
                )}
              </TableCell>
              <TableCell>
                {user.referrals ? user.referrals.length : 0}
              </TableCell>
              <TableCell>
                {user.rewardPoints !== undefined ? user.rewardPoints : "-"}
              </TableCell>
              <TableCell>
                {user.rewardTier ? (
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                    {user.rewardTier.name}
                  </span>
                ) : (
                  <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">
                    No Tier
                  </span>
                )}
              </TableCell>
              <TableCell>
                {new Date(user.createdAt).toLocaleDateString()}
              </TableCell>
              <TableCell>{user.totalOrders ?? 0}</TableCell>
              <TableCell>Rs. {user.totalOrderPrice ?? "0.00"}</TableCell>
              <TableCell>Rs. {user.averageOrderPrice ?? "0.00"}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => router.push(`/users/${user.id}/edit`)}
                    >
                      Edit
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <PaginationBar
        className="mt-12"
        pagination={pagination}
        handlePageChange={handlePageChange}
      />
    </div>
  );
}

export default UsersPage;
