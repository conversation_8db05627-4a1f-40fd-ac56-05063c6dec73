"use client";

import { apiService } from "@/api";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";
import { Supplier, Warehouse } from "@/frontend-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { PlusCircle, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";

const formatDateForInput = (date: Date | undefined) => {
  if (!date) return "";
  return date.toISOString().split("T")[0];
};

const inventoryItemSchema = z
  .object({
    barcode: z.string().min(1, "Barcode is required"),
    buyingPrice: z.number().positive("Buying price must be a positive number"),
    sellingPrice: z
      .number()
      .positive("Selling price must be a positive number"),
    manufactureDate: z.date().optional(),
    expiryDate: z.date({
      required_error: "Expiry date is required",
      invalid_type_error: "Expiry date must be a valid date",
    }),
    supplierId: z.number().int().positive().optional(),
    initialStock: z.number().int().positive(),
  })
  .refine((data) => data.buyingPrice < data.sellingPrice, {
    message: "Buying price must be less than selling price",
    path: ["sellingPrice"],
  });

const createInventorySchema = z.object({
  batchName: z.string().min(1, "Batch name is required"),
  warehouseId: z.number().positive("Warehouse is required"),
  inventories: z
    .array(inventoryItemSchema)
    .min(1, "At least one inventory item is required"),
});

type FormValues = z.infer<typeof createInventorySchema>;

export default function AddInventoryPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);

  const form = useForm<FormValues>({
    resolver: zodResolver(createInventorySchema),
    defaultValues: {
      batchName: "",
      warehouseId: undefined as unknown as number,
      inventories: [
        {
          barcode: undefined,
          buyingPrice: undefined,
          sellingPrice: undefined,
          initialStock: undefined,
          supplierId: undefined,
          expiryDate: undefined as unknown as Date,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    name: "inventories",
    control: form.control,
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [whRes, supplierRes] = await Promise.all([
          apiService.get("admin/warehouses"),
          apiService.get("admin/suppliers"),
        ]);
        setWarehouses(whRes.data.data as Warehouse[]);
        setSuppliers(supplierRes.data as Supplier[]);
      } catch (err) {
        console.log(err);
        toast.error("Error", {
          description: "Failed to fetch warehouses or suppliers",
        });
      }
    };

    fetchData();
  }, []);

  const onSubmit = async (batch: FormValues) => {
    setIsSubmitting(true);
    try {
      await apiService.post("admin/inventories", batch);

      toast.success("Success", {
        description: "Inventory items have been added successfully",
      });

      router.push("/inventories");
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6 max-w">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Add New Inventory</h1>
        <div className="flex gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              append({
                barcode: "",
                buyingPrice: "" as unknown as number,
                sellingPrice: "" as unknown as number,
                initialStock: "" as unknown as number,
                supplierId: undefined,
                expiryDate: undefined as unknown as Date,
              })
            }
          >
            <PlusCircle className="mr-2 h-4 w-4" /> Add Another Item
          </Button>

          <Button
            type="submit"
            onClick={form.handleSubmit(onSubmit)}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Adding Inventory..." : "Add Inventory"}
          </Button>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <FormField
            control={form.control}
            name="batchName"
            render={({ field }) => (
              <FormItem className="mb-6">
                <FormLabel>Batch Number</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Enter batch number" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="warehouseId"
            render={({ field }) => (
              <FormItem className="mb-6">
                <FormLabel>Select Warehouse</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={(value) => field.onChange(Number(value))}
                    value={field.value?.toString() || ""}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a warehouse" />
                    </SelectTrigger>
                    <SelectContent>
                      {warehouses.map((wh) => (
                        <SelectItem key={wh.id} value={wh.id.toString()}>
                          {wh.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Barcode</TableHead>
                <TableHead>Initial Stock</TableHead>
                <TableHead>Buying Price</TableHead>
                <TableHead>Selling Price</TableHead>
                <TableHead>Supplier</TableHead>
                <TableHead>Manufacture Date</TableHead>
                <TableHead>Expiry Date</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {fields.map((field, index) => (
                <TableRow key={field.id}>
                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`inventories.${index}.barcode`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="string"
                              {...field}
                              onChange={(e) => field.onChange(e.target.value)}
                              placeholder="Barcode"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`inventories.${index}.initialStock`}
                      render={({ field }) => (
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                            placeholder="Initial Stock"
                          />
                        </FormControl>
                      )}
                    />
                  </TableCell>

                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`inventories.${index}.buyingPrice`}
                      render={({ field }) => (
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                            placeholder="Buying Price"
                          />
                        </FormControl>
                      )}
                    />
                  </TableCell>

                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`inventories.${index}.sellingPrice`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="number"
                              {...field}
                              onChange={(e) =>
                                field.onChange(Number(e.target.value))
                              }
                              placeholder="Selling Price"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`inventories.${index}.supplierId`}
                      render={({ field }) => (
                        <FormControl>
                          <Select
                            onValueChange={(value) =>
                              field.onChange(Number(value))
                            }
                            value={field.value?.toString() || ""}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select supplier" />
                            </SelectTrigger>
                            <SelectContent>
                              {suppliers.map((s) => (
                                <SelectItem key={s.id} value={s.id.toString()}>
                                  {s.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                      )}
                    />
                  </TableCell>

                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`inventories.${index}.manufactureDate`}
                      render={({ field }) => (
                        <FormControl>
                          <Input
                            type="date"
                            {...field}
                            value={formatDateForInput(field.value)}
                            onChange={(e) =>
                              field.onChange(
                                e.target.value
                                  ? new Date(e.target.value)
                                  : undefined
                              )
                            }
                          />
                        </FormControl>
                      )}
                    />
                  </TableCell>

                  <TableCell>
                    <FormField
                      control={form.control}
                      name={`inventories.${index}.expiryDate`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              type="date"
                              {...field}
                              value={formatDateForInput(field.value)}
                              onChange={(e) =>
                                field.onChange(
                                  e.target.value
                                    ? new Date(e.target.value)
                                    : undefined
                                )
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TableCell>

                  <TableCell>
                    {index > 0 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => remove(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </form>
      </Form>
    </div>
  );
}
