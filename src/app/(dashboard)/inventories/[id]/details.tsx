"use client";

import { apiService } from "@/api";
import { AddInventoryTransaction } from "@/components/inventory/AddInventoryTransaction";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { formatCurrency, formatDate } from "@/lib/utils";
import { Inventory } from "@/frontend-types";
import {
  Box,
  Calendar,
  ChevronLeft,
  Edit,
  History,
  MapPin,
  Warehouse,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function InventoryDetails({ id }: { id: string }) {
  const [inventory, setInventory] = useState<Inventory | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const refreshInventory = async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      const response = await apiService.get(`admin/inventories/${id}`);
      if (!response.data) throw new Error("Could not get inventory details.");
      setInventory(response.data as Inventory);
    } catch (error) {
      console.error(error);
      toast.error("Error fetching inventory", {
        description: `${error}`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshInventory();
  }, [id]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (!inventory) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>Inventory not found</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-[1600px] mx-auto p-6">
        <Button variant="ghost" onClick={() => router.back()} className="mb-6">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Back to Inventory
        </Button>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Main Content - Left Side */}
          <div className="flex-grow space-y-6 max-w-[1000px]">
            <div className="bg-card p-6 border rounded-lg">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h1 className="text-2xl font-semibold mb-1">
                    Batch {inventory.batch.name}
                  </h1>
                  <div className="flex items-center text-muted-foreground text-sm">
                    <Calendar className="h-4 w-4 mr-1" />
                    {formatDate(inventory.createdAt)}
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Badge
                    status={inventory.quantity > 0 ? "DELIVERED" : "FAILED"}
                  >
                    {inventory.quantity > 0 ? "In Stock" : "Out of Stock"}
                  </Badge>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() =>
                      router.push(`/inventories/${inventory.id}/edit`)
                    }
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h2 className="text-base font-semibold mb-3">
                    Product Details
                  </h2>
                  <div className="space-y-2">
                    <p className="font-medium">{inventory.product.name}</p>
                    <p className="text-muted-foreground">
                      {inventory.product.description}
                    </p>
                    <div className="text-sm text-muted-foreground mt-4">
                      <p>Weight: {inventory.product.weight}g</p>
                      <p>
                        Dimensions: {inventory.product.length} x{" "}
                        {inventory.product.width} x {inventory.product.height}{" "}
                        cm
                      </p>
                    </div>
                  </div>
                </div>

                <div>
                  <h2 className="text-base font-semibold mb-3">Pricing</h2>
                  <div className="space-y-2">
                    <p>
                      Buying Price: {formatCurrency(+inventory.buyingPrice)}
                    </p>
                    <p>
                      Selling Price: {formatCurrency(+inventory.sellingPrice)}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg border">
              <div className="p-6 border-b">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="flex items-center text-base font-semibold">
                    <Box className="h-4 w-4 mr-2" />
                    Inventory Information
                  </h2>
                </div>
              </div>

              <div className="space-y-4 p-4">
                <p className="text-muted-foreground">
                  Manufacture Date: {formatDate(inventory.manufactureDate)}
                </p>

                <p className="text-muted-foreground">
                  Expiry Date: {formatDate(inventory.expiryDate)}
                </p>

                <p className="text-muted-foreground">
                  Supplier:{" "}
                  {inventory.supplierId
                    ? `Supplier #${inventory.supplierId}`
                    : "N/A"}
                </p>
              </div>
            </div>

            <div className="bg-card rounded-lg border">
              <div className="p-6 border-b">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="flex items-center text-base font-semibold">
                    <Warehouse className="h-4 w-4 mr-2" />
                    Warehouse Information
                  </h2>
                </div>
              </div>

              <div className="space-y-4 p-4">
                {inventory.batch.warehouse ? (
                  <>
                    <p className="text-muted-foreground">
                      Warehouse Name:{" "}
                      <span className="font-medium">
                        {inventory.batch.warehouse.name}
                      </span>
                    </p>

                    <p className="text-muted-foreground">
                      Warehouse Type:{" "}
                      <span className="font-medium">
                        {inventory.batch.warehouse.type}
                      </span>
                    </p>

                    <div className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                      <p className="text-muted-foreground">
                        Location:{" "}
                        <span className="font-medium">
                          Lat: {inventory.batch.warehouse.lat}, Long:{" "}
                          {inventory.batch.warehouse.long}
                        </span>
                      </p>
                    </div>
                  </>
                ) : (
                  <p className="text-muted-foreground">
                    No warehouse information available
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Transactions - Right Side */}
          <div className="w-full lg:w-80 space-y-6">
            <AddInventoryTransaction
              inventoryId={inventory.id}
              onTransactionAdded={refreshInventory}
            />

            <div className="bg-card p-6 rounded-lg border">
              <h2 className="flex items-center font-semibold mb-4">
                <History className="h-4 w-4 mr-2" />
                Inventory Transactions
              </h2>
              <div className="space-y-4">
                {inventory.inventoryTransactions.length === 0 ? (
                  <p className="text-muted-foreground">
                    No transactions recorded.
                  </p>
                ) : (
                  inventory.inventoryTransactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className="flex justify-between items-center border-b pb-4"
                    >
                      <div className="flex-1">
                        <p className="font-medium text-sm">
                          {transaction.type}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(transaction.createdAt)}
                        </p>
                        {transaction.remark && (
                          <p className="text-xs text-muted-foreground mt-2">
                            Remark: {transaction.remark}
                          </p>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          Quantity: {transaction.quantity}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Order ID: {transaction.orderId || "N/A"}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
