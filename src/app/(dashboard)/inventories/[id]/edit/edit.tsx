"use client";

import { apiService } from "@/api";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON>ton } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { cn, formatDate } from "@/lib/utils";
import { Inventory, Supplier } from "@/frontend-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { CalendarIcon, ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const editInventorySchema = z
  .object({
    buyingPrice: z.number().positive("Buying price must be a positive number"),
    sellingPrice: z
      .number()
      .positive("Selling price must be a positive number"),
    manufactureDate: z.date().optional(),
    expiryDate: z.date({
      required_error: "Expiry date is required",
      invalid_type_error: "Expiry date must be a valid date",
    }),
    supplierId: z.number().int().positive().optional(),
  })
  .refine((data) => data.buyingPrice < data.sellingPrice, {
    message: "Buying price must be less than selling price",
    path: ["sellingPrice"],
  });

type FormValues = z.infer<typeof editInventorySchema>;

export default function EditInventory({ id }: { id: string }) {
  const [inventory, setInventory] = useState<Inventory | null>(null);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const form = useForm<FormValues>({
    resolver: zodResolver(editInventorySchema),
    defaultValues: {
      buyingPrice: 0,
      sellingPrice: 0,
      manufactureDate: undefined,
      expiryDate: undefined,
      supplierId: undefined,
    },
  });

  useEffect(() => {
    if (!id) return;

    async function fetchData() {
      try {
        setIsLoading(true);

        // Fetch inventory details
        const inventoryResponse = await apiService.get(
          `admin/inventories/${id}`
        );
        if (!inventoryResponse.data)
          throw new Error("Could not get inventory details.");

        // Fetch suppliers for dropdown
        const suppliersResponse = await apiService.get("admin/suppliers");
        if (!suppliersResponse.data)
          throw new Error("Could not get suppliers.");

        const inventoryData = inventoryResponse.data as Inventory;
        setInventory(inventoryData);
        setSuppliers(suppliersResponse.data as Supplier[]);

        // Set form default values
        form.reset({
          buyingPrice: +inventoryData.buyingPrice,
          sellingPrice: +inventoryData.sellingPrice,
          manufactureDate: inventoryData.manufactureDate
            ? new Date(inventoryData.manufactureDate)
            : undefined,
          expiryDate: inventoryData.expiryDate
            ? new Date(inventoryData.expiryDate)
            : undefined,
          supplierId: inventoryData.supplierId || undefined,
        });
      } catch (error) {
        console.error(error);
        toast.error("Error fetching data", {
          description: `${error}`,
        });
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [id, form]);

  const onSubmit = async (values: FormValues) => {
    if (!id) return;

    setIsSubmitting(true);
    try {
      await apiService.put(`admin/inventories/${id}`, values);

      toast.success("Success", {
        description: "Inventory has been updated successfully",
      });

      router.push(`/inventories/${id}`);
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (!inventory) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>Inventory not found</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push(`/inventories/${id}`)}
          className="mr-4"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back to Inventory Details
        </Button>
        <h1 className="text-2xl font-bold">Edit Inventory</h1>
      </div>

      <div className="bg-card p-6 border rounded-lg">
        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">
            Product: {inventory.product.name}
          </h2>
          <p className="text-muted-foreground">Batch: {inventory.batch.name}</p>
          <p className="text-muted-foreground">
            Created: {formatDate(inventory.createdAt)}
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="buyingPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Buying Price</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value))
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sellingPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Selling Price</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseFloat(e.target.value))
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="manufactureDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Manufacture Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expiryDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Expiry Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="supplierId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      defaultValue={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a supplier" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {suppliers.map((supplier) => (
                          <SelectItem
                            key={supplier.id}
                            value={supplier.id.toString()}
                          >
                            {supplier.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(`/inventories/${id}`)}
                className="mr-2"
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
