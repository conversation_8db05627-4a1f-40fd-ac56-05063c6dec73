"use client";

import { apiService } from "@/api";
import { InventoryTable } from "@/components/inventory/InventoryTable";
import PaginationBar from "@/components/PaginationBar";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { Inventory, PaginationData } from "@/frontend-types";
import { PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function InventoryPage() {
  const [inventories, setInventories] = useState<Inventory[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 1,
  });

  const router = useRouter();

  useEffect(() => {
    async function fetchInventory() {
      try {
        const response = await apiService.get("admin/inventories", {
          page: pagination.page,
          pageSize: pagination.pageSize,
        });

        if (!response.data) {
          throw new Error("Could not get inventory items.");
        }

        const { data: inventoryItems, ...paginationData } = response.data;

        setInventories(inventoryItems as Inventory[]);
        setPagination(paginationData as PaginationData);
      } catch (error) {
        toast.error((error as Error).message, {
          description: `${error}`,
        });
      }
    }

    fetchInventory();
  }, [pagination.page, pagination.pageSize]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  return (
    <div className="p-4">
      <div className="flex flex-row mb-4 justify-between items-center">
        <h1 className="font-bold text-2xl">Inventory</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => router.push("/inventories/batches")}
            className="flex items-center gap-2"
          >
            View Batches
          </Button>
          <Button
            onClick={() => router.push("/inventories/add")}
            className="flex items-center gap-2"
          >
            <PlusIcon className="h-4 w-4" />
            Add Inventory
          </Button>
        </div>
      </div>
      <Separator className="my-4" />
      <InventoryTable
        inventories={inventories}
        onClick={(inventory) => {
          router.push(`/inventories/${inventory.id}`);
        }}
      />

      <PaginationBar
        pagination={pagination}
        handlePageChange={handlePageChange}
        className="mt-12"
      />
    </div>
  );
}
