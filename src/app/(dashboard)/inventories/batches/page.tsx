"use client";

import { apiService } from "@/api";
import PaginationBar from "@/components/PaginationBar";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";
import { formatDate } from "@/lib/utils";
import { InventoryBatch, PaginationData } from "@/frontend-types";
import { ChevronLeft, PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function BatchesPage() {
  const [batches, setBatches] = useState<InventoryBatch[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 1,
  });

  const router = useRouter();

  useEffect(() => {
    async function fetchBatches() {
      try {
        const response = await apiService.get("admin/inventories/batches", {
          page: pagination.page,
          pageSize: pagination.pageSize,
        });

        if (!response.data) {
          throw new Error("Could not get inventory batches.");
        }

        const { data: batchItems, ...paginationData } = response.data;

        setBatches(batchItems as InventoryBatch[]);
        setPagination(paginationData as PaginationData);
      } catch (error) {
        toast.error((error as Error).message, {
          description: `${error}`,
        });
      }
    }

    fetchBatches();
  }, [pagination.page, pagination.pageSize]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  return (
    <div className="p-4">
      <div className="flex flex-row mb-4 items-center">
        <Button
          variant="ghost"
          onClick={() => router.push("/inventories")}
          className="mr-4"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back to Inventory
        </Button>
        <h1 className="font-bold text-2xl">Inventory Batches</h1>
      </div>
      <Separator className="my-4" />

      <div className="flex justify-end mb-4">
        <Button
          onClick={() => router.push("/inventories/add")}
          className="flex items-center gap-2"
        >
          <PlusIcon className="h-4 w-4" />
          Add New Batch
        </Button>
      </div>

      <div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Batch Name</TableHead>
              <TableHead>Warehouse</TableHead>
              <TableHead>Created Date</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {batches.map((batch) => (
              <TableRow key={batch.id}>
                <TableCell>{batch.name}</TableCell>
                <TableCell>{batch.warehouse?.name || "N/A"}</TableCell>
                <TableCell>{formatDate(batch.createdAt)}</TableCell>
                <TableCell>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      router.push(`/inventories/batches/${batch.id}`)
                    }
                  >
                    View Inventories
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <PaginationBar
        pagination={pagination}
        handlePageChange={handlePageChange}
        className="mt-12"
      />
    </div>
  );
}
