"use client";

import { apiService } from "@/api";
import { InventoryTable } from "@/components/inventory/InventoryTable";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import PaginationBar from "@/components/PaginationBar";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { formatDate } from "@/lib/utils";
import { Inventory, InventoryBatch, PaginationData } from "@/frontend-types";
import { ChevronLeft, Warehouse } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function BatchDetails({ id }: { id: string }) {
  const [batch, setBatch] = useState<InventoryBatch | null>(null);
  const [inventories, setInventories] = useState<Inventory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 1,
  });

  const router = useRouter();

  useEffect(() => {
    if (!id) return;

    async function fetchBatchDetails() {
      try {
        setIsLoading(true);
        // First, get the batch details
        const batchResponse = await apiService.get(
          `admin/inventories/batches/${id}`
        );
        if (!batchResponse.data)
          throw new Error("Could not get batch details.");

        // Then get the inventories for this batch
        const inventoriesResponse = await apiService.get(
          `admin/inventories/batches/${id}`,
          {
            page: pagination.page,
            pageSize: pagination.pageSize,
          }
        );

        if (!inventoriesResponse.data)
          throw new Error("Could not get batch inventories.");

        const { data: inventoryItems, ...paginationData } =
          inventoriesResponse.data;

        setBatch(batchResponse.data as InventoryBatch);
        setInventories(inventoryItems as Inventory[]);
        setPagination(paginationData as PaginationData);
      } catch (error) {
        console.error(error);
        toast.error("Error fetching batch details", {
          description: `${error}`,
        });
      } finally {
        setIsLoading(false);
      }
    }

    fetchBatchDetails();
  }, [id, pagination.page, pagination.pageSize]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (!batch) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>Batch not found</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex flex-row mb-4 items-center">
        <Button
          variant="ghost"
          onClick={() => router.push("/inventories/batches")}
          className="mr-4"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back to Batches
        </Button>
        <h1 className="font-bold text-2xl">Batch: {batch.name}</h1>
      </div>

      <div className="bg-card p-6 border rounded-lg mb-6">
        <div className="flex items-center mb-4">
          <Warehouse className="h-5 w-5 mr-2" />
          <h2 className="text-xl font-semibold">Warehouse Information</h2>
        </div>

        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <p className="text-muted-foreground">Warehouse Name:</p>
            <p className="font-medium">{batch.warehouse?.name || "N/A"}</p>
          </div>
          {batch.warehouse && (
            <>
              <div>
                <p className="text-muted-foreground">Warehouse Type:</p>
                <p className="font-medium">{batch.warehouse.type}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Location:</p>
                <p className="font-medium">
                  Lat: {batch.warehouse.lat}, Long: {batch.warehouse.long}
                </p>
              </div>
            </>
          )}
          <div>
            <p className="text-muted-foreground">Created Date:</p>
            <p className="font-medium">{formatDate(batch.createdAt)}</p>
          </div>
        </div>
      </div>

      <Separator className="my-4" />

      <h2 className="text-xl font-semibold mb-4">Inventories in this Batch</h2>

      <InventoryTable
        inventories={inventories}
        onClick={(inventory) => {
          router.push(`/inventories/${inventory.id}`);
        }}
      />

      <PaginationBar
        pagination={pagination}
        handlePageChange={handlePageChange}
        className="mt-12"
      />
    </div>
  );
}
