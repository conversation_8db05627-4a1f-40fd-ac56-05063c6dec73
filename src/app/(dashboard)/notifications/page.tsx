"use client";

import { apiService } from "@/api";
import ItemPicker, { Column } from "@/components/ItemPicker";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { ErrorResponse, PaginatedResponse } from "@/frontend-types";
import { AxiosError } from "axios";
import { Bell, DollarSign, Send, Target, Upload, Users, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface RewardTier {
  id: number;
  name: string;
  type: string;
  _count: {
    users: number;
  };
}

interface User {
  id: number;
  name: string;
  email: string;
  firebaseToken: string | null;
}

interface NotificationFormData {
  title: string;
  description: string;
  imageUrl?: string;
  targetType: "ALL_USERS" | "REWARD_TIERS" | "SPECIFIC_USER" | "SPENDING_BASED";
  rewardTierIds?: number[];
  userId?: number;
  spendingAmount?: number;
  spendingDays?: number;
}

function NotificationsPage() {
  const [formData, setFormData] = useState<NotificationFormData>({
    title: "",
    description: "",
    targetType: "ALL_USERS",
  });
  const [selectedRewardTiers, setSelectedRewardTiers] = useState<RewardTier[]>(
    []
  );
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Column definitions for ItemPicker
  const userColumns: Column<User>[] = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      id: "notificationStatus",
      header: "Notification Status",
      render: (user) =>
        user.firebaseToken ? (
          <Badge variant="default" className="bg-green-100 text-green-800">
            Enabled
          </Badge>
        ) : (
          <Badge variant="secondary" className="bg-red-100 text-red-800">
            Disabled
          </Badge>
        ),
    },
  ];

  const rewardTierColumns: Column<RewardTier>[] = [
    {
      accessorKey: "name",
      header: "Tier Name",
    },
    {
      accessorKey: "type",
      header: "Type",
    },
    {
      id: "userCount",
      header: "Users",
      render: (tier) => `${tier._count.users} users`,
    },
  ];

  // Fetch functions for ItemPicker
  const fetchUsers = async (
    page: number,
    pageSize: number,
    searchQuery: string
  ): Promise<PaginatedResponse<User>> => {
    try {
      const response = await apiService.get("admin/users", {
        search: searchQuery,
        page,
        pageSize,
      });
      return response.data;
    } catch (error) {
      toast.error("Failed to fetch users");
      throw error;
    }
  };

  const fetchRewardTiers = async (
    page: number,
    pageSize: number,
    searchQuery: string
  ): Promise<PaginatedResponse<RewardTier>> => {
    try {
      const response = await apiService.get("admin/notifications/reward-tiers");
      const tiers = response.data;

      // Filter by search query if provided
      const filteredTiers = searchQuery
        ? tiers.filter((tier: RewardTier) =>
            tier.name.toLowerCase().includes(searchQuery.toLowerCase())
          )
        : tiers;

      // Simulate pagination for reward tiers (since the API doesn't paginate them)
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedTiers = filteredTiers.slice(startIndex, endIndex);

      return {
        data: paginatedTiers,
        total: filteredTiers.length,
        page,
        pageSize,
        totalPages: Math.ceil(filteredTiers.length / pageSize),
      };
    } catch (error) {
      toast.error("Failed to fetch reward tiers");
      throw error;
    }
  };

  const handleImageUpload = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await apiService.post("admin/media/upload", formData, {
        ContentType: "multipart/form-data",
      });

      return response.data.url;
    } catch (error) {
      toast.error("Failed to upload image");
      throw error;
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setImageFile(null);
    setImagePreview(null);
    setFormData({ ...formData, imageUrl: undefined });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim() || !formData.description.trim()) {
      toast.error("Title and description are required");
      return;
    }

    setIsLoading(true);

    try {
      let imageUrl = formData.imageUrl;

      // Upload image if selected
      if (imageFile) {
        imageUrl = await handleImageUpload(imageFile);
      }

      const payload = {
        ...formData,
        imageUrl,
        rewardTierIds: selectedRewardTiers.map((tier) => tier.id),
        userId: selectedUser?.id,
      };

      const response = await apiService.post(
        "admin/notifications/send",
        payload
      );

      if (response.data.success) {
        toast.success("Notification sent successfully", {
          description: `Sent to ${response.data.targetedUsers} users`,
        });

        // Reset form
        setFormData({
          title: "",
          description: "",
          targetType: "ALL_USERS",
        });
        setSelectedRewardTiers([]);
        setSelectedUser(null);
        setImageFile(null);
        setImagePreview(null);
      } else {
        toast.error("Failed to send notification", {
          description: response.data.error,
        });
      }
    } catch (error) {
      if (error instanceof AxiosError) {
        toast.error("Failed to send notification", {
          description: (error.response?.data as ErrorResponse)?.message,
        });
        return;
      } else {
        toast.error("Failed to send notification", {
          description: (error as Error).message,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const getTargetDescription = () => {
    switch (formData.targetType) {
      case "ALL_USERS":
        return "Send to all users with valid notification tokens";
      case "REWARD_TIERS":
        const totalUsers = selectedRewardTiers.reduce(
          (sum, tier) => sum + tier._count.users,
          0
        );
        return selectedRewardTiers.length > 0
          ? `Send to users in selected reward tiers (${totalUsers} users)`
          : "Select reward tiers to target";
      case "SPECIFIC_USER":
        return selectedUser
          ? `Send to ${selectedUser.name} (${selectedUser.email})`
          : "Select a specific user";
      case "SPENDING_BASED":
        return `Send to users who spent $${
          formData.spendingAmount || 0
        } in the past ${formData.spendingDays || 0} days`;
      default:
        return "";
    }
  };

  return (
    <div className="p-4 max-w-4xl">
      <div className="flex flex-row mb-4 items-center gap-4">
        <Bell className="h-6 w-6" />
        <h1 className="font-bold text-2xl">Send Push Notification</h1>
      </div>
      <Separator className="my-4" />

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Send className="h-5 w-5" />
              Notification Content
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) =>
                  setFormData({ ...formData, title: e.target.value })
                }
                placeholder="Enter notification title"
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                placeholder="Enter notification description"
                rows={3}
                required
              />
            </div>

            <div>
              <Label htmlFor="image">Image (Optional)</Label>
              <div className="mt-2">
                {imagePreview ? (
                  <div className="relative inline-block">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-32 h-32 object-cover rounded-lg border"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                      onClick={removeImage}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ) : (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                    <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                    <Label
                      htmlFor="image-upload"
                      className="cursor-pointer text-sm text-gray-600"
                    >
                      Click to upload image
                    </Label>
                    <Input
                      id="image-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      className="hidden"
                    />
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Target Audience
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="mb-2" htmlFor="targetType">
                Target Type
              </Label>
              <Select
                value={formData.targetType}
                onValueChange={(value: NotificationFormData["targetType"]) =>
                  setFormData({ ...formData, targetType: value })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL_USERS">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      All Users
                    </div>
                  </SelectItem>
                  <SelectItem value="REWARD_TIERS">
                    <div className="flex items-center gap-2">
                      <Badge className="h-4 w-4" />
                      Reward Tiers
                    </div>
                  </SelectItem>
                  <SelectItem value="SPECIFIC_USER">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Specific User
                    </div>
                  </SelectItem>
                  <SelectItem value="SPENDING_BASED">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Spending Based
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Reward Tiers Selection */}
            {formData.targetType === "REWARD_TIERS" && (
              <div>
                <Label>Select Reward Tiers</Label>
                <div className="mt-2">
                  <ItemPicker<RewardTier>
                    title="Select Reward Tiers"
                    selectionMode="multiple"
                    columns={rewardTierColumns}
                    fetchItems={fetchRewardTiers}
                    onConfirmSelection={(selected) => {
                      const tiers = Array.isArray(selected)
                        ? selected
                        : [selected];
                      setSelectedRewardTiers(tiers);
                    }}
                    initialSelectedItems={selectedRewardTiers}
                    searchPlaceholder="Search reward tiers..."
                    renderTrigger={() => (
                      <Button
                        variant="outline"
                        className="w-full justify-start"
                      >
                        <Target className="h-4 w-4 mr-2" />
                        {selectedRewardTiers.length > 0 ? (
                          <span>
                            {selectedRewardTiers.length} tier(s) selected
                          </span>
                        ) : (
                          <span className="text-gray-500">
                            Select reward tiers...
                          </span>
                        )}
                      </Button>
                    )}
                  />
                  {selectedRewardTiers.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedRewardTiers.map((tier) => (
                        <Badge key={tier.id} variant="secondary">
                          {tier.name} ({tier._count.users} users)
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Specific User Selection */}
            {formData.targetType === "SPECIFIC_USER" && (
              <div>
                <Label>Select User</Label>
                <div className="mt-2">
                  <ItemPicker<User>
                    title="Select User"
                    selectionMode="single"
                    columns={userColumns}
                    fetchItems={fetchUsers}
                    onConfirmSelection={(selected) => {
                      const user = Array.isArray(selected)
                        ? selected[0]
                        : selected;
                      setSelectedUser(user);
                    }}
                    initialSelectedItems={selectedUser ? [selectedUser] : []}
                    searchPlaceholder="Search users by name or email..."
                    renderTrigger={() => (
                      <Button
                        variant="outline"
                        className="w-full justify-start"
                      >
                        <Users className="h-4 w-4 mr-2" />
                        {selectedUser ? (
                          <span>
                            {selectedUser.name} ({selectedUser.email})
                          </span>
                        ) : (
                          <span className="text-gray-500">
                            Select a user...
                          </span>
                        )}
                      </Button>
                    )}
                  />
                </div>
              </div>
            )}

            {/* Spending Based Criteria */}
            {formData.targetType === "SPENDING_BASED" && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="spendingAmount">
                    Minimum Spending Amount
                  </Label>
                  <Input
                    id="spendingAmount"
                    type="number"
                    value={formData.spendingAmount || ""}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        spendingAmount: parseFloat(e.target.value) || 0,
                      })
                    }
                    placeholder="Enter amount"
                    min="0"
                    step="0.01"
                  />
                </div>
                <div>
                  <Label htmlFor="spendingDays">In Past Days</Label>
                  <Input
                    id="spendingDays"
                    type="number"
                    value={formData.spendingDays || ""}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        spendingDays: parseInt(e.target.value) || 0,
                      })
                    }
                    placeholder="Enter days"
                    min="1"
                  />
                </div>
              </div>
            )}

            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-700">{getTargetDescription()}</p>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Send className="h-4 w-4" />
            {isLoading ? "Sending..." : "Send Notification"}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default NotificationsPage;
