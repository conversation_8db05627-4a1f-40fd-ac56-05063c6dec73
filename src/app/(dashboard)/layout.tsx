"use client";

import { LoadingSpinner } from "@/components/LoadingSpinner";
import NavigationBar from "@/components/NavigationBar";
import { AppSidebar } from "@/components/Sidebar";
import { Label } from "@/components/ui/label";
import { SidebarProvider } from "@/components/ui/sidebar";
import { useAuth } from "@/context";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";

export default function Layout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading && !isAuthenticated()) {
      console.log("withAuth: not authenticated");
      router.replace("/auth");
    }
  }, [isAuthenticated, router, isLoading]);

  // If not authenticated, don't render anything
  if (!isAuthenticated() && isLoading) {
    return (
      <main className="flex flex-row items-center justify-center h-screen">
        <LoadingSpinner className="" />
      </main>
    );
  }

  if (!isAuthenticated() && !isLoading) {
    return (
      <main className="flex flex-row items-center justify-center h-screen">
        <Label>Unauthorized</Label>
      </main>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <main className="w-full">
        <NavigationBar />
        <div className="mt-14 bg-gray-100">{children}</div>
      </main>
    </SidebarProvider>
  );
}
