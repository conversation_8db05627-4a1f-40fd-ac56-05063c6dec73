"use client";

import { Separator } from "@/components/ui/separator";
import { PaginatedResponse, PaginationData, Product } from "@/frontend-types";
import ProductTable from "@/components/products/ProductTable";
import { useEffect, useState } from "react";
import { apiService } from "@/api";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import PaginationBar from "@/components/PaginationBar";

function ProductsPage() {
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 1,
  });
  const [search, setSearch] = useState("");

  const onDeleteProduct = async (id: number) => {
    try {
      const response = await apiService.delete(`admin/products/${id}`);

      if (!response.data) {
        throw new Error("Could not delete product.");
      }

      setProducts(products.filter((p) => p.id != id));
    } catch (error) {
      console.log(error);
      toast.error((error as Error).message, {
        description: `${error}`,
      });
    }
  };

  useEffect(() => {
    async function fetchData() {
      try {
        const response = await apiService.get("admin/products", {
          page: pagination.page,
          pageSize: pagination.pageSize,
          search,
        });

        if (!response.data) {
          throw new Error("Could not get products.");
        }

        const { data: products, ...paginationData } =
          response.data as PaginatedResponse<Product>;

        setProducts(products);
        setPagination(paginationData as PaginationData);
      } catch (error) {
        toast.error((error as Error).message, {
          description: `${error}`,
        });
      }
    }
    fetchData();
  }, [pagination.page, pagination.pageSize, search]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const handleSearchChange = (value: string) => {
    setSearch(value);
    // Reset to page 1 when search changes
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  return (
    <div className="p-4">
      <div className="flex flex-row mb-4 justify-between items-center">
        <div>
          <h1 className="font-bold text-2xl">Products</h1>
          <p className="text-sm text-gray-600 mt-1">
            Total: {pagination.total} products
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Input
            className="w-80"
            placeholder="Search products by name, barcode, or description..."
            value={search}
            onChange={(e) => handleSearchChange(e.target.value)}
          />
          <Button
            variant="default"
            onClick={() => router.push("/products/add")}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>
      </div>
      <Separator />
      <div>
        <ProductTable products={products} onDelete={onDeleteProduct} />
      </div>
      <div>
        <PaginationBar
          pagination={pagination}
          handlePageChange={handlePageChange}
          className="mt-12"
        />
      </div>
    </div>
  );
}

export default ProductsPage;
