"use client";

import { apiService } from "@/api";
import {
  ProductEditor,
  ProductEditorProps,
} from "@/components/products/ProductEditor";
import { toast } from "sonner";
import { ErrorResponse, Product } from "@/frontend-types";
import { AxiosError } from "axios";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function EditProduct({ id }: { id: string }) {
  const router = useRouter();
  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (id) {
      fetchProduct(Number(id));
    }
  }, [id]);

  const fetchProduct = async (productId: number) => {
    try {
      setIsLoading(true);
      const response = await apiService.get(`admin/products/${productId}`);

      // Fetch the thumbnail if it exists
      const productData = response.data;

      setProduct(productData);
    } catch (error) {
      toast.error("Error", {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = async (
    data: Parameters<ProductEditorProps["onSubmit"]>[number]
  ) => {
    if (!product) {
      return;
    }

    try {
      setIsLoading(true);

      // Upload images first if there are any
      const uploadedMediaItems: {
        type: "IMAGE" | "VIDEO";
        url: string;
      }[] = [];

      const thumbnail: (typeof uploadedMediaItems)[number] = {
        type: "IMAGE",
        url: data.thumbnail?.url ?? "",
      };

      // Upload thumbnail if it exists and has a file
      if (data.thumbnail?.file) {
        try {
          const formData = new FormData();
          formData.append("file", data.thumbnail.file);

          const response = await apiService.post(
            "admin/media/upload",
            formData,
            { ContentType: "multipart/form-data" }
          );

          if (response.data?.url) {
            thumbnail.url = response.data.url;
          }
        } catch (error) {
          console.error("Error uploading thumbnail:", error);
          toast.error("Thumbnail Upload Failed", {
            description: (error as Error).message,
          });
        }
      }

      // Upload gallery images
      if (data.files?.images && data.files.images.length > 0) {
        for (const file of data.files.images) {
          try {
            const formData = new FormData();
            formData.append("file", file);

            const response = await apiService.post(
              "admin/media/upload",
              formData,
              { ContentType: "multipart/form-data" }
            );

            if (response.data?.url) {
              uploadedMediaItems.push({
                type: "IMAGE",
                url: response.data.url,
              });
            }
          } catch (error) {
            console.error("Error uploading image:", error);
            toast.error("Image Upload Failed", {
              description: (error as Error).message,
            });
          }
        }
      }

      const currentImages = data.currentImages ?? [];
      product.media = product.media.filter((media) =>
        currentImages.some((img) => img.url === media.url)
      );

      // Prepare product data
      const productData: Omit<
        Product,
        | "createdAt"
        | "updatedAt"
        | "id"
        | "category"
        | "relatedProducts"
        | "media"
        | "thumbnail"
        | "thumbnailId"
        | "price"
        | "quantity"
      > & {
        media: {
          type: "IMAGE" | "VIDEO";
          url: string;
        }[];
        thumbnail?: {
          type: "IMAGE" | "VIDEO";
          url: string;
        };
        relatedProductIds: number[];
      } = {
        name: data.name,
        barcode: data.barcode,
        description: data.description,
        slug: data.slug,
        categoryId: parseInt(data.categoryId, 10),
        gstPercentage: parseFloat(data.gstPercentage),
        weight: data.weight ? parseFloat(data.weight) : 0,
        weightUnit: data.weightUnit,
        length: data.length ? parseFloat(data.length) : 0,
        width: data.width ? parseFloat(data.width) : 0,
        height: data.height ? parseFloat(data.height) : 0,
        discountValue: data.discountValue
          ? parseFloat(data.discountValue)
          : null,
        discountType: data.discountType ?? null,
        media: product.media.map((img) => {
          return {
            type: img.mediaType,
            url: img.url,
          };
        }),
        thumbnail: thumbnail,
        hasVariations: data.hasVariations,
        highlights: data.highlights,
        information: data.information,
        productPolicies: data.productPolicies ?? [],
        active: data.active,
        relatedProductIds: data.relatedProductIds ?? [],
        maxCartQuantity: data.maxCartQuantity
          ? Number(data.maxCartQuantity)
          : null,
      };

      // Handle media items

      if (uploadedMediaItems.length > 0) {
        // If the product already has media, we need to append the new ones
        if (productData.media && productData.media.length > 0) {
          const keptMediaItems = product.media.map((media) => ({
            type: media.mediaType,
            url: media.url,
          }));

          productData.media = [...keptMediaItems, ...uploadedMediaItems];
        } else {
          productData.media = uploadedMediaItems;
        }
      }

      await apiService.patch(`admin/products/${id}`, productData);

      toast.success("Success", {
        description: "Product updated successfully",
      });

      router.refresh();
    } catch (error) {
      toast.error("Error", {
        description: ((error as AxiosError).response?.data as ErrorResponse)
          .message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!product && !isLoading) {
    return <div className="p-4">Loading product...</div>;
  }

  return (
    <div className="p-4">
      {product && (
        <ProductEditor
          product={product}
          isLoading={isLoading}
          onSubmit={handleEdit}
        />
      )}
    </div>
  );
}
