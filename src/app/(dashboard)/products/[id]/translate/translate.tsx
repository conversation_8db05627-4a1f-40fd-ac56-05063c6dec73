"use client";

import { apiService } from "@/api";
import TranslationDialog from "@/components/products/TranslationDialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";
import { Product, ProductTranslation } from "@/frontend-types";
import { ArrowLeft, Pencil } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function ProductTranslationPage({ id }: { id: string }) {
  const router = useRouter();
  const [product, setProduct] = useState<Product | null>(null);
  const [translations, setTranslations] = useState<ProductTranslation[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedTranslation, setSelectedTranslation] =
    useState<ProductTranslation | null>(null);

  const fetchTranslations = async () => {
    try {
      const response = await apiService.get(`admin/products/${id}`);
      if (!response.data) throw new Error("Failed to fetch product");

      const productData = response.data as Product;
      const translationRes = await apiService.get(
        `admin/products/${id}/translations`
      );
      setProduct(productData);
      setTranslations(translationRes.data || []);
    } catch (error) {
      toast.error("Error loading translations", {
        description: (error as Error).message,
      });
    }
  };

  useEffect(() => {
    if (id) fetchTranslations();
  }, [id]);

  const handleSave = async (data: {
    languageId: number;
    name: string;
    description: string;
  }) => {
    try {
      if (!id) return;

      if (selectedTranslation) {
        await apiService.patch(
          `admin/products/${id}/translations/${selectedTranslation.languageId}`,
          data
        );
      } else {
        await apiService.post(`admin/products/${id}/translations`, data);
      }

      toast.success("Saved successfully");
      setDialogOpen(false);
      setSelectedTranslation(null);
      fetchTranslations();
    } catch (error) {
      toast.error("Error saving translation", {
        description: (error as Error).message,
      });
    }
  };

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex flex-row mb-4 items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/products")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>

          <h1 className="text-2xl font-bold">
            <div>Manage Translations</div>
          </h1>
        </div>
        <Button
          onClick={() => {
            setSelectedTranslation(null);
            setDialogOpen(true);
          }}
        >
          Add Translation
        </Button>
      </div>

      {product && (
        <div className="mb-6">
          <h2 className="text-lg font-semibold">{product.name}</h2>
          <p className="text-sm text-muted-foreground">
            Barcode: {product.barcode}
          </p>
          <p className="text-lg  mt-4">Description: {product.description}</p>
        </div>
      )}

      <Separator className="mb-4" />

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[200px]">Language</TableHead>
            <TableHead>Translated Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {translations.map((t) => (
            <TableRow key={t.languageId}>
              <TableCell className="flex items-center gap-2">
                <Image
                  src={t.language.flagUrl}
                  alt={t.language.code}
                  className="w-5 h-5 rounded-sm"
                />
                {t.language.name} ({t.language.code})
              </TableCell>
              <TableCell>{t.name}</TableCell>
              <TableCell>{t.description}</TableCell>
              <TableCell className="text-right">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => {
                    setSelectedTranslation(t);
                    setDialogOpen(true);
                  }}
                >
                  <Pencil className="w-4 h-4 mr-1" />
                  Edit
                </Button>
              </TableCell>
            </TableRow>
          ))}
          {translations.length === 0 && (
            <TableRow>
              <TableCell
                colSpan={4}
                className="text-center py-6 text-muted-foreground"
              >
                No translations found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <TranslationDialog
        open={dialogOpen}
        onClose={() => {
          setDialogOpen(false);
          setSelectedTranslation(null);
        }}
        onSubmit={handleSave}
        initialData={selectedTranslation}
      />
    </div>
  );
}
