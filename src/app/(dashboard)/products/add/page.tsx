"use client";

import { apiService } from "@/api";
import {
  ProductEditor,
  ProductEditorProps,
} from "@/components/products/ProductEditor";
import { toast } from "sonner";
import { ErrorResponse, Product } from "@/frontend-types";
import { AxiosError } from "axios";
import { useRouter } from "next/navigation";
import { useState } from "react";

function AddProductPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleCreate = async (
    data: Parameters<ProductEditorProps["onSubmit"]>[number]
  ) => {
    try {
      setIsLoading(true);

      const uploadedMediaItems: {
        type: "IMAGE" | "VIDEO";
        url: string;
      }[] = [];

      const thumbnail: (typeof uploadedMediaItems)[number] = {
        type: "IMAGE",
        url: data.thumbnail?.url ?? "",
      };

      // Upload thumbnail if it exists and has a file
      if (data.thumbnail?.file) {
        try {
          const formData = new FormData();
          formData.append("file", data.thumbnail.file);

          const response = await apiService.post(
            "admin/media/upload",
            formData,
            { ContentType: "multipart/form-data" }
          );

          if (response.data?.url) {
            thumbnail.url = response.data.url;
          }
        } catch (error) {
          console.error("Error uploading thumbnail:", error);
          toast.error("Thumbnail Upload Failed", {
            description: (error as Error).message,
          });
        }
      }

      // Upload gallery images
      if (data.files?.images && data.files.images.length > 0) {
        for (const file of data.files.images) {
          try {
            const formData = new FormData();
            formData.append("file", file);

            const response = await apiService.post(
              "admin/media/upload",
              formData,
              { ContentType: "multipart/form-data" }
            );

            if (response.data?.url) {
              uploadedMediaItems.push({
                type: "IMAGE",
                url: response.data.url,
              });
            }
          } catch (error) {
            console.error("Error uploading image:", error);
            toast.error("Image Upload Failed", {
              description: (error as Error).message,
            });
          }
        }
      }

      const productData: Omit<
        Product,
        | "createdAt"
        | "updatedAt"
        | "id"
        | "category"
        | "relatedProducts"
        | "media"
        | "thumbnail"
        | "thumbnailId"
        | "price"
        | "quantity"
      > & {
        media: {
          type: "IMAGE" | "VIDEO";
          url: string;
        }[];
        thumbnail?: {
          type: "IMAGE" | "VIDEO";
          url: string;
        };
        relatedProductIds: number[];
      } = {
        name: data.name,
        barcode: data.barcode,
        description: data.description,
        slug: data.slug,
        categoryId: parseInt(data.categoryId, 10),
        gstPercentage: parseFloat(data.gstPercentage),
        weight: data.weight ? parseFloat(data.weight) : 0,
        weightUnit: data.weightUnit,
        length: data.length ? parseFloat(data.length) : 0,
        width: data.width ? parseFloat(data.width) : 0,
        height: data.height ? parseFloat(data.height) : 0,
        discountValue: data.discountValue
          ? parseFloat(data.discountValue)
          : null,
        discountType: data.discountType ?? null,
        media: uploadedMediaItems,
        thumbnail: thumbnail,
        hasVariations: data.hasVariations,
        highlights: data.highlights,
        information: data.information,
        productPolicies: data.productPolicies ?? [],
        active: data.active,
        relatedProductIds: data.relatedProductIds ?? [],
        maxCartQuantity: data.maxCartQuantity
          ? Number(data.maxCartQuantity)
          : null,
      };

      await apiService.post("admin/products", productData);

      toast.success("Success", {
        description: "Product created successfully",
      });

      router.push("/products");
    } catch (error) {
      toast.error("Error", {
        description: ((error as AxiosError).response?.data as ErrorResponse)
          .message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4">
      <ProductEditor isLoading={isLoading} onSubmit={handleCreate} />
    </div>
  );
}

export default AddProductPage;
