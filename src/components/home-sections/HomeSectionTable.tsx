import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDate } from "@/lib/utils";
import { HomeSection } from "@/frontend-types";
import { Edit, Eye, MoreHorizontal, Trash2, Languages } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Button } from "../ui/button";

export interface HomeSectionTableProps {
  homeSections: HomeSection[];
  onView: (homeSection: HomeSection) => void;
  onEdit: (homeSection: HomeSection) => void;
  onDelete: (homeSection: HomeSection) => void;
  onTranslate: (homeSection: HomeSection) => void;
}

export function HomeSectionTable({
  homeSections,
  onView,
  onEdit,
  onDelete,
  onTranslate,
}: HomeSectionTableProps) {
  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Warehouse</TableHead>
            <TableHead>Display Order</TableHead>
            <TableHead>Only Discount</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {homeSections.map((section) => (
            <TableRow key={section.id}>
              <TableCell>{section.title}</TableCell>
              <TableCell>{section.type}</TableCell>
              <TableCell>
                {section.warehouse ? section.warehouse.name : "Global"}
              </TableCell>
              <TableCell>{section.displayOrder}</TableCell>
              <TableCell>{section.onlyDiscount ? "Yes" : "No"}</TableCell>
              <TableCell>{formatDate(section.createdAt)}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onView(section)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onEdit(section)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onTranslate(section)}>
                      <Languages className="mr-2 h-4 w-4" />
                      Translate
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-red-500"
                      onClick={() => onDelete(section)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
