"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { HomeSection, Warehouse } from "@/frontend-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";

const homeSectionSchema = z.object({
  title: z.string().min(1, "Title is required"),
  type: z.enum(["CATEGORY", "PRODUCT"]),
  onlyDiscount: z.boolean().default(false),
  warehouseId: z.number().int().nullable(),
  displayOrder: z.number().int().min(0),
});

type FormValues = z.infer<typeof homeSectionSchema>;

export interface HomeSectionEditorProps {
  homeSection?: HomeSection;
  warehouses: Warehouse[];
  isLoading: boolean;
  onEdit?: (homeSection: FormValues) => void;
  onCreate?: (homeSection: FormValues) => void;
}

export function HomeSectionEditor({
  homeSection,
  warehouses,
  isLoading,
  onEdit,
  onCreate,
}: HomeSectionEditorProps) {
  const router = useRouter();
  const isEditMode = !!homeSection;

  const form = useForm<FormValues>({
    resolver: zodResolver(homeSectionSchema),
    defaultValues: {
      title: homeSection?.title ?? "",
      type: homeSection?.type ?? "CATEGORY",
      onlyDiscount: homeSection?.onlyDiscount ?? false,
      warehouseId: homeSection?.warehouseId ?? null,
      displayOrder: homeSection?.displayOrder ?? 0,
    },
  });

  useEffect(() => {
    if (homeSection) {
      form.reset({
        title: homeSection.title,
        type: homeSection.type,
        onlyDiscount: homeSection.onlyDiscount,
        warehouseId: homeSection.warehouseId,
        displayOrder: homeSection.displayOrder,
      });
    }
  }, [homeSection, form]);

  const onSubmit = (values: FormValues) => {
    if (isEditMode && onEdit) {
      onEdit(values);
    } else if (onCreate) {
      onCreate(values);
    }
  };

  return (
    <div>
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/home-sections")}
          className="mr-2"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold">
          {isEditMode ? "Edit Home Section" : "Create Home Section"}
        </h1>
      </div>

      <Separator className="my-4" />

      <div className="max-w-2xl">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="CATEGORY">Category</SelectItem>
                          <SelectItem value="PRODUCT">Product</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Category type will show categories, Product type will
                        show products
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="warehouseId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Warehouse</FormLabel>
                      <Select
                        onValueChange={(value) =>
                          field.onChange(
                            value === "null" ? null : parseInt(value)
                          )
                        }
                        defaultValue={field.value?.toString() || "null"}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select warehouse (optional)" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="null">
                            Global (No specific warehouse)
                          </SelectItem>
                          {warehouses.map((warehouse) => (
                            <SelectItem
                              key={warehouse.id}
                              value={warehouse.id.toString()}
                            >
                              {warehouse.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        If no warehouse is selected, this section will be global
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="displayOrder"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Display Order</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseInt(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormDescription>
                        Lower numbers will be displayed first
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="onlyDiscount"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          Only Discount Items
                        </FormLabel>
                        <FormDescription>
                          If enabled, only discounted items will be shown in
                          this section
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/home-sections")}
                className="mr-2"
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : isEditMode ? "Update" : "Create"}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
