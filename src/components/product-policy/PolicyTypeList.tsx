import { Pen<PERSON><PERSON>, Plus, Trash2 } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { apiService } from "../../api";
import { MultipleImageUpload } from "../../components/multiple-image-upload";
import { Button } from "../../components/ui/button";
import { Card, CardHeader, CardTitle } from "../../components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../../components/ui/dialog";
import { Input } from "../../components/ui/input";
import { Label } from "../../components/ui/label";
import { toast } from "sonner";
import { ProductPolicyType } from "../../frontend-types";

export default function PolicyTypeList() {
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [selectedType, setSelectedType] = useState<ProductPolicyType | null>(
    null
  );
  const [formData, setFormData] = useState({
    name: "",
  });
  const [iconImage, setIconImage] = useState<{ url: string; file?: File }[]>(
    []
  );
  const [editIconImage, setEditIconImage] = useState<
    { url: string; file?: File }[]
  >([]);
  const [policyTypes, setPolicyTypes] = useState<ProductPolicyType[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch policy types
  useEffect(() => {
    fetchPolicyTypes();
  }, []);

  const fetchPolicyTypes = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.get("admin/product-policy/type");
      setPolicyTypes(response.data);
    } catch (error) {
      toast.error("Error", {
        description: "Failed to fetch policy types",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreate = async () => {
    try {
      if (!formData.name) {
        toast.error("Error", {
          description: "Please enter a name for the policy type",
        });
        return;
      }

      // Handle icon upload if provided
      let iconUrl = "";

      if (iconImage.length > 0 && iconImage[0].file) {
        // Upload the icon image
        const uploadFormData = new FormData();
        uploadFormData.append("file", iconImage[0].file);

        // Upload the file with the correct content type
        const response = await apiService.post(
          "admin/media/upload",
          uploadFormData,
          { ContentType: "multipart/form-data" }
        );

        if (response.data?.url) {
          iconUrl = response.data.url;
        }
      }

      await apiService.post("admin/product-policy/type", {
        name: formData.name,
        icon: iconUrl,
      });

      toast.success("Success", {
        description: "Policy type created successfully",
      });

      setIsCreateOpen(false);
      setFormData({ name: "" });
      setIconImage([]);
      fetchPolicyTypes();
    } catch (error) {
      console.error("Error creating policy type:", error);
      toast.error("Error", {
        description: "Failed to create policy type",
      });
    }
  };

  const handleUpdate = async () => {
    if (selectedType) {
      try {
        if (!formData.name) {
          toast.error("Error", {
            description: "Please enter a name for the policy type",
          });
          return;
        }

        // Handle icon upload if provided
        let iconUrl = selectedType.icon || "";

        if (editIconImage.length > 0 && editIconImage[0].file) {
          // Upload the icon image
          const uploadFormData = new FormData();
          uploadFormData.append("file", editIconImage[0].file);

          const response = await apiService.post(
            "admin/media/upload",
            uploadFormData,
            { ContentType: "multipart/form-data" }
          );

          if (response.data?.url) {
            iconUrl = response.data.url;
          }
        }

        await apiService.patch(`admin/product-policy/type/${selectedType.id}`, {
          name: formData.name,
          icon: iconUrl,
        });

        toast.success("Success", {
          description: "Policy type updated successfully",
        });

        setIsEditOpen(false);
        setSelectedType(null);
        setEditIconImage([]);
        fetchPolicyTypes();
      } catch (error) {
        console.error("Error updating policy type:", error);
        toast.error("Error", {
          description: "Failed to update policy type",
        });
      }
    }
  };

  const handleDelete = async () => {
    if (selectedType) {
      try {
        await apiService.delete(`admin/product-policy/type/${selectedType.id}`);
        toast.success("Success", {
          description: "Policy type deleted successfully",
        });
        setIsDeleteOpen(false);
        setSelectedType(null);
        fetchPolicyTypes();
      } catch (error) {
        toast.error("Error", {
          description: "Failed to delete policy type",
        });
      }
    }
  };

  const openEditDialog = (type: ProductPolicyType) => {
    setSelectedType(type);
    setFormData({
      name: type.name,
    });

    // Initialize edit icon image if the type has an icon
    if (type.icon) {
      setEditIconImage([{ url: type.icon }]);
    } else {
      setEditIconImage([]);
    }

    setIsEditOpen(true);
  };

  const openDeleteDialog = (type: ProductPolicyType) => {
    setSelectedType(type);
    setIsDeleteOpen(true);
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Product Policy Types</h1>
        <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Policy Type
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Create New Policy Type</DialogTitle>
              <DialogDescription>
                Add a new policy type that can be applied to products.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-start gap-4 pt-2">
                <Label className="text-right pt-2">Upload Icon</Label>
                <div className="col-span-3">
                  <MultipleImageUpload
                    value={iconImage}
                    onChange={setIconImage}
                    label="Add Icon"
                    id="policy-type-icon"
                    maxImages={1}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    Upload an icon for this policy type
                  </p>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleCreate}>Create</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {isLoading ? (
        <div>Loading...</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {policyTypes.map((type) => (
            <Card key={type.id}>
              <CardHeader>
                <CardTitle className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    {type.icon && (
                      <div className="relative w-6 h-6">
                        <Image
                          src={type.icon}
                          alt={`${type.name} icon`}
                          fill
                          className="object-contain"
                          unoptimized={true}
                        />
                      </div>
                    )}
                    {type.name}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => openEditDialog(type)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => openDeleteDialog(type)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
            </Card>
          ))}
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Policy Type</DialogTitle>
            <DialogDescription>
              Update the details of this policy type.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                Name
              </Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-start gap-4 pt-2">
              <Label className="text-right pt-2">Upload Icon</Label>
              <div className="col-span-3">
                <MultipleImageUpload
                  value={editIconImage}
                  onChange={setEditIconImage}
                  label="Update Icon"
                  id="edit-policy-type-icon"
                  maxImages={1}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Upload a new icon for this policy type
                </p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleUpdate}>Update</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Delete Policy Type</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this policy type? This action
              cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
