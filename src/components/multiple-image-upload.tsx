import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Plus, X } from "lucide-react";
import Image from "next/image";
import { ChangeEvent, useEffect, useRef, useState } from "react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";

interface ImageItem {
  url: string;
  file?: File;
}

interface MultipleImageUploadProps {
  value?: ImageItem[];
  onChange: (images: ImageItem[]) => void;
  className?: string;
  label?: string;
  id?: string;
  maxImages?: number;
}

export function MultipleImageUpload({
  value = [],
  onChange,
  className,
  label = "Upload Images",
  id = "multiple-image-upload",
  maxImages = 10,
}: MultipleImageUploadProps) {
  const [images, setImages] = useState<ImageItem[]>(value);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Update images when value changes
  useEffect(() => {
    try {
      if (value && value.length > 0) {
        // Don't use JSON.parse/stringify as it corrupts File objects
        // Instead, make a shallow copy that preserves File objects
        const imagesCopy = value.map((img) => ({
          url: img.url,
          file: img.file, // Preserve the File object
        }));
        setImages(imagesCopy);
      }
    } catch (error) {
      console.error("Error updating images in MultipleImageUpload:", error);
      // If there's an error, at least try to set the images directly
      if (value && value.length > 0) {
        setImages([...value]);
      }
    }
  }, [value]);

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Check if adding these files would exceed the maximum
    if (images.length + files.length > maxImages) {
      toast.error("Too many images", {
        description: `You can only upload a maximum of ${maxImages} images.`,
      });
      return;
    }

    // Validate and process all files first
    const validFiles: File[] = [];

    for (const file of Array.from(files)) {
      // Check file type
      const validTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
      if (!validTypes.includes(file.type)) {
        toast.error("Invalid file type", {
          description: `File "${file.name}" is not a valid image type. Please upload JPG, PNG, GIF, or WebP images.`,
        });
        continue;
      }

      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File too large", {
          description: `File "${file.name}" is too large. Images must be less than 5MB.`,
        });
        continue;
      }

      validFiles.push(file);
    }

    if (validFiles.length === 0) return;

    // Process all valid files and collect the results
    const newImages: ImageItem[] = [];

    try {
      // Use Promise.all to process all files concurrently but wait for all to complete
      const imagePromises = validFiles.map((file) => {
        return new Promise<ImageItem>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const previewUrl = reader.result as string;
            const newImage: ImageItem = {
              url: previewUrl,
              file: file,
            };
            resolve(newImage);
          };
          reader.onerror = () => {
            reject(new Error(`Failed to read file: ${file.name}`));
          };
          reader.readAsDataURL(file);
        });
      });

      const processedImages = await Promise.all(imagePromises);
      newImages.push(...processedImages);

      // Update state with all new images at once
      setImages((prev) => {
        const updatedImages = [...prev, ...newImages];
        onChange(updatedImages);
        return updatedImages;
      });
    } catch (error) {
      console.error("Error processing files:", error);
      toast.error("Error processing files", {
        description: "Some files could not be processed. Please try again.",
      });
    }

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleRemove = (index: number) => {
    setImages((prev) => {
      const updatedImages = prev.filter((_, i) => i !== index);
      onChange(updatedImages);
      return updatedImages;
    });
  };

  return (
    <div className={cn("space-y-4", className)}>
      <Input
        type="file"
        accept="image/jpeg,image/png,image/gif,image/webp"
        onChange={handleFileChange}
        ref={fileInputRef}
        className="hidden"
        id={id}
        multiple
      />

      <div className="grid grid-cols-2 gap-4">
        {images.map((image, index) => (
          <div
            key={index}
            className="relative w-full h-48 border rounded-md overflow-hidden"
          >
            {image && image.url ? (
              <Image
                src={image.url}
                alt={`Product image ${index + 1}`}
                fill
                className="object-contain"
                unoptimized={image.url.startsWith("http")}
                onError={() =>
                  console.error(`Error loading image: ${image.url}`)
                }
              />
            ) : (
              <div className="flex items-center justify-center h-full bg-gray-100">
                <span className="text-gray-400">Image not available</span>
              </div>
            )}
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2 h-8 w-8 rounded-full"
              onClick={() => handleRemove(index)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ))}

        {images.length < maxImages && (
          <label
            htmlFor={id}
            className="flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-md cursor-pointer hover:bg-muted/50 transition-colors"
          >
            <div className="flex flex-col items-center justify-center pt-5 pb-6">
              <Plus className="w-10 h-10 mb-3 text-muted-foreground" />
              <p className="mb-2 text-sm text-muted-foreground">
                <span className="font-semibold">{label}</span>
              </p>
              <p className="text-xs text-muted-foreground">
                JPG, PNG, GIF or WebP (max 5MB)
              </p>
            </div>
          </label>
        )}
      </div>
    </div>
  );
}
