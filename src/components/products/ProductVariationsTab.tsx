import { apiService } from "@/api";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus<PERSON>ir<PERSON>, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { MultipleImageUpload } from "../multiple-image-upload";

// Types for our component
interface ProductAttribute {
  id: number;
  name: string;
  options: ProductAttributeOption[];
}

interface ProductAttributeOption {
  id: number;
  name: string;
  colorCode: string | null;
  imageUrl: string | null;
  iconUrl: string | null;
}

interface ProductVariation {
  id: number;
  barcode: string;
  options: {
    option: {
      id: number;
      name: string;
      colorCode: string | null;
      imageUrl: string | null;
      iconUrl: string | null;
      attribute: {
        id: number;
        name: string;
      };
    };
  }[];
  media: {
    id: number;
    url: string;
    mediaType: string;
  }[];
}

interface ProductVariationsTabProps {
  productId: number;
  hasVariations: boolean;
  onToggleVariations: (hasVariations: boolean) => void;
}

// Form schemas
const attributeFormSchema = z.object({
  name: z.string().min(1, "Attribute name is required"),
});

const optionFormSchema = z.object({
  name: z.string().min(1, "Option name is required"),
  colorCode: z.string().optional(),
  imageUrl: z.string().optional(),
});

const generateVariationsFormSchema = z.object({
  barcodePrefix: z.string().optional(),
});

const variationFormSchema = z.object({
  barcode: z.string().min(1, "Barcode is required"),
  weight: z.string().optional(),
  weightUnit: z.enum(["GRAM", "MILLILITER", "PIECE"]),
  length: z.string().optional(),
  width: z.string().optional(),
  height: z.string().optional(),
});

export default function ProductVariationsTab({
  productId,
  hasVariations,
  onToggleVariations,
}: ProductVariationsTabProps) {
  const [attributes, setAttributes] = useState<ProductAttribute[]>([]);
  const [variations, setVariations] = useState<ProductVariation[]>([]);
  const [activeTab, setActiveTab] = useState("attributes");
  const [isAddingAttribute, setIsAddingAttribute] = useState(false);
  const [isAddingOption, setIsAddingOption] = useState(false);
  const [selectedAttributeId, setSelectedAttributeId] = useState<number | null>(
    null
  );
  const [isGeneratingVariations, setIsGeneratingVariations] = useState(false);
  const [isEditingVariation, setIsEditingVariation] = useState(false);
  const [selectedVariation, setSelectedVariation] =
    useState<ProductVariation | null>(null);
  const [variationImages, setVariationImages] = useState<any[]>([]);
  const [optionIconImage, setOptionIconImage] = useState<any[]>([]);

  // Initialize forms
  const attributeForm = useForm<z.infer<typeof attributeFormSchema>>({
    resolver: zodResolver(attributeFormSchema),
    defaultValues: {
      name: "",
    },
  });

  const optionForm = useForm<z.infer<typeof optionFormSchema>>({
    resolver: zodResolver(optionFormSchema),
    defaultValues: {
      name: "",
      colorCode: "",
      imageUrl: "",
    },
  });

  const generateVariationsForm = useForm<
    z.infer<typeof generateVariationsFormSchema>
  >({
    resolver: zodResolver(generateVariationsFormSchema),
    defaultValues: {
      barcodePrefix: "",
    },
  });

  const variationForm = useForm<z.infer<typeof variationFormSchema>>({
    resolver: zodResolver(variationFormSchema),
    defaultValues: {
      barcode: "",
      weight: "",
      weightUnit: "GRAM",
      length: "",
      width: "",
      height: "",
    },
  });

  // Fetch attributes and variations when component mounts or productId changes
  useEffect(() => {
    if (productId && hasVariations) {
      fetchAttributes();
      fetchVariations();
    }
  }, [productId, hasVariations]);

  // Fetch product attributes
  const fetchAttributes = async () => {
    try {
      const response = await apiService.get(
        `admin/products/${productId}/attributes`
      );
      setAttributes(response.data);
    } catch (error) {
      toast.error("Error", {
        description: "Failed to fetch product attributes",
      });
    }
  };

  // Fetch product variations
  const fetchVariations = async () => {
    try {
      const response = await apiService.get(
        `admin/products/${productId}/variations`
      );
      setVariations(response.data);
    } catch (error) {
      toast.error("Error", {
        description: "Failed to fetch product variations",
      });
    }
  };

  // Handle variations toggle
  const handleToggleVariations = async (checked: boolean) => {
    onToggleVariations(checked);
  };

  // Add a new attribute
  const handleAddAttribute = async (
    data: z.infer<typeof attributeFormSchema>
  ) => {
    try {
      await apiService.post(`admin/products/${productId}/attributes`, {
        name: data.name,
      });

      toast.success("Success", {
        description: "Attribute added successfully",
      });

      setIsAddingAttribute(false);
      attributeForm.reset();
      fetchAttributes();
    } catch (error) {
      toast.error("Error", {
        description: "Failed to add attribute",
      });
    }
  };

  // Add a new option to an attribute
  const handleAddOption = async (data: z.infer<typeof optionFormSchema>) => {
    if (!selectedAttributeId) return;

    try {
      // Handle icon upload if provided
      let iconUrl = null;

      if (
        optionIconImage &&
        optionIconImage.length > 0 &&
        optionIconImage[0].file
      ) {
        // Upload the icon image
        const formData = new FormData();
        formData.append("file", optionIconImage[0].file);

        const response = await apiService.post("admin/media/upload", formData, {
          ContentType: "multipart/form-data",
        });

        if (response.data?.url) {
          iconUrl = response.data.url;
        }
      }

      await apiService.post(`admin/products/attributes/options`, {
        attributeId: selectedAttributeId,
        name: data.name,
        colorCode: data.colorCode || null,
        imageUrl: data.imageUrl || null,
        iconUrl: iconUrl,
      });

      toast.success("Success", {
        description: "Option added successfully",
      });

      setIsAddingOption(false);
      optionForm.reset();
      setOptionIconImage([]);
      fetchAttributes();
    } catch (error) {
      toast.error("Error", {
        description: "Failed to add option",
      });
    }
  };

  // State for tracking the generation process
  const [isSubmittingGeneration, setIsSubmittingGeneration] = useState(false);

  // Generate variations
  const handleGenerateVariations = async (
    data: z.infer<typeof generateVariationsFormSchema>
  ) => {
    try {
      setIsSubmittingGeneration(true);

      await apiService.post(`admin/products/${productId}/generate-variations`, {
        barcodePrefix: data.barcodePrefix || undefined,
      });

      toast.success("Success", {
        description: "Variations generated successfully",
      });

      fetchVariations();
      setActiveTab("variations");
      setIsGeneratingVariations(false); // Close the dialog after success
    } catch (error) {
      toast.error("Error", {
        description: "Failed to generate variations",
      });
      // Keep the dialog open on error
    } finally {
      setIsSubmittingGeneration(false);
    }
  };

  // Edit a variation
  const handleEditVariation = async (
    data: z.infer<typeof variationFormSchema>
  ) => {
    if (!selectedVariation) return;

    try {
      // Prepare media items if there are any
      const mediaItems = [];

      if (variationImages && variationImages.length > 0) {
        for (const img of variationImages) {
          if (img.file) {
            // This is a new file that needs to be uploaded
            const formData = new FormData();
            formData.append("file", img.file);

            const response = await apiService.post(
              "admin/media/upload",
              formData,
              { ContentType: "multipart/form-data" }
            );

            if (response.data?.url) {
              mediaItems.push({
                type: "IMAGE",
                url: response.data.url,
              });
            }
          } else if (img.url) {
            // This is an existing image
            mediaItems.push({
              type: "IMAGE",
              url: img.url,
            });
          }
        }
      }

      // Update the variation
      await apiService.patch(
        `admin/products/variations/${selectedVariation.id}`,
        {
          barcode: data.barcode,
          media: mediaItems.length > 0 ? mediaItems : undefined,
        }
      );

      toast.success("Success", {
        description: "Variation updated successfully",
      });

      setIsEditingVariation(false);
      variationForm.reset();
      setVariationImages([]);
      fetchVariations();
    } catch (error) {
      toast.error("Error", {
        description: "Failed to update variation",
      });
    }
  };

  // Delete a variation
  const handleDeleteVariation = async (variationId: number) => {
    if (
      !confirm(
        "Are you sure you want to delete this variation? You can click 'Regenerate Variations' to recreate it if needed."
      )
    ) {
      return;
    }

    try {
      await apiService.delete(`admin/products/variations/${variationId}`);

      toast.success("Success", {
        description:
          "Variation deleted successfully. You can click 'Regenerate Variations' to recreate it if needed.",
      });

      fetchVariations();
    } catch (error) {
      toast.error("Error", {
        description: "Failed to delete variation",
      });
    }
  };

  // Delete an attribute
  const handleDeleteAttribute = async (attributeId: number) => {
    if (
      !confirm(
        "Are you sure you want to delete this attribute? This will delete all options and variations associated with it. You will need to regenerate variations after this action."
      )
    ) {
      return;
    }

    try {
      await apiService.delete(`admin/products/attributes/${attributeId}`);

      toast.success("Success", {
        description:
          "Attribute and related options/variations deleted. Click 'Regenerate Variations' to create new variations based on remaining attributes.",
      });

      fetchAttributes();
      fetchVariations();
    } catch (error) {
      toast.error("Error", {
        description: "Failed to delete attribute",
      });
    }
  };

  // Delete an option
  const handleDeleteOption = async (optionId: number) => {
    if (
      !confirm(
        "Are you sure you want to delete this option? This will delete all product variations that use this option. You will need to regenerate variations after this action."
      )
    ) {
      return;
    }

    try {
      await apiService.delete(`admin/products/attributes/options/${optionId}`);

      toast.success("Success", {
        description:
          "Option and related variations deleted. Click 'Regenerate Variations' to create new variations based on remaining options.",
      });

      fetchAttributes();
      fetchVariations();
    } catch (error) {
      toast.error("Error", {
        description: "Failed to delete option",
      });
    }
  };

  return (
    <Card className="mt-6 product-variations-tab">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Product Variations</CardTitle>
          <div className="flex items-center space-x-2">
            <Switch
              checked={hasVariations}
              onCheckedChange={handleToggleVariations}
              id="has-variations"
            />
            <Label htmlFor="has-variations">Enable Variations</Label>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {hasVariations ? (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="attributes">Attributes & Options</TabsTrigger>
              <TabsTrigger value="variations">Variations</TabsTrigger>
            </TabsList>

            <TabsContent value="attributes">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Product Attributes</h3>
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsAddingAttribute(true);
                    }}
                    type="button"
                  >
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Attribute
                  </Button>
                </div>

                {attributes.length === 0 ? (
                  <p className="text-muted-foreground">
                    No attributes defined. Add attributes to create variations.
                  </p>
                ) : (
                  <div className="space-y-6">
                    {attributes.map((attribute) => (
                      <div key={attribute.id} className="border rounded-md p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="font-medium">{attribute.name}</h4>
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedAttributeId(attribute.id);
                                setIsAddingOption(true);
                              }}
                            >
                              <PlusCircle className="mr-2 h-4 w-4" /> Add Option
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteAttribute(attribute.id);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {attribute.options.length === 0 ? (
                          <p className="text-sm text-muted-foreground">
                            No options defined
                          </p>
                        ) : (
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                            {attribute.options.map((option) => (
                              <div
                                key={option.id}
                                className="flex items-center justify-between border rounded-md p-2"
                              >
                                <div className="flex items-center space-x-2">
                                  {option.colorCode && (
                                    <div
                                      className="w-4 h-4 rounded-full"
                                      style={{
                                        backgroundColor: option.colorCode,
                                      }}
                                    />
                                  )}
                                  {option.iconUrl && (
                                    <img
                                      src={option.iconUrl}
                                      alt={`${option.name} icon`}
                                      className="w-4 h-4 object-contain"
                                    />
                                  )}
                                  <span>{option.name}</span>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  type="button"
                                  className="h-6 w-6"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteOption(option.id);
                                  }}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}

                    <div className="mt-6">
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          setIsGeneratingVariations(true);
                        }}
                        disabled={attributes.some(
                          (attr) => attr.options.length === 0
                        )}
                        type="button"
                        title="Generate variations based on current attributes and options. This will preserve existing variations and create any missing ones."
                      >
                        Generate Variations
                      </Button>
                      {attributes.some((attr) => attr.options.length === 0) && (
                        <p className="text-sm text-muted-foreground mt-2">
                          All attributes must have at least one option to
                          generate variations.
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="variations">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">Product Variations</h3>
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsGeneratingVariations(true);
                    }}
                    disabled={attributes.some(
                      (attr) => attr.options.length === 0
                    )}
                    type="button"
                    title="Regenerate variations based on current attributes and options. This will preserve existing variations and create any missing ones."
                  >
                    Regenerate Variations
                  </Button>
                </div>

                {variations.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground mb-4">
                      No variations generated yet.
                    </p>
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsGeneratingVariations(true);
                      }}
                      disabled={attributes.some(
                        (attr) => attr.options.length === 0
                      )}
                      type="button"
                      title="Generate variations based on current attributes and options. This will preserve existing variations and create any missing ones."
                    >
                      Generate Variations
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Barcode</TableHead>
                        <TableHead>Attributes</TableHead>
                        <TableHead>Images</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {variations.map((variation) => (
                        <TableRow key={variation.id}>
                          <TableCell>{variation.barcode}</TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {variation.options.map((optionMapping) => (
                                <div
                                  key={optionMapping.option.id}
                                  className="text-xs bg-muted px-2 py-1 rounded-md flex items-center gap-1"
                                >
                                  {optionMapping.option.colorCode && (
                                    <div
                                      className="w-3 h-3 rounded-full"
                                      style={{
                                        backgroundColor:
                                          optionMapping.option.colorCode,
                                      }}
                                    />
                                  )}
                                  {optionMapping.option.iconUrl && (
                                    <img
                                      src={optionMapping.option.iconUrl}
                                      alt={`${optionMapping.option.name} icon`}
                                      className="w-3 h-3 object-contain"
                                    />
                                  )}
                                  {optionMapping.option.attribute.name}:{" "}
                                  {optionMapping.option.name}
                                </div>
                              ))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              {variation.media
                                .slice(0, 3)
                                .map((media, index) => (
                                  <img
                                    key={index}
                                    src={media.url}
                                    alt={`Variation ${variation.id}`}
                                    className="w-10 h-10 object-cover rounded-md"
                                  />
                                ))}
                              {variation.media.length > 3 && (
                                <div className="w-10 h-10 bg-muted rounded-md flex items-center justify-center text-xs">
                                  +{variation.media.length - 3}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="icon"
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedVariation(variation);
                                variationForm.setValue(
                                  "barcode",
                                  variation.barcode
                                );
                                setVariationImages(
                                  variation.media.map((m) => ({ url: m.url }))
                                );
                                setIsEditingVariation(true);
                              }}
                            >
                              Edit
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteVariation(variation.id);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </div>
            </TabsContent>
          </Tabs>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              Enable variations to create different versions of this product
              (e.g., different sizes, colors).
            </p>
          </div>
        )}
      </CardContent>

      {/* Add Attribute Dialog */}
      <Dialog open={isAddingAttribute} onOpenChange={setIsAddingAttribute}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Add Attribute</DialogTitle>
            <DialogDescription>
              Add a new attribute like Size, Color, etc.
            </DialogDescription>
          </DialogHeader>

          <Form {...attributeForm}>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                attributeForm.handleSubmit(handleAddAttribute)(e);
              }}
              className="space-y-4"
            >
              <FormField
                control={attributeForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Attribute Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Size, Color" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddingAttribute(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Add Attribute</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Add Option Dialog */}
      <Dialog
        open={isAddingOption}
        onOpenChange={(open) => {
          setIsAddingOption(open);
          if (!open) {
            optionForm.reset();
            setOptionIconImage([]);
          }
        }}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Add Option</DialogTitle>
            <DialogDescription>
              Add a new option to the attribute.
            </DialogDescription>
          </DialogHeader>

          <Form {...optionForm}>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                optionForm.handleSubmit(handleAddOption)(e);
              }}
              className="space-y-4"
            >
              <FormField
                control={optionForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Option Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Small, Red" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={optionForm.control}
                name="colorCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Color Code (optional)</FormLabel>
                    <FormControl>
                      <Input type="color" {...field} />
                    </FormControl>
                    <FormDescription>
                      Only applicable for color attributes.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <Label>Icon (optional)</Label>
                <MultipleImageUpload
                  value={optionIconImage}
                  onChange={setOptionIconImage}
                  label="Add Icon"
                  id="option-icon"
                  maxImages={1}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Upload an icon to represent this option (e.g., a size chart
                  icon for size options)
                </p>
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsAddingOption(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Add Option</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Generate Variations Dialog */}
      <Dialog
        open={isGeneratingVariations}
        onOpenChange={(open) => {
          setIsGeneratingVariations(open);
          if (!open) generateVariationsForm.reset();
        }}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Generate Variations</DialogTitle>
            <DialogDescription>
              This will generate all possible combinations of your attributes
              and options. Existing variations will be preserved and any missing
              combinations will be created as new variations. You can optionally
              add a barcode prefix to help identify new variations.
            </DialogDescription>
          </DialogHeader>

          <Form {...generateVariationsForm}>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                generateVariationsForm.handleSubmit(handleGenerateVariations)(
                  e
                );
              }}
              className="space-y-4"
            >
              <FormField
                control={generateVariationsForm.control}
                name="barcodePrefix"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Barcode Prefix (optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., PROD-" {...field} />
                    </FormControl>
                    <FormDescription>
                      {
                        "A prefix to add to all generated barcodes. Don't worry, barcodes can be edited later."
                      }
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsGeneratingVariations(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmittingGeneration}>
                  {isSubmittingGeneration
                    ? "Generating..."
                    : "Generate Variations"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Variation Dialog */}
      <Dialog open={isEditingVariation} onOpenChange={setIsEditingVariation}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Variation</DialogTitle>
            <DialogDescription>
              Update the details for this variation.
            </DialogDescription>
          </DialogHeader>

          <Form {...variationForm}>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                e.stopPropagation();
                variationForm.handleSubmit(handleEditVariation)(e);
              }}
              className="space-y-4"
            >
              <FormField
                control={variationForm.control}
                name="barcode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Barcode</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <Label>Variation Images</Label>
                <MultipleImageUpload
                  value={variationImages}
                  onChange={setVariationImages}
                  label="Add Variation Images"
                  id="variation-images"
                  maxImages={5}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditingVariation(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Update Variation</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
