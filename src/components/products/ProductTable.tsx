"use client";

import { Product } from "@/frontend-types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Button } from "../ui/button";
import { MoreHorizontal } from "lucide-react";
import { formatDate } from "@/lib/utils";
import { useRouter } from "next/navigation";

export interface ProductTableProps {
  products: Product[];
  onDelete: (id: number) => void;
}

export default function ProductTable({
  products,
  onDelete,
}: ProductTableProps) {
  const router = useRouter();

  return (
    <>
      <div className="mt-10">
        <Table>
          {/* <TableCaption>List of products</TableCaption> */}
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Barcode</TableHead>
              <TableHead>Slug</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>GST</TableHead>
              <TableHead>Discount</TableHead>
              <TableHead className="text-right">Created At</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.map((product) => {
              return (
                <TableRow key={product.id}>
                  <TableCell>{product.name}</TableCell>
                  <TableHead>{product.barcode}</TableHead>
                  <TableCell>{product.slug}</TableCell>
                  <TableCell>{product.category.name}</TableCell>
                  <TableCell>{product.gstPercentage}%</TableCell>
                  <TableCell>
                    {product.discountValue
                      ? `${product.discountValue}${
                          product.discountType === "PERCENTAGE" ? "%" : ""
                        }`
                      : ""}
                  </TableCell>
                  <TableCell className="text-right">
                    {formatDate(product.createdAt)}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => {
                            router.push(`/products/${product.id}/edit`);
                          }}
                        >
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-500"
                          onClick={() => {
                            onDelete(product.id);
                          }}
                        >
                          Delete
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => {
                            router.push(`/products/${product.id}/translate`);
                          }}
                        >
                          Translate
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </>
  );
}
