"use client";

import { Edit, Plus, PlusCircle, Trash2 } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { apiService } from "../../api";
import { toast } from "sonner";
import { ProductPolicy, ProductPolicyType } from "../../frontend-types";
import { MultipleImageUpload } from "../multiple-image-upload";
import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Badge } from "../ui/badge";

interface ProductPolicyEditorProps {
  productId?: number;
  policies: ProductPolicy[];
  onChange: (policies: ProductPolicy[]) => void;
}

export default function ProductPolicyEditor({
  policies,
  onChange,
}: ProductPolicyEditorProps) {
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isCreateTypeOpen, setIsCreateTypeOpen] = useState(false);
  const [selectedPolicyTypeId, setSelectedPolicyTypeId] = useState<
    number | null
  >(null);
  const [policyDescription, setPolicyDescription] = useState<string>("");
  const [editingPolicyIndex, setEditingPolicyIndex] = useState<number | null>(
    null
  );

  const [newPolicyType, setNewPolicyType] = useState({ name: "" });
  const [keyValuePairs, setKeyValuePairs] = useState<
    { key: string; value: string }[]
  >([{ key: "", value: "" }]);
  const [typeIconImage, setTypeIconImage] = useState<
    { url: string; file?: File }[]
  >([]);
  const [policyTypes, setPolicyTypes] = useState<ProductPolicyType[]>([]);
  const [_isLoading, setIsLoading] = useState(true);

  // Fetch policy types
  useEffect(() => {
    fetchPolicyTypes();
  }, []);

  const fetchPolicyTypes = async () => {
    try {
      setIsLoading(true);
      const response = await apiService.get("admin/product-policy/type");
      setPolicyTypes(response.data);
    } catch (error) {
      toast.error("Error", {
        description: "Failed to fetch policy types",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddPolicy = () => {
    if (!selectedPolicyTypeId) {
      toast.error("Error", {
        description: "Please select a policy type",
      });
      return;
    }

    // Check if this policy type is already added
    const existingPolicy = policies.find(
      (p) => p.productPolicyTypeId === selectedPolicyTypeId
    );
    if (existingPolicy) {
      toast.error("Error", {
        description: "This policy type is already added to the product",
      });
      return;
    }

    // Convert key-value pairs to details object
    const details: Record<string, string> = {};
    keyValuePairs.forEach((pair) => {
      if (pair.key.trim() !== "") {
        details[pair.key.trim()] = pair.value;
      }
    });

    const newPolicy: ProductPolicy = {
      id: 0,
      productPolicyTypeId: selectedPolicyTypeId,
      description: policyDescription,
      details,
      productId: 0,
      productPolicyType: policyTypes.find(
        (pt) => pt.id === selectedPolicyTypeId
      )!,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    onChange([...policies, newPolicy]);
    setIsAddOpen(false);
    setSelectedPolicyTypeId(null);
    setPolicyDescription("");
    setKeyValuePairs([{ key: "", value: "" }]);
  };

  const handleEditPolicy = () => {
    if (editingPolicyIndex === null) return;

    // Convert key-value pairs to details object
    const details: Record<string, string> = {};
    keyValuePairs.forEach((pair) => {
      if (pair.key.trim() !== "") {
        details[pair.key.trim()] = pair.value;
      }
    });

    const updatedPolicies = [...policies];
    updatedPolicies[editingPolicyIndex] = {
      ...updatedPolicies[editingPolicyIndex],
      description: policyDescription,
      details,
      updatedAt: new Date(),
    };

    onChange(updatedPolicies);
    setIsEditOpen(false);
    setEditingPolicyIndex(null);
    setPolicyDescription("");
    setKeyValuePairs([{ key: "", value: "" }]);
  };

  const openEditDialog = (index: number) => {
    const policy = policies[index];
    setEditingPolicyIndex(index);
    setPolicyDescription(policy.description || "");

    // Convert details object to key-value pairs array
    const pairs = Object.entries(policy.details || {}).map(([key, value]) => ({
      key,
      value: value as string,
    }));

    // Ensure there's at least one empty pair if no details exist
    setKeyValuePairs(pairs.length > 0 ? pairs : [{ key: "", value: "" }]);
    setIsEditOpen(true);
  };

  const handleRemovePolicy = (index: number) => {
    const updatedPolicies = [...policies];
    updatedPolicies.splice(index, 1);
    onChange(updatedPolicies);
  };

  const handleAddKeyValuePair = () => {
    setKeyValuePairs([...keyValuePairs, { key: "", value: "" }]);
  };

  const handleKeyValueChange = (
    index: number,
    field: "key" | "value",
    value: string
  ) => {
    const updatedPairs = [...keyValuePairs];
    updatedPairs[index][field] = value;
    setKeyValuePairs(updatedPairs);
  };

  const handleRemoveKeyValuePair = (index: number) => {
    if (keyValuePairs.length > 1) {
      const updatedPairs = [...keyValuePairs];
      updatedPairs.splice(index, 1);
      setKeyValuePairs(updatedPairs);
    }
  };

  const handleCreatePolicyType = async () => {
    if (!newPolicyType.name) {
      toast.error("Error", {
        description: "Please enter a name for the policy type",
      });
      return;
    }

    try {
      // Handle icon upload if provided
      let iconUrl = "";

      if (typeIconImage.length > 0 && typeIconImage[0].file) {
        // Upload the icon image
        const uploadFormData = new FormData();
        uploadFormData.append("file", typeIconImage[0].file);

        const response = await apiService.post(
          "admin/media/upload",
          uploadFormData,
          { ContentType: "multipart/form-data" }
        );

        if (response.data?.url) {
          iconUrl = response.data.url;
        }
      }

      const response = await apiService.post("admin/product-policy/type", {
        name: newPolicyType.name,
        icon: iconUrl,
      });

      toast.success("Success", {
        description: "Policy type created successfully",
      });

      setIsCreateTypeOpen(false);
      setNewPolicyType({ name: "" });
      setTypeIconImage([]);

      // Refetch policy types
      await fetchPolicyTypes();

      // Set the newly created policy type as selected
      const newPolicyTypeId = response.data.id;
      setSelectedPolicyTypeId(newPolicyTypeId);
    } catch (error) {
      console.error("Error creating policy type:", error);
      toast.error("Error", {
        description: "Failed to create policy type",
      });
    }
  };

  const getPolicyTypeName = (typeId: number) => {
    const policyType = policyTypes?.find((type) => type.id === typeId);
    return policyType?.name || "Unknown";
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Product Policies</h3>
        <Dialog open={isAddOpen} onOpenChange={setIsAddOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Policy
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add Product Policy</DialogTitle>
              <DialogDescription>
                Select a policy type and add details for this product.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="flex items-center gap-2">
                <div className="flex-1">
                  <Label htmlFor="policyType">Policy Type</Label>
                  <Select
                    value={selectedPolicyTypeId?.toString() || ""}
                    onValueChange={(value) =>
                      setSelectedPolicyTypeId(Number(value))
                    }
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a policy type" />
                    </SelectTrigger>
                    <SelectContent>
                      {policyTypes
                        ?.filter(
                          (type) =>
                            !policies.some(
                              (p) => p.productPolicyTypeId === type.id
                            )
                        )
                        .map((type) => (
                          <SelectItem key={type.id} value={type.id.toString()}>
                            <div className="flex items-center gap-2">
                              {type.icon && (
                                <div className="relative w-4 h-4">
                                  <Image
                                    src={type.icon}
                                    alt={`${type.name} icon`}
                                    fill
                                    className="object-contain"
                                    unoptimized={true}
                                  />
                                </div>
                              )}
                              {type.name}
                            </div>
                          </SelectItem>
                        ))}
                      {policyTypes?.filter(
                        (type) =>
                          !policies.some(
                            (p) => p.productPolicyTypeId === type.id
                          )
                      ).length === 0 && (
                        <div className="p-2 text-sm text-muted-foreground text-center">
                          All policy types have been added
                        </div>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <div className="mt-6">
                  <Dialog
                    open={isCreateTypeOpen}
                    onOpenChange={setIsCreateTypeOpen}
                  >
                    <DialogTrigger asChild>
                      <Button variant="outline" size="icon">
                        <PlusCircle className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create New Policy Type</DialogTitle>
                        <DialogDescription>
                          Add a new policy type that can be applied to products.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor="name" className="text-right">
                            Name
                          </Label>
                          <Input
                            id="name"
                            value={newPolicyType.name}
                            onChange={(e) =>
                              setNewPolicyType({
                                ...newPolicyType,
                                name: e.target.value,
                              })
                            }
                            className="col-span-3"
                          />
                        </div>

                        <div className="grid grid-cols-4 items-start gap-4 pt-2">
                          <Label className="text-right pt-2">Upload Icon</Label>
                          <div className="col-span-3">
                            <MultipleImageUpload
                              value={typeIconImage}
                              onChange={setTypeIconImage}
                              label="Add Icon"
                              id="policy-type-icon"
                              maxImages={1}
                            />
                            <p className="text-sm text-muted-foreground mt-1">
                              Upload an icon for this policy type
                            </p>
                          </div>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button onClick={handleCreatePolicyType}>Create</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
              <div>
                <Label htmlFor="policyDescription">Description</Label>
                <Input
                  id="policyDescription"
                  placeholder="Enter policy description"
                  value={policyDescription}
                  onChange={(e) => setPolicyDescription(e.target.value)}
                  className="mt-1"
                />
              </div>
              <div>
                <Label>Policy Details</Label>
                <div className="space-y-2 mt-2">
                  {keyValuePairs.map((pair, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        placeholder="Key"
                        value={pair.key}
                        onChange={(e) =>
                          handleKeyValueChange(index, "key", e.target.value)
                        }
                        className="flex-1"
                      />
                      <Input
                        placeholder="Value"
                        value={pair.value}
                        onChange={(e) =>
                          handleKeyValueChange(index, "value", e.target.value)
                        }
                        className="flex-1"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleRemoveKeyValuePair(index)}
                        disabled={keyValuePairs.length === 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAddKeyValuePair}
                    className="mt-2"
                  >
                    <Plus className="mr-2 h-4 w-4" /> Add Field
                  </Button>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleAddPolicy}>Add Policy</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Edit Policy Dialog */}
      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Product Policy</DialogTitle>
            <DialogDescription>
              Update the details for this policy.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div>
              <Label htmlFor="editPolicyDescription">Description</Label>
              <Input
                id="editPolicyDescription"
                placeholder="Enter policy description"
                value={policyDescription}
                onChange={(e) => setPolicyDescription(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label>Policy Details</Label>
              <div className="space-y-2 mt-2">
                {keyValuePairs.map((pair, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      placeholder="Key"
                      value={pair.key}
                      onChange={(e) =>
                        handleKeyValueChange(index, "key", e.target.value)
                      }
                      className="flex-1"
                    />
                    <Input
                      placeholder="Value"
                      value={pair.value}
                      onChange={(e) =>
                        handleKeyValueChange(index, "value", e.target.value)
                      }
                      className="flex-1"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleRemoveKeyValuePair(index)}
                      disabled={keyValuePairs.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAddKeyValuePair}
                  className="mt-2"
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Field
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleEditPolicy}>Update Policy</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {policies.length === 0 ? (
        <div className="text-center p-4 border rounded-md text-muted-foreground">
          {`No policies added yet. Click "Add Policy" to add one.`}
        </div>
      ) : (
        <div className="space-y-4">
          {policies.map((policy, index) => (
            <Card key={index} className="overflow-hidden">
              <CardHeader className="pb-2 bg-muted/30">
                <CardTitle className="text-md flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    {policyTypes?.find(
                      (type) => type.id === policy.productPolicyTypeId
                    )?.icon && (
                      <div className="relative w-5 h-5">
                        <Image
                          src={
                            policyTypes.find(
                              (type) => type.id === policy.productPolicyTypeId
                            )?.icon || ""
                          }
                          alt={`${getPolicyTypeName(
                            policy.productPolicyTypeId
                          )} icon`}
                          fill
                          className="object-contain"
                          unoptimized={true}
                        />
                      </div>
                    )}
                    {getPolicyTypeName(policy.productPolicyTypeId)}
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        openEditDialog(index);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        handleRemovePolicy(index);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                {policy.description && (
                  <div className="mb-3 text-sm bg-muted/20 p-2 rounded-md">
                    {policy.description}
                  </div>
                )}
                {Object.keys(policy.details || {}).length > 0 ? (
                  <div className="grid grid-cols-1 gap-2">
                    {Object.entries(policy.details || {}).map(
                      ([key, value]) => (
                        <div
                          key={key}
                          className="flex justify-between items-center border-b pb-2"
                        >
                          <Badge variant="outline" className="font-medium">
                            {key}
                          </Badge>
                          <span className="text-sm">{value as string}</span>
                        </div>
                      )
                    )}
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground italic">
                    No details added for this policy.
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
