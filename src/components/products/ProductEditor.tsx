"use client";

import { apiService } from "@/api";
import {
  AmountType,
  Category,
  PaginatedResponse,
  Product,
  ProductPolicy,
  WeightUnit,
} from "@/frontend-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft, ExternalLink, Plus, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";
import ItemPicker from "../ItemPicker";
import { MultipleImageUpload } from "../multiple-image-upload";
import { ImageUpload } from "../image-upload";
import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Input } from "../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Separator } from "../ui/separator";
import { Textarea } from "../ui/textarea";
import { Switch } from "../ui/switch";
import { toast } from "sonner";
import ProductVariationsTab from "./ProductVariationsTab";
import ProductPolicyEditor from "../products/ProductPolicyEditor";

interface ImageItem {
  url: string;
  file?: File;
}

// Define a schema for key-value pairs
const keyValuePairSchema = z.object({
  id: z.string(),
  key: z.string().min(1, "Key is required"),
  value: z.string().min(1, "Value is required"),
});

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  barcode: z.string().min(1, "Barcode is required"),
  description: z.string().min(1, "Description is required"),
  slug: z.string().min(1, "Slug is required"),
  categoryId: z.string().min(1, "Category is required"),
  gstPercentage: z.string().min(1, "GST percentage is required"),
  weight: z.string().optional(),
  weightUnit: z.enum(WeightUnit),
  length: z.string().optional(),
  width: z.string().optional(),
  height: z.string().optional(),
  discountValue: z.string().optional(),
  discountType: z.enum(AmountType).optional(),
  hasVariations: z.boolean(),
  active: z.boolean(),
  maxCartQuantity: z.string().optional(),
  highlights: z.array(keyValuePairSchema),
  information: z.array(keyValuePairSchema),
});

type FormValues = z.infer<typeof formSchema>;
type KeyValue = z.infer<typeof keyValuePairSchema>;

export interface ProductEditorProps {
  product?: Product;
  isLoading: boolean;
  onSubmit: (
    data: Omit<FormValues, "highlights" | "information"> & {
      productPolicies: ProductPolicy[];
      highlights: Record<string, string>;
      information: Record<string, string>;
      files: { images?: File[] };
      thumbnail?: ImageItem;
      currentImages: ImageItem[];
      relatedProductIds?: number[];
    }
  ) => void;
}

export function ProductEditor({
  product,
  isLoading,
  onSubmit,
}: ProductEditorProps) {
  const router = useRouter();

  const [selectedCategory, setSelectedCategory] = useState<
    Category | undefined
  >(product?.category);

  const [productImages, setProductImages] = useState<ImageItem[]>(
    product?.media ?? []
  );

  const [productThumbnail, setProductThumbnail] = useState<ImageItem | null>(
    product?.thumbnail ?? null
  );

  const [productPolicies, setProductPolicies] = useState<ProductPolicy[]>(
    product?.productPolicies ?? []
  );

  const [selectedRelatedProducts, setSelectedRelatedProducts] = useState<
    Product[]
  >(product?.relatedProducts ?? []);

  // Function to generate slug from name
  const generateSlug = (name: string): string => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters except spaces and hyphens
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with a single hyphen
      .trim();
  };

  // Helper function to convert JSON object to array of key-value pairs
  const jsonToKeyValueArray = (
    json: Record<string, string | string[]> | null | undefined
  ): KeyValue[] => {
    if (!json) return [];
    return Object.entries(json).map(([key, value], index) => ({
      id: `kv-${index}`,
      key,
      value: Array.isArray(value) ? value.join(", ") : String(value),
    }));
  };

  // Helper function to convert array of key-value pairs to JSON object
  const keyValueArrayToJson = (array: { key: string; value: string }[]) => {
    const result: Record<string, string> = {};
    array.forEach(({ key, value }) => {
      if (key.trim()) {
        result[key.trim()] = value;
      }
    });
    return result;
  };

  // Initialize form with default values or product data
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: product?.name ?? "",
      barcode: product?.barcode ?? "",
      description: product?.description || "",
      slug: product?.slug ?? "",
      categoryId: product?.categoryId?.toString() ?? "",
      gstPercentage: product?.gstPercentage?.toString() ?? "",
      weight: product?.weight?.toString() ?? "",
      weightUnit: product?.weightUnit ?? "GRAM",
      length: product?.length?.toString() ?? "",
      width: product?.width?.toString() ?? "",
      height: product?.height?.toString() ?? "",
      discountValue: product?.discountValue?.toString() ?? "",
      discountType: product?.discountType ?? undefined,
      hasVariations: product?.hasVariations ?? false,
      active: product?.active ?? false,
      maxCartQuantity: product?.maxCartQuantity?.toString() ?? "",
      highlights: jsonToKeyValueArray(product?.highlights),
      information: jsonToKeyValueArray(product?.information),
    },
  });

  // Set up field arrays for highlights and information
  const {
    fields: highlightFields,
    append: appendHighlight,
    remove: removeHighlight,
  } = useFieldArray({ control: form.control, name: "highlights" });

  const {
    fields: informationFields,
    append: appendInformation,
    remove: removeInformation,
  } = useFieldArray({ control: form.control, name: "information" });

  // Watch for name changes to update slug automatically
  const productName = form.watch("name");
  const [slugManuallyEdited, setSlugManuallyEdited] = useState(false);

  useEffect(() => {
    // Auto-generate slug if name changes and slug hasn't been manually edited
    if (productName && !slugManuallyEdited) {
      const slug = generateSlug(productName);
      form.setValue("slug", slug);
    }
  }, [productName, form, slugManuallyEdited]);

  const fetchCategories = async (
    page: number,
    pageSize: number,
    search: string
  ): Promise<PaginatedResponse<Category>> => {
    const resp = await apiService.get("admin/categories", {
      page,
      pageSize,
      search,
      type: "SEGMENT,CATEGORY",
    });
    return resp.data as PaginatedResponse<Category>;
  };

  const fetchProducts = async (
    page: number,
    pageSize: number,
    search: string
  ): Promise<PaginatedResponse<Product>> => {
    const resp = await apiService.get("admin/products", {
      page,
      pageSize,
      search,
    });
    return resp.data as PaginatedResponse<Product>;
  };

  // Handle form submission
  const handleSubmit = async (data: FormValues) => {
    try {
      // Prepare files for upload - ensure productImages is defined
      const imageFiles = productImages
        ? productImages
            .filter((img) => img && img.file)
            .map((img) => img.file as File)
        : [];

      // Convert highlights and information arrays to JSON objects
      const highlightsJson = keyValueArrayToJson(data.highlights);
      const informationJson = keyValueArrayToJson(data.information);

      // Add files to form data
      const formData = {
        ...data,
        highlights: highlightsJson,
        information: informationJson,
        productPolicies: productPolicies,
        files: {
          images: imageFiles,
        },
        thumbnail: productThumbnail ?? undefined,
        currentImages: productImages ?? [],
        relatedProductIds: selectedRelatedProducts.map((p) => p.id),
      };
      console.log("Product policies being sent to parent:", productPolicies);
      onSubmit(formData);
    } catch (error) {
      console.error("Error in form submission:", error);
      toast.error("Error", {
        description: "Failed to submit product data",
      });
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">
            {product ? "Edit Product" : "Add Product"}
          </h1>
        </div>
        {product && (
          <Button
            variant="outline"
            onClick={() => {
              const storefrontUrl = process.env.NEXT_PUBLIC_STOREFRONT_URL;
              if (storefrontUrl) {
                window.open(`${storefrontUrl}/product/${product.id}`, "_blank");
              }
            }}
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            View on Storefront
          </Button>
        )}
      </div>

      <Separator className="my-6" />

      <Form {...form}>
        <form
          onSubmit={(e) => {
            // Check if the event originated from a ProductVariationsTab element
            const target = e.target as HTMLElement;
            const isFromVariationsTab = target.closest(
              ".product-variations-tab"
            );

            if (isFromVariationsTab) {
              e.preventDefault();
              return false;
            }

            return form.handleSubmit(handleSubmit)(e);
          }}
          className="space-y-8"
        >
          {/* Product Variations Tab - Only shown when editing a product */}
          {product && (
            <ProductVariationsTab
              productId={product.id}
              hasVariations={form.watch("hasVariations")}
              onToggleVariations={(hasVariations) => {
                form.setValue("hasVariations", hasVariations);
              }}
            />
          )}

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <FormField
                    control={form.control}
                    name="active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                        <div className="space-y-0.5">
                          <FormLabel>Product Status</FormLabel>
                          <FormDescription>
                            {field.value ? "Published" : "Unpublished"}
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Product name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="barcode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Barcode</FormLabel>
                      <FormControl>
                        <Input placeholder="Product barcode" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="slug"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Slug</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          <Input
                            placeholder="Product slug"
                            {...field}
                            onChange={(e) => {
                              // If user manually edits the slug, mark it as edited
                              if (
                                e.target.value !== generateSlug(productName)
                              ) {
                                setSlugManuallyEdited(true);
                              }
                              field.onChange(e);
                            }}
                            className="flex-1"
                          />
                          {slugManuallyEdited && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const autoSlug = generateSlug(productName);
                                form.setValue("slug", autoSlug);
                                setSlugManuallyEdited(false);
                              }}
                            >
                              Reset
                            </Button>
                          )}
                        </div>
                      </FormControl>
                      <FormDescription>
                        Automatically generated from product name, but can be
                        edited
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Product description"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Category & Pricing</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="categoryId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category (Segment)</FormLabel>
                      <FormDescription>
                        Select the product segment
                      </FormDescription>
                      <FormControl>
                        <ItemPicker<Category>
                          title={"Select Category"}
                          selectionMode="single"
                          columns={[{ accessorKey: "name", header: "Name" }]}
                          fetchItems={fetchCategories}
                          initialSelectedItems={
                            field.value
                              ? [
                                  {
                                    id: parseInt(field.value),
                                    name: selectedCategory?.name ?? "",
                                  } as Category,
                                ]
                              : []
                          }
                          onConfirmSelection={(item) => {
                            field.onChange((item as Category).id.toString());
                            setSelectedCategory(item as Category);
                          }}
                          renderTrigger={() => (
                            <Button variant="outline">
                              {selectedCategory
                                ? selectedCategory.name
                                : "Select Category"}
                            </Button>
                          )}
                        />
                      </FormControl>

                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="gstPercentage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>GST (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          placeholder="GST percentage"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="discountValue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Discount Value</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="Discount value"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="discountType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Discount Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select discount type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="FLAT">FLAT</SelectItem>
                          <SelectItem value="PERCENTAGE">PERCENTAGE</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maxCartQuantity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max Cart Quantity</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          placeholder="Maximum quantity allowed in cart"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Maximum quantity a user can add to cart for this
                        product. Leave empty for no limit.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Dimensions & Weight</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="weight"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Weight</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="any"
                          placeholder="Product weight"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="weightUnit"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Weight Unit</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select weight unit" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {WeightUnit.map((unit) => (
                            <SelectItem key={unit} value={unit}>
                              {unit}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="length"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Length</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="any"
                          placeholder="Product length"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="width"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Width</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="any"
                          placeholder="Product width"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Height</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          step="any"
                          placeholder="Product height"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Highlights Section */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Highlights</CardTitle>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    appendHighlight({
                      id: `kv-${new Date().getTime()}`,
                      key: "",
                      value: "",
                    })
                  }
                >
                  <Plus className="h-4 w-4 mr-1" /> Add Highlight
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  {highlightFields.map((field, index) => (
                    <div key={field.id} className="flex items-center gap-2">
                      <div className="flex-1">
                        <FormField
                          control={form.control}
                          name={`highlights.${index}.key`}
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input placeholder="Name" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="flex-1">
                        <FormField
                          control={form.control}
                          name={`highlights.${index}.value`}
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input placeholder="Value" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removeHighlight(index)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  ))}
                  {highlightFields.length === 0 && (
                    <div className="text-center py-4 text-muted-foreground">
                      {`No highlights added. Click "Add Highlight" to add one.`}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Information Section */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Information</CardTitle>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    appendInformation({
                      id: `kv-${new Date().getTime()}`,
                      key: "",
                      value: "",
                    })
                  }
                >
                  <Plus className="h-4 w-4 mr-1" /> Add Information
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  {informationFields.map((field, index) => (
                    <div key={field.id} className="flex items-center gap-2">
                      <div className="flex-1">
                        <FormField
                          control={form.control}
                          name={`information.${index}.key`}
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input placeholder="Name" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="flex-1">
                        <FormField
                          control={form.control}
                          name={`information.${index}.value`}
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <Input placeholder="Value" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removeInformation(index)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  ))}
                  {informationFields.length === 0 && (
                    <div className="text-center py-4 text-muted-foreground">
                      {`No information added. Click "Add Information" to add
                        one.`}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Product Policies Section */}
            <Card>
              <CardContent>
                <ProductPolicyEditor
                  productId={product?.id}
                  policies={productPolicies}
                  onChange={setProductPolicies}
                />
              </CardContent>
            </Card>

            {/* Related Products Section */}
            <Card>
              <CardHeader>
                <CardTitle>Related Products</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    {`Select products that are related to this product. These will be shown as "frequently bought together" or similar recommendations.`}
                  </p>
                  <ItemPicker<Product>
                    title="Select Related Products"
                    selectionMode="multiple"
                    columns={[
                      { accessorKey: "name", header: "Name" },
                      { accessorKey: "barcode", header: "Barcode" },
                      {
                        accessorKey: "category",
                        header: "Category",
                        render: (item) => item.category?.name || "N/A",
                      },
                    ]}
                    fetchItems={fetchProducts}
                    initialSelectedItems={selectedRelatedProducts}
                    onConfirmSelection={(items) => {
                      const selectedItems = Array.isArray(items)
                        ? items
                        : [items];
                      // Filter out the current product if it's being edited
                      const filteredItems = product
                        ? selectedItems.filter((item) => item.id !== product.id)
                        : selectedItems;
                      setSelectedRelatedProducts(filteredItems);
                    }}
                    renderTrigger={() => (
                      <Button variant="outline">
                        Select Related Products (
                        {selectedRelatedProducts.length})
                      </Button>
                    )}
                    searchPlaceholder="Search products..."
                  />
                </div>

                {selectedRelatedProducts.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">
                      Selected Related Products:
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      {selectedRelatedProducts.map((relatedProduct) => (
                        <div
                          key={relatedProduct.id}
                          className="flex items-center justify-between p-2 border rounded-md bg-muted/50"
                        >
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">
                              {relatedProduct.name}
                            </p>
                            <p className="text-xs text-muted-foreground truncate">
                              {relatedProduct.barcode}
                            </p>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedRelatedProducts((prev) =>
                                prev.filter((p) => p.id !== relatedProduct.id)
                              );
                            }}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="h-full">
              <CardHeader>
                <CardTitle>Product Thumbnail</CardTitle>
              </CardHeader>
              <CardContent>
                <ImageUpload
                  value={productThumbnail?.url ?? ""}
                  onChange={(url, file) => {
                    setProductThumbnail({ file, url });
                  }}
                  label="Add Product Thumbnail"
                  id="product-thumbnail"
                />
              </CardContent>
            </Card>

            <Card className="h-full">
              <CardHeader>
                <CardTitle>Product Gallery</CardTitle>
              </CardHeader>
              <CardContent>
                <MultipleImageUpload
                  value={productImages}
                  onChange={setProductImages}
                  label="Add Product Images"
                  id="product-images"
                  maxImages={10}
                />
              </CardContent>
            </Card>
          </div>

          <Button type="submit" disabled={isLoading}>
            {isLoading
              ? product
                ? "Updating..."
                : "Creating..."
              : product
              ? "Update Product"
              : "Create Product"}
          </Button>
        </form>
      </Form>
    </div>
  );
}
