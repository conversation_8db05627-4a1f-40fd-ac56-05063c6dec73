import { Order } from "@/frontend-types";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Button } from "../ui/button";
import { MoreHorizontal, Eye } from "lucide-react";
import { formatDate, formatCurrency } from "@/lib/utils";
import { Badge } from "../ui/badge";

export interface OrderTableProps {
  orders: Order[];
  onClick: (order: Order) => void;
  onPreview?: (order: Order) => void;
}

export function OrderTable({ orders, onClick, onPreview }: OrderTableProps) {
  return (
    <div className="mt-10">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Order ID</TableHead>
            <TableHead>Preview</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Total Amount</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Created At</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orders.map((order) => (
            <TableRow
              key={order.id}
              onClick={() => onClick(order)}
              className="cursor-pointer"
            >
              <TableCell>#{order.id}</TableCell>
              <TableCell>
                {onPreview && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onPreview(order);
                    }}
                    className="h-8 px-3"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    Preview
                  </Button>
                )}
              </TableCell>
              <TableCell>{order.user.name}</TableCell>
              <TableCell>{formatCurrency(+order.totalAmount)}</TableCell>
              <TableCell>
                <Badge status={order.status}>{order.status}</Badge>
              </TableCell>
              <TableCell className="text-right">
                {formatDate(order.createdAt)}
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onClick(order)}>
                      View
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        navigator.clipboard.writeText(order.id.toString());
                      }}
                    >
                      Copy Order ID
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
