"use client";

import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { formatCurrency } from "@/lib/utils";
import { Order } from "@/frontend-types";
import { Package } from "lucide-react";

interface OrderPreviewDialogProps {
  order: Order | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function OrderPreviewDialog({
  order,
  open,
  onOpenChange,
}: OrderPreviewDialogProps) {
  if (!order) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="text-xl font-semibold">
            Order #{order.id}
          </DialogTitle>
          <div className="flex items-center gap-2">
            <Badge>{order.status}</Badge>
          </div>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Billing Details */}
          <div>
            <h3 className="font-semibold text-base mb-3">Billing details</h3>
            <div className="space-y-2 text-sm">
              <p className="font-medium">{order.user.name}</p>
              <p className="text-muted-foreground">
                {order.address.apartment && `${order.address.apartment}, `}
                {order.address.block && `${order.address.block}, `}
                {order.address.streetName}
              </p>
              <p className="text-muted-foreground">
                {order.address.city}, {order.address.state}{" "}
                {order.address.zipCode}
              </p>
              <div className="pt-2">
                <p className="text-muted-foreground">
                  <span className="font-medium">Email:</span> {order.user.email}
                </p>
                <p className="text-muted-foreground">
                  <span className="font-medium">Phone:</span> {order.user.phone}
                </p>
              </div>
              <div className="pt-2">
                <p className="text-muted-foreground">
                  <span className="font-medium">Payment via:</span> Cash on
                  delivery
                </p>
              </div>
              <div className="pt-2">
                <p className="text-muted-foreground">
                  <span className="font-medium">Coupons:</span>{" "}
                  {order.coupon ? order.coupon.code : "No coupons used"}
                </p>
              </div>
            </div>
          </div>

          {/* Shipping Details */}
          <div>
            <h3 className="font-semibold text-base mb-3">Shipping details</h3>
            <div className="space-y-2 text-sm">
              <p className="font-medium">{order.user.name}</p>
              <p className="text-muted-foreground">
                {order.address.apartment && `${order.address.apartment}, `}
                {order.address.block && `${order.address.block}, `}
                {order.address.streetName}
              </p>
              <p className="text-muted-foreground">
                {order.address.city}, {order.address.state}{" "}
                {order.address.zipCode}
              </p>
              <div className="pt-2">
                <p className="text-muted-foreground">
                  <span className="font-medium">Phone:</span> {order.user.phone}
                </p>
              </div>
              <div className="pt-2">
                <p className="text-muted-foreground">
                  <span className="font-medium">Shipping method:</span> Free
                  shipping
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="mt-6">
          <div className="border rounded-lg">
            <div className="bg-muted/50 px-4 py-3 border-b">
              <div className="grid grid-cols-12 gap-4 text-sm font-medium">
                <div className="col-span-6">Product</div>
                <div className="col-span-2 text-center">Quantity</div>
                <div className="col-span-2 text-right">Total</div>
              </div>
            </div>
            <div className="divide-y">
              {order.items.map((item) => (
                <div key={item.id} className="px-4 py-3">
                  <div className="grid grid-cols-12 gap-4 items-center text-sm">
                    <div className="col-span-6">
                      <div className="flex items-center gap-3">
                        <div className="h-12 w-12 rounded bg-gray-100 flex items-center justify-center overflow-hidden flex-shrink-0">
                          {item.product.thumbnail ? (
                            <img
                              src={item.product.thumbnail.url}
                              alt={item.product.name}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <Package className="h-6 w-6 text-gray-400" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{item.product.name}</p>
                          {item.variation &&
                            item.variation.options &&
                            item.variation.options.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-1">
                                {item.variation.options.map((optionMapping) => (
                                  <span
                                    key={optionMapping.option.id}
                                    className="text-xs text-muted-foreground bg-gray-100 px-2 py-1 rounded"
                                  >
                                    {optionMapping.option.attribute.name}:{" "}
                                    {optionMapping.option.name}
                                  </span>
                                ))}
                              </div>
                            )}
                        </div>
                      </div>
                    </div>
                    <div className="col-span-2 text-center">
                      {item.quantity}
                    </div>
                    <div className="col-span-2 text-right font-medium">
                      {formatCurrency(+item.price)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Pricing Summary */}
        <div className="mt-6 flex justify-end">
          <div className="w-full max-w-sm space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>{formatCurrency(+order.subtotal)}</span>
            </div>
            <div className="flex justify-between">
              <span>Handling Charge:</span>
              <span>{formatCurrency(+order.handlingCharge)}</span>
            </div>
            <div className="flex justify-between">
              <span>Delivery Fee:</span>
              <span>{formatCurrency(+order.deliveryFee)}</span>
            </div>
            {Number(order.couponDiscount) > 0 && (
              <div className="flex justify-between text-green-600">
                <span>Coupon Discount:</span>
                <span>-{formatCurrency(+order.couponDiscount)}</span>
              </div>
            )}
            {Number(order.rewardDiscount) > 0 && (
              <div className="flex justify-between text-green-600">
                <span>Reward Discount:</span>
                <span>-{formatCurrency(+order.rewardDiscount)}</span>
              </div>
            )}
            <div className="border-t pt-2">
              <div className="flex justify-between font-semibold text-base">
                <span>Total:</span>
                <span>{formatCurrency(+order.totalAmount)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-6 flex justify-end gap-2"></div>
      </DialogContent>
    </Dialog>
  );
}
