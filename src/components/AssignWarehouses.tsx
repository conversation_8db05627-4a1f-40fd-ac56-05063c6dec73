import { apiService } from "@/api";
import { Label } from "@/components/ui/label";
import { MultiSelect } from "@/components/ui/multi-select";
import { toast } from "sonner";
import { Warehouse } from "@/frontend-types";
import { useEffect, useState } from "react";

export default function AssignWarehouses({
  selected,
  onChange,
}: {
  selected: number[];
  onChange: (ids: number[]) => void;
}) {
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);

  useEffect(() => {
    async function fetchWarehouses() {
      try {
        const res = await apiService.get("admin/warehouses");
        setWarehouses(res.data.data);
      } catch (err) {
        toast.error("Error loading warehouses", { description: `${err}` });
      }
    }

    fetchWarehouses();
  }, []);

  return (
    <div className="space-y-4">
      <Label>Assigned Warehouses</Label>
      <MultiSelect
        options={warehouses.map((w) => ({
          label: w.name,
          value: w.id,
        }))}
        selected={selected}
        onChange={onChange}
      />
    </div>
  );
}
