import { useState, useRef, ChangeEvent, useEffect } from "react";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { UploadCloud, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface ImageUploadProps {
  value?: string;
  onChange: (url: string, file?: File) => void;
  className?: string;
  label?: string;
  id?: string; // Add unique ID prop
}

export function ImageUpload({
  value,
  onChange,
  className,
  label = "Upload Image",
  id = "image-upload", // Default ID with fallback
}: ImageUploadProps) {
  const [preview, setPreview] = useState<string | null>(value || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Update preview when value changes (for edit mode)
  useEffect(() => {
    if (value) {
      console.log("ImageUpload received value:", value);
      setPreview(value);

      // Log the image URL that will be used
      const imageUrl = value.startsWith("data:")
        ? value
        : value.startsWith("http")
        ? value
        : value.startsWith("/")
        ? `http://localhost:3000${value}`
        : value;
      console.log("Image URL will be:", imageUrl);
    }
  }, [value]);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    const validTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!validTypes.includes(file.type)) {
      toast.error("Invalid file type", {
        description: "Please upload a JPG, PNG, GIF, or WebP image.",
      });
      return;
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File too large", {
        description: "Image must be less than 5MB.",
      });
      return;
    }

    // Create a preview
    const reader = new FileReader();
    reader.onload = () => {
      const previewUrl = reader.result as string;
      setPreview(previewUrl);
      // Pass both the preview URL and the file to the parent component
      onChange(previewUrl, file);
    };
    reader.readAsDataURL(file);
  };

  const handleRemove = () => {
    setPreview(null);
    onChange("", undefined); // Clear both the URL and the file
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Input
        type="file"
        accept="image/jpeg,image/png,image/gif,image/webp"
        onChange={handleFileChange}
        ref={fileInputRef}
        className="hidden"
        id={id}
      />

      {preview ? (
        <div className="relative w-full  border rounded-md overflow-hidden">
          <img
            src={
              preview.startsWith("data:")
                ? preview
                : preview.startsWith("http")
                ? preview
                : preview.startsWith("/")
                ? `http://localhost:3000${preview}`
                : preview
            }
            alt="Preview"
            className="object-contain"
          />
          <Button
            type="button"
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-8 w-8 rounded-full"
            onClick={handleRemove}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <label
          htmlFor={id}
          className="flex flex-col items-center justify-center w-full h-48 border-2 border-dashed rounded-md cursor-pointer hover:bg-muted/50 transition-colors"
        >
          <div className="flex flex-col items-center justify-center pt-5 pb-6">
            <UploadCloud className="w-10 h-10 mb-3 text-muted-foreground" />
            <p className="mb-2 text-sm text-muted-foreground">
              <span className="font-semibold">{label}</span>
            </p>
            <p className="text-xs text-muted-foreground">
              JPG, PNG, GIF or WebP (max 5MB)
            </p>
          </div>
        </label>
      )}
    </div>
  );
}
