import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatCurrency, formatDate } from "@/lib/utils";
import { Inventory } from "@/frontend-types";

export interface InventoryTableProps {
  inventories: Inventory[];
  onClick: (inventory: Inventory) => void;
}

export function InventoryTable({ inventories, onClick }: InventoryTableProps) {
  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Batch Number</TableHead>
            <TableHead>Warehouse</TableHead>
            <TableHead>Product</TableHead>
            <TableHead>Quantity</TableHead>
            <TableHead>Buying Price</TableHead>
            <TableHead>Selling Price</TableHead>
            <TableHead>Manufacture Date</TableHead>
            <TableHead>Expiry Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {inventories.map((item) => (
            <TableRow
              key={item.id}
              className="cursor-pointer"
              onClick={() => onClick(item)}
            >
              <TableCell>{item.batch.name}</TableCell>
              <TableCell>{item.batch.warehouse?.name ?? ""}</TableCell>
              <TableCell className="text-ellipsis">
                {item.product.name}
              </TableCell>
              <TableCell>{item.quantity}</TableCell>
              <TableCell>{formatCurrency(+item.buyingPrice)}</TableCell>
              <TableCell>{formatCurrency(+item.sellingPrice)}</TableCell>
              <TableCell>{formatDate(item.manufactureDate)}</TableCell>
              <TableCell>{formatDate(item.expiryDate)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
