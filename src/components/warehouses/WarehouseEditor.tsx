"use client";

import { Warehouse } from "@/frontend-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "../ui/button";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  // FormDescription,
  Form,
} from "../ui/form";
import { Separator } from "../ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Input } from "../ui/input";
import { useEffect, useState } from "react";
import { Switch } from "../ui/switch";
import { MapPicker } from "./MapPicker";
import { Card, CardContent } from "../ui/card";

const createWarehouseSchema = z.object({
  id: z.number().int(),
  name: z.string(),
  lat: z.string(),
  long: z.string(),
  type: z.enum(["GENERAL", "SUPER"]),
  active: z.boolean(),
});

type FormValues = z.infer<typeof createWarehouseSchema>;

export interface WarehouseEditorProps {
  warehouse?: Warehouse;
  isLoading: boolean;
  onEdit?: (warehouse: Warehouse) => void;
  onCreate?: (warehouse: Warehouse) => void;
}

export function WarehouseEditor(params: WarehouseEditorProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(createWarehouseSchema),
    defaultValues: {
      id: params.warehouse?.id ?? 0,
      name: params.warehouse?.name ?? "",
      lat: params.warehouse?.lat ?? "",
      long: params.warehouse?.long ?? "",
      type: params.warehouse?.type ?? "GENERAL",
      active: params.warehouse?.active ?? true,
    },
  });

  const [showMap, setShowMap] = useState(false);
  const router = useRouter();

  useEffect(() => {
    form.reset({
      id: params.warehouse?.id ?? 0,
      name: params.warehouse?.name ?? "",
      lat: params.warehouse?.lat ?? "",
      long: params.warehouse?.long ?? "",
      type: params.warehouse?.type ?? "GENERAL",
      active: params.warehouse?.active ?? true,
    });
  }, [params.warehouse, form, form.reset]);

  const handleLocationSelect = (lat: string, lng: string) => {
    form.setValue("lat", lat);
    form.setValue("long", lng);
  };

  const onSubmit = async (data: FormValues) => {
    const warehouse: Warehouse = {
      createdAt: params.warehouse?.createdAt ?? new Date(),
      updatedAt: params.warehouse?.createdAt ?? new Date(),
      id: params.warehouse?.id ?? 0,
      name: data.name,
      lat: data.lat,
      long: data.long,
      type: data.type,
      active: data.active,
    };

    if (params.warehouse) {
      if (params.onEdit) params.onEdit(warehouse);
    } else {
      if (params.onCreate) params.onCreate(warehouse);
    }
  };

  return (
    <div>
      <div className="flex flex-row mb-4 items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/warehouses")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="font-bold text-2xl">
          {params.warehouse ? "Edit" : "Add"} Warehouse
        </h1>
      </div>
      <Separator className="my-4" />

      <div className="max-w-2xl">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Location</h3>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowMap(!showMap)}
                >
                  {showMap ? "Hide Map" : "Show Map"}
                </Button>
              </div>

              {showMap && (
                <Card className="mb-4">
                  <CardContent className="pt-4">
                    <MapPicker
                      initialLat={form.getValues("lat")}
                      initialLng={form.getValues("long")}
                      onLocationSelect={handleLocationSelect}
                    />
                  </CardContent>
                </Card>
              )}

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="lat"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Latitude</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="long"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Longitude</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Type</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select the warehouse type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {["GENERAL", "SUPER"].map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Active</FormLabel>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <Button type="submit" disabled={params.isLoading}>
              {params.isLoading
                ? "Saving..."
                : `${params.warehouse ? "Edit" : "Create"} Warehouse`}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}
