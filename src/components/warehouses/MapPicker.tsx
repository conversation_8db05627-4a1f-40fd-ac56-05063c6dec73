import { useCallback, useEffect, useRef, useState } from "react";
import {
  GoogleMap,
  useJsApi<PERSON>oader,
  Marker,
  Libraries,
} from "@react-google-maps/api";

const containerStyle = {
  width: "100%",
  height: "400px",
};

const libraries: Libraries = ["places"];

interface MapPickerProps {
  initialLat?: string;
  initialLng?: string;
  onLocationSelect: (lat: string, lng: string) => void;
}

export function MapPicker({
  initialLat,
  initialLng,
  onLocationSelect,
}: MapPickerProps) {
  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAP_KEY!,
    libraries: libraries,
  });

  const mapRef = useRef<google.maps.Map | null>(null);
  const [center, setCenter] = useState<google.maps.LatLngLiteral>({
    lat: initialLat ? parseFloat(initialLat) : 28.6139,
    lng: initialLng ? parseFloat(initialLng) : 77.209,
  });
  const [markerPosition, setMarkerPosition] =
    useState<google.maps.LatLngLiteral | null>(
      initialLat && initialLng
        ? {
            lat: parseFloat(initialLat),
            lng: parseFloat(initialLng),
          }
        : null
    );
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<
    google.maps.places.AutocompletePrediction[]
  >([]);

  const autocompleteService =
    useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);

  useEffect(() => {
    if (isLoaded && mapRef.current) {
      autocompleteService.current =
        new window.google.maps.places.AutocompleteService();
      placesService.current = new window.google.maps.places.PlacesService(
        mapRef.current
      );
    }
  }, [isLoaded]);

  const onLoad = useCallback(
    (map: google.maps.Map) => {
      mapRef.current = map;

      // Initialize places services after map is loaded
      if (isLoaded) {
        autocompleteService.current =
          new window.google.maps.places.AutocompleteService();
        placesService.current = new window.google.maps.places.PlacesService(
          map
        );
      }
    },
    [isLoaded]
  );

  const onUnmount = useCallback(() => {
    mapRef.current = null;
  }, []);

  const handleMapClick = useCallback(
    (e: google.maps.MapMouseEvent) => {
      if (e.latLng) {
        const lat = e.latLng.lat().toString();
        const lng = e.latLng.lng().toString();

        setMarkerPosition({
          lat: e.latLng.lat(),
          lng: e.latLng.lng(),
        });

        onLocationSelect(lat, lng);
      }
    },
    [onLocationSelect]
  );

  const handleSearch = (query: string) => {
    if (autocompleteService.current && query.length > 0) {
      autocompleteService.current.getPlacePredictions(
        { input: query },
        (predictions) => {
          if (predictions) {
            setSearchResults(predictions);
          }
        }
      );
    } else {
      setSearchResults([]);
    }
  };

  const handlePlaceSelect = (placeId: string) => {
    if (placesService.current) {
      placesService.current.getDetails({ placeId }, (place) => {
        if (place && place.geometry?.location) {
          const lat = place.geometry.location.lat().toString();
          const lng = place.geometry.location.lng().toString();

          setCenter({
            lat: place.geometry.location.lat(),
            lng: place.geometry.location.lng(),
          });

          setMarkerPosition({
            lat: place.geometry.location.lat(),
            lng: place.geometry.location.lng(),
          });

          onLocationSelect(lat, lng);

          // Clear search results
          setSearchResults([]);
          setSearchQuery("");
        }
      });
    }
  };

  return (
    <div className="w-full">
      {isLoaded ? (
        <>
          <div className="relative mb-2">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                handleSearch(e.target.value);
              }}
              className="border p-2 rounded w-full"
              placeholder="Search for a location..."
            />
            {searchResults.length > 0 && (
              <ul className="absolute bg-white border w-full z-10 max-h-60 overflow-auto">
                {searchResults.map((result) => (
                  <li
                    key={result.place_id}
                    className="cursor-pointer p-2 hover:bg-gray-100"
                    onClick={() => handlePlaceSelect(result.place_id)}
                  >
                    {result.description}
                  </li>
                ))}
              </ul>
            )}
          </div>
          <GoogleMap
            mapContainerStyle={containerStyle}
            center={center}
            zoom={10}
            onLoad={onLoad}
            onUnmount={onUnmount}
            onClick={handleMapClick}
          >
            {markerPosition && (
              <Marker
                position={markerPosition}
                draggable={true}
                onDragEnd={(e) => {
                  if (e.latLng) {
                    const lat = e.latLng.lat().toString();
                    const lng = e.latLng.lng().toString();

                    setMarkerPosition({
                      lat: e.latLng.lat(),
                      lng: e.latLng.lng(),
                    });

                    onLocationSelect(lat, lng);
                  }
                }}
              />
            )}
          </GoogleMap>
          <p className="text-sm text-gray-500 mt-2">
            Click on the map to select a location or search for an address
            above. You can also drag the marker to adjust the position.
          </p>
        </>
      ) : (
        <div className="flex items-center justify-center h-[400px] bg-gray-100 rounded">
          <p>Loading Google Maps...</p>
        </div>
      )}
    </div>
  );
}
