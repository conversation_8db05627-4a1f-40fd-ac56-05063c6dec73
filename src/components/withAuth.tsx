/* eslint-disable @typescript-eslint/no-explicit-any */
import { useAuth } from "@/context";
import { NextPage } from "next";
import { useRouter } from "next/router";
import { useEffect } from "react";
import { AppSidebar } from "./Sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import NavigationBar from "./NavigationBar";
import { LoadingSpinner } from "./LoadingSpinner";
import { Label } from "./ui/label";

export function withAuth(WrappedComponent: NextPage) {
  const Component = (props: any) => {
    const router = useRouter();
    const { isAuthenticated, isLoading } = useAuth();

    useEffect(() => {
      if (!isLoading && !isAuthenticated()) {
        console.log("withAuth: not authenticated");
        router.replace("/auth");
      }
    }, [isAuthenticated, router, isLoading]);

    // If not authenticated, don't render anything
    if (!isAuthenticated() && isLoading) {
      return (
        <main className="flex flex-row items-center justify-center h-screen">
          <LoadingSpinner className="" />
        </main>
      );
    }

    if (!isAuthenticated() && !isLoading) {
      return (
        <main className="flex flex-row items-center justify-center h-screen">
          <Label>Unauthorized</Label>
        </main>
      );
    }

    return (
      <>
        <SidebarProvider>
          <AppSidebar />
          <main className="w-full">
            <NavigationBar />
            <div className="mt-14 bg-gray-100">
              <WrappedComponent {...props} />
            </div>
          </main>
        </SidebarProvider>
      </>
    );
  };

  return Component;
}
