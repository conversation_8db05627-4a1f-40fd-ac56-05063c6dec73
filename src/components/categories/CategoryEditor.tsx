"use client";

import { Category, CategoryType } from "@/frontend-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
  Form,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { ImageUpload } from "@/components/image-upload";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import ItemPicker, { Column } from "@/components/ItemPicker";
import { apiService } from "@/api";
import { PaginatedResponse } from "@/frontend-types";
import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const createCategorySchema = z.object({
  id: z.number().int(),
  name: z.string().min(1, "Name is required"),
  type: z.enum(["COLLECTION", "CATEGORY", "SEGMENT"] as const),
  parentId: z.string().optional().nullable(),
  slug: z.string().min(1, "Slug is required"),
  isActive: z.boolean().default(true),
  bannerUrl: z.string().optional().or(z.literal("")),
  iconUrl: z.string().optional().or(z.literal("")),
});

type FormValues = z.infer<typeof createCategorySchema>;

export interface CategoryEditorProps {
  category?: Category;
  isLoading: boolean;
  onCreate?: (
    category: Category & { files?: { banner?: File; icon?: File } }
  ) => void;
  onEdit?: (
    category: Category & { files?: { banner?: File; icon?: File } }
  ) => void;
}

export function CategoryEditor({
  category,
  isLoading,
  onCreate,
  onEdit,
}: CategoryEditorProps) {
  const router = useRouter();

  const [bannerFile, setBannerFile] = useState<File>();
  const [iconFile, setIconFile] = useState<File>();
  const [parentCategory, setParentCategory] = useState<Category | null>(
    category?.parent ?? null
  );

  const form = useForm<FormValues>({
    resolver: zodResolver(createCategorySchema),
    defaultValues: {
      id: category?.id ?? 0,
      name: category?.name ?? "",
      type: category?.type ?? "SEGMENT",
      parentId: category?.parentId?.toString() ?? null,
      slug: category?.slug ?? "",
      isActive: category?.isActive ?? true,
      bannerUrl: category?.bannerUrl ?? "",
      iconUrl: category?.iconUrl ?? "",
    },
  });

  // Watch type so UI reacts
  const selectedType = form.watch("type");

  // Reset parent when type changes
  useEffect(() => {
    form.setValue("parentId", null);
    setParentCategory(null);
  }, [selectedType, form]);

  useEffect(() => {
    form.reset({
      id: category?.id ?? 0,
      name: category?.name ?? "",
      type: category?.type ?? "SEGMENT",
      parentId: category?.parentId?.toString() ?? null,
      slug: category?.slug ?? "",
      isActive: category?.isActive ?? true,
      bannerUrl: category?.bannerUrl ?? "",
      iconUrl: category?.iconUrl ?? "",
    });
    setBannerFile(undefined);
    setIconFile(undefined);
    setParentCategory(category?.parent ?? null);
  }, [category, form]);

  const onSubmit = async (data: FormValues) => {
    const payload: any = {
      id: data.id,
      name: data.name,
      type: data.type,
      parentId: data.parentId ? parseInt(data.parentId) : null,
      slug: data.slug,
      isActive: data.isActive,
      bannerUrl: data.bannerUrl || undefined,
      iconUrl: data.iconUrl || undefined,
      createdAt: category?.createdAt ?? new Date(),
      updatedAt: category?.updatedAt ?? new Date(),
      files: {
        banner: bannerFile,
        icon: iconFile,
      },
    };
    if (category && onEdit) {
      onEdit(payload);
    } else if (!category && onCreate) {
      onCreate(payload);
    }
  };

  const handleNameChange = (value: string) => {
    const slug = value
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)+/g, "");
    form.setValue("slug", slug);
  };

  const columns: Column<Category>[] = [{ accessorKey: "name", header: "Name" }];

  const fetchParentItems = async (
    page: number,
    pageSize: number,
    search: string
  ): Promise<PaginatedResponse<Category>> => {
    let filterType: CategoryType | undefined;
    if (selectedType === "CATEGORY") {
      filterType = "COLLECTION";
    } else if (selectedType === "SEGMENT") {
      filterType = "CATEGORY";
    } else if (selectedType === "COLLECTION") {
      return {
        data: [],
        page: 0,
        pageSize: 0,
        total: 0,
        totalPages: 0,
      };
    }
    const resp = await apiService.get("admin/categories", {
      page,
      pageSize,
      search,
      type: filterType,
    });
    return resp.data as PaginatedResponse<Category>;
  };

  return (
    <div>
      <div className="flex items-center gap-4 mb-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/categories")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold">
          {category ? "Edit" : "Add"} Category
        </h1>
      </div>

      <Separator className="my-4" />

      <div className="max-w-2xl">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleNameChange(e.target.value);
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Slug */}
            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Slug</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormDescription>
                    URL-friendly; auto-generated
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Type</FormLabel>
                  <FormControl>
                    <Select
                      value={field.value}
                      onValueChange={(val) => {
                        field.onChange(val);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="COLLECTION">Collection</SelectItem>
                        <SelectItem value="CATEGORY">Category</SelectItem>
                        <SelectItem value="SEGMENT">Segment</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Parent (hidden if COLLECTION) */}
            {selectedType !== "COLLECTION" && (
              <FormField
                control={form.control}
                name="parentId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Parent{" "}
                      {selectedType === "CATEGORY" ? "Collection" : "Category"}
                    </FormLabel>
                    <FormDescription></FormDescription>
                    <FormControl>
                      <ItemPicker<Category>
                        title={
                          selectedType === "CATEGORY"
                            ? "Select Collection"
                            : "Select Category"
                        }
                        selectionMode="single"
                        columns={columns}
                        fetchItems={fetchParentItems}
                        initialSelectedItems={
                          field.value
                            ? [
                                {
                                  id: parseInt(field.value),
                                  name: parentCategory?.name ?? "",
                                } as Category,
                              ]
                            : []
                        }
                        onConfirmSelection={(item) => {
                          field.onChange((item as Category).id.toString());
                          setParentCategory(item as Category);
                        }}
                        renderTrigger={() => (
                          <Button variant="outline">
                            {parentCategory
                              ? parentCategory.name
                              : selectedType === "CATEGORY"
                              ? "Select Collection"
                              : "Select Category"}
                          </Button>
                        )}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Active */}
            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <FormLabel>Active</FormLabel>
                    <FormDescription>Visible in store</FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Images */}
            <Card>
              <CardHeader>
                <CardTitle>Images</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="bannerUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Banner Image</FormLabel>
                      <FormControl>
                        <ImageUpload
                          value={field.value}
                          onChange={(url, file) => {
                            field.onChange(url);
                            setBannerFile(file);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="iconUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Icon Image</FormLabel>
                      <FormControl>
                        <ImageUpload
                          value={field.value}
                          onChange={(url, file) => {
                            field.onChange(url);
                            setIconFile(file);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <Button type="submit" disabled={isLoading}>
              {isLoading
                ? category
                  ? "Saving..."
                  : "Creating..."
                : category
                ? "Save"
                : "Create"}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}
