import { apiService } from "@/api";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { CategoryTranslation, Language } from "@/frontend-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const schema = z.object({
  languageId: z.number(),
  name: z.string().min(1, "Translated name is required"),
});

type FormValues = z.infer<typeof schema>;

export default function TranslationDialog({
  open,
  onClose,
  onSubmit,
  initialData,
}: {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: FormValues) => void;
  initialData?: CategoryTranslation | null;
}) {
  const [languages, setLanguages] = useState<Language[]>([]);

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      languageId: 0,
      name: "",
    },
  });

  useEffect(() => {
    async function loadLanguages() {
      try {
        const res = await apiService.get("languages");
        setLanguages(res.data || []);
      } catch (error) {
        toast.error("Failed to load languages", {
          description: (error as Error).message,
        });
      }
    }

    loadLanguages();
  }, []);

  useEffect(() => {
    if (initialData) {
      form.reset({
        languageId: initialData.languageId,
        name: initialData.name,
      });
    } else {
      form.reset({
        languageId: 0,
        name: "",
      });
    }
  }, [initialData, form]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md max-w-3xl">
        <DialogHeader>
          <DialogTitle>
            {initialData ? "Edit Translation" : "Add Translation"}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            className="space-y-4"
            onSubmit={form.handleSubmit((values) => onSubmit(values))}
          >
            <FormField
              control={form.control}
              name="languageId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Language</FormLabel>
                  <FormControl>
                    <Select
                      disabled={!!initialData}
                      onValueChange={(val) => field.onChange(Number(val))}
                      value={field.value.toString()}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                      <SelectContent>
                        {languages.map((lang) => (
                          <SelectItem key={lang.id} value={lang.id.toString()}>
                            <div className="flex items-center gap-2">
                              {/* <Image
                                src={lang.flagUrl}
                                alt={lang.code}
                                className="w-4 h-4"
                              /> */}
                              <span>{lang.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Translated Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full">
              {initialData ? "Save Changes" : "Add Translation"}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
