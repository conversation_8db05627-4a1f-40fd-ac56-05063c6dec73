"use client";

import { User } from "@/frontend-types";
import { useState, useEffect } from "react";
import { jwtDecode } from "jwt-decode";
import { AuthContext } from "@/context";
import { toast } from "sonner";
import { apiService } from "@/api";

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isLoading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    const token = localStorage.getItem("token");
    setToken(token);
    setLoading(false);
  }, []);

  useEffect(() => {
    async function fetchUser() {
      if (token) {
        try {
          const response = await apiService.get("users");

          if (!response.data) {
            throw new Error("Could not fetch user details.");
          }

          setUser(response.data as User);
        } catch (error) {
          console.log(error);
          localStorage.removeItem("token");
        }
      }
    }

    fetchUser();
  }, [token]);

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/login`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email, password }),
        }
      );

      if (!response.ok) {
        throw new Error("Invalid username or password.");
      }

      const { token } = await response.json();

      localStorage.setItem("token", token);
      setToken(token);

      const decoded = jwtDecode<User>(token);
      setUser(decoded);
    } catch (error) {
      toast.error(`${(error as Error).message}`);
    }
    setLoading(false);
  };

  const logout = () => {
    localStorage.removeItem("token");
    setToken(null);
    setUser(null);
  };

  const isAuthenticated = () => {
    if (!token) return false;

    try {
      const decoded = jwtDecode(token);
      if (!decoded.exp) {
        logout();
        return false;
      }

      const isExpired = Date.now() >= decoded.exp * 1000;

      if (isExpired) {
        logout();
        return false;
      }

      return true;
    } catch (error) {
      console.log(error);
      return false;
    }
  };

  return (
    <AuthContext.Provider
      value={{ user, login, logout, isAuthenticated, isLoading }}
    >
      {children}
    </AuthContext.Provider>
  );
};
