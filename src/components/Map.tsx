"use client";

import { useEffect, useRef, useState } from "react";
import { Loader } from "@googlemaps/js-api-loader";
import { Input } from "./ui/input";
import { Button } from "./ui/button";
import { Search } from "lucide-react";

interface Position {
  lat: number;
  lng: number;
}

interface Marker {
  position: Position;
  title?: string;
  draggable?: boolean;
}

interface MapProps {
  center: Position;
  zoom: number;
  markers?: Marker[];
  onClick?: (position: Position) => void;
  onMarkerDragEnd?: (position: Position) => void;
  onLocationSelect?: (
    lat: string,
    lng: string,
    addressComponents?: google.maps.GeocoderAddressComponent[]
  ) => void;
  showSearch?: boolean;
  draggable?: boolean;
}

const Map = ({
  center,
  zoom,
  markers = [],
  onClick,
  onMarkerDragEnd,
  onLocationSelect,
  showSearch = false,
  draggable = true,
}: MapProps) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const markersRef = useRef<google.maps.Marker[]>([]);
  const searchBoxRef = useRef<google.maps.places.SearchBox | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    const initMap = async () => {
      const loader = new Loader({
        apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAP_KEY || "",
        version: "weekly",
        libraries: ["places"],
      });

      const google = await loader.load();
      const { Map } = (await google.maps.importLibrary(
        "maps"
      )) as google.maps.MapsLibrary;

      if (mapRef.current) {
        // Create the map instance
        const mapInstance = new Map(mapRef.current, {
          center,
          zoom,
          disableDefaultUI: true,
          zoomControl: true,
          streetViewControl: false,
          mapTypeControl: false,
          draggable,
        });

        mapInstanceRef.current = mapInstance;

        // Add click event listener
        if (onClick) {
          mapInstance.addListener("click", (e: google.maps.MapMouseEvent) => {
            if (e.latLng) {
              const position = {
                lat: e.latLng.lat(),
                lng: e.latLng.lng(),
              };
              onClick(position);

              // If onLocationSelect is provided, get address components
              if (onLocationSelect) {
                updateLocation(e.latLng);
              }
            }
          });
        }

        // Add markers
        markers.forEach((marker) => {
          const newMarker = new google.maps.Marker({
            position: marker.position,
            map: mapInstance,
            title: marker.title,
            draggable: marker.draggable,
          });

          if (marker.draggable && onMarkerDragEnd) {
            newMarker.addListener("dragend", () => {
              const position = newMarker.getPosition();
              if (position) {
                const newPosition = {
                  lat: position.lat(),
                  lng: position.lng(),
                };
                onMarkerDragEnd(newPosition);

                // If onLocationSelect is provided, get address components
                if (onLocationSelect) {
                  updateLocation(position);
                }
              }
            });
          }

          markersRef.current.push(newMarker);
        });

        // Initialize SearchBox if showSearch is true
        if (showSearch) {
          const input = document.getElementById(
            "map-search"
          ) as HTMLInputElement;
          if (input) {
            const searchBox = new google.maps.places.SearchBox(input);
            searchBoxRef.current = searchBox;

            // Bias SearchBox results towards current map's viewport
            mapInstance.addListener("bounds_changed", () => {
              searchBox.setBounds(
                mapInstance.getBounds() as google.maps.LatLngBounds
              );
            });

            // Listen for the event fired when the user selects a prediction
            searchBox.addListener("places_changed", () => {
              const places = searchBox.getPlaces();
              if (!places?.length) return;

              const place = places[0];
              if (!place.geometry?.location) return;

              // Update map and marker
              mapInstance.setCenter(place.geometry.location);

              // If onLocationSelect is provided, get address components
              if (onLocationSelect) {
                onLocationSelect(
                  place.geometry.location.lat().toString(),
                  place.geometry.location.lng().toString(),
                  place.address_components
                );
              }
            });
          }
        }
      }
    };

    initMap();

    return () => {
      // Clean up markers
      markersRef.current.forEach((marker) => marker.setMap(null));
      markersRef.current = [];
    };
  }, [
    center,
    zoom,
    markers,
    onClick,
    onMarkerDragEnd,
    onLocationSelect,
    showSearch,
    draggable,
  ]);

  const updateLocation = async (location: google.maps.LatLng) => {
    if (!onLocationSelect) return;

    const google = await new Loader({
      apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAP_KEY || "",
      version: "weekly",
    }).load();

    const geocoder = new google.maps.Geocoder();
    const response = await geocoder.geocode({ location });

    if (response.results[0]) {
      onLocationSelect(
        location.lat().toString(),
        location.lng().toString(),
        response.results[0].address_components
      );
    }
  };

  const handleSearch = () => {
    searchBoxRef.current?.set("value", searchQuery);
  };

  return (
    <div className="space-y-4">
      {showSearch && (
        <div className="flex gap-2">
          <Input
            id="map-search"
            placeholder="Search for a location..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault();
                handleSearch();
              }
            }}
          />
          <Button type="button" variant="outline" onClick={handleSearch}>
            <Search className="h-4 w-4" />
          </Button>
        </div>
      )}
      <div ref={mapRef} className="w-full h-full" />
    </div>
  );
};

export default Map;
