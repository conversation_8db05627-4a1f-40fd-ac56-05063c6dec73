import { useState, useEffect } from "react";
import { apiService } from "@/api";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AmountType } from "@/frontend-types";

interface Product {
  id: number;
  name: string;
  barcode: string;
}

interface AddTierProductDiscountDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tierId: number;
  onSuccess: () => void;
}

export function AddTierProductDiscountDialog({
  open,
  onOpenChange,
  tierId,
  onSuccess,
}: AddTierProductDiscountDialogProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProductId, setSelectedProductId] = useState<string>("");
  const [discountValue, setDiscountValue] = useState<string>("");
  const [discountType, setDiscountType] = useState<AmountType>("PERCENTAGE");
  const [loading, setLoading] = useState(false);
  const [productsLoading, setProductsLoading] = useState(false);

  useEffect(() => {
    if (open) {
      fetchProducts();
    }
  }, [open]);

  const fetchProducts = async () => {
    try {
      setProductsLoading(true);
      const response = await apiService.get("admin/products");
      setProducts(response.data);
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Error", {
        description: "Failed to load products",
      });
    } finally {
      setProductsLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!selectedProductId || !discountValue) {
      toast.error("Error", {
        description: "Please fill in all required fields",
      });
      return;
    }

    try {
      setLoading(true);
      await apiService.post("admin/tier-product-discounts", {
        tierId,
        productId: parseInt(selectedProductId),
        discountValue: parseFloat(discountValue),
        discountType,
        active: true,
      });

      toast.success("Success", {
        description: "Product discount added successfully",
      });

      // Reset form
      setSelectedProductId("");
      setDiscountValue("");
      setDiscountType("PERCENTAGE");

      // Close dialog and refresh data
      onOpenChange(false);
      onSuccess();
    } catch (error) {
      console.error("Error adding product discount:", error);
      toast.error("Error", {
        description: "Failed to add product discount",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] max-w-3xl">
        <DialogHeader>
          <DialogTitle>Add Product Discount</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="product" className="text-right">
              Product
            </Label>
            <Select
              value={selectedProductId}
              onValueChange={setSelectedProductId}
              disabled={productsLoading}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select a product" />
              </SelectTrigger>
              <SelectContent>
                {productsLoading ? (
                  <SelectItem value="loading" disabled>
                    Loading products...
                  </SelectItem>
                ) : (
                  products.map((product) => (
                    <SelectItem key={product.id} value={product.id.toString()}>
                      {product.name} ({product.barcode})
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="discountType" className="text-right">
              Discount Type
            </Label>
            <Select
              value={discountType}
              onValueChange={(value) => setDiscountType(value as AmountType)}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select discount type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={"PERCENTAGE"}>Percentage (%)</SelectItem>
                <SelectItem value={"FLAT"}>Flat Amount</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="discountValue" className="text-right">
              Discount Value
            </Label>
            <Input
              id="discountValue"
              type="number"
              value={discountValue}
              onChange={(e) => setDiscountValue(e.target.value)}
              className="col-span-3"
              placeholder={
                discountType === "PERCENTAGE" ? "e.g. 10" : "e.g. 100"
              }
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button type="button" onClick={handleSubmit} disabled={loading}>
            {loading ? "Adding..." : "Add Discount"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
