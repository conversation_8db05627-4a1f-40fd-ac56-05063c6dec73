"use client";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { RewardTier } from "@/frontend-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "../ui/button";
import { Input } from "../ui/input";

const rewardTierSchema = z.object({
  name: z.string().min(1, "Name is required"),
  requiredSpending: z
    .number()
    .min(0, "Required points to gain this tier is required"),
  earnPercentage: z.number().min(0, "Earn % is required"),
  redeemPercentage: z.number().min(0, "Redeem % is required"),
  maxDiscountValue: z.number(),
  minOrderAmountForBonus: z.number().nullable(),
});

export type RewardTierFormValues = z.infer<typeof rewardTierSchema>;

export function RewardTierEditor({
  tier,
  isLoading,
  onSubmit,
}: {
  tier?: RewardTier;
  isLoading?: boolean;
  onSubmit: (data: RewardTierFormValues) => void;
}) {
  const router = useRouter();

  const form = useForm<RewardTierFormValues>({
    resolver: zodResolver(rewardTierSchema),
    defaultValues: {
      name: tier?.name ?? "",
      requiredSpending:
        tier?.requiredRollingSpend ?? (undefined as unknown as number),
      earnPercentage: tier?.earnPercentage ?? (undefined as unknown as number),
      redeemPercentage:
        tier?.redeemPercentage ?? (undefined as unknown as number),
      maxDiscountValue:
        tier?.maxDiscountValue ?? (undefined as unknown as number),
      minOrderAmountForBonus:
        tier?.minOrderAmountForBonus ?? (undefined as unknown as number),
    },
  });

  useEffect(() => {
    form.reset({
      name: tier?.name || "",
      requiredSpending:
        tier?.requiredRollingSpend ?? (undefined as unknown as number),
      earnPercentage: tier?.earnPercentage ?? (undefined as unknown as number),
      redeemPercentage:
        tier?.redeemPercentage ?? (undefined as unknown as number),
      maxDiscountValue:
        tier?.maxDiscountValue ?? (undefined as unknown as number),
      minOrderAmountForBonus:
        tier?.minOrderAmountForBonus ?? (undefined as unknown as number),
    });
  }, [tier, form]);

  return (
    <div>
      <div className="flex flex-row mb-4 items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/reward-tiers")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="font-bold text-2xl">
          {tier ? "Edit" : "Add"} Reward Tier
        </h1>
      </div>
      <Separator className="my-4" />

      <div className="max-w-xl">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="requiredSpending"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Required Points</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      value={field.value ?? ""}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value === ""
                            ? undefined
                            : Number(e.target.value)
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="earnPercentage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Earn Percentage (%)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      value={field.value ?? ""}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value === ""
                            ? undefined
                            : Number(e.target.value)
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="redeemPercentage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Redeem Percentage (%)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      value={field.value ?? ""}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value === ""
                            ? undefined
                            : Number(e.target.value)
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="maxDiscountValue"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Max Discount Value</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      value={field.value ?? ""}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value === "" ? null : Number(e.target.value)
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="minOrderAmountForBonus"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Min Order Value</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      value={field.value ?? ""}
                      onChange={(e) =>
                        field.onChange(
                          e.target.value === "" ? null : Number(e.target.value)
                        )
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : tier ? "Save Changes" : "Create Tier"}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}
