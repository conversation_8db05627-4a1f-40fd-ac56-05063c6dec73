import { apiService } from "@/api";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";
import { AmountType } from "@/frontend-types";
import { PlusIcon, Trash2Icon } from "lucide-react";
import { useEffect, useState } from "react";
import { AddTierProductDiscountDialog } from "./AddTierProductDiscountDialog";
import Image from "next/image";

interface Product {
  id: number;
  name: string;
  barcode: string;
  media?: { url: string }[];
}

interface TierProductDiscount {
  id: number;
  tierId: number;
  productId: number;
  discountValue: number;
  discountType: AmountType;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  product: Product;
}

interface TierProductDiscountListProps {
  tierId: number;
  tierName: string;
}

export function TierProductDiscountList({
  tierId,
  tierName,
}: TierProductDiscountListProps) {
  const [discounts, setDiscounts] = useState<TierProductDiscount[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const fetchDiscounts = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(
        `admin/tier-product-discounts/tier/${tierId}`
      );
      setDiscounts(response.data);
    } catch (error) {
      console.error("Error fetching tier product discounts:", error);
      toast.error("Error", {
        description: "Failed to load product discounts",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDiscounts();
  }, [tierId]);

  const handleAddDiscount = async () => {
    setIsAddDialogOpen(true);
  };

  const handleRemoveDiscount = async (id: number) => {
    try {
      await apiService.delete(`admin/tier-product-discounts/${id}`);
      toast.success("Success", {
        description: "Product discount removed successfully",
      });
      fetchDiscounts();
    } catch (error) {
      console.error("Error removing product discount:", error);
      toast.error("Error", {
        description: "Failed to remove product discount",
      });
    }
  };

  const handleToggleStatus = async (id: number, currentStatus: boolean) => {
    try {
      await apiService.patch(
        `admin/tier-product-discounts/${id}/toggle-status`,
        {
          active: !currentStatus,
        }
      );
      toast.success("Success", {
        description: `Product discount ${
          !currentStatus ? "activated" : "deactivated"
        } successfully`,
      });
      fetchDiscounts();
    } catch (error) {
      console.error("Error updating product discount status:", error);
      toast.error("Error", {
        description: "Failed to update product discount status",
      });
    }
  };

  const formatDiscountValue = (discount: TierProductDiscount) => {
    if (discount.discountType === "FLAT") {
      return `${discount.discountValue} (Flat)`;
    } else {
      return `${discount.discountValue}% (Percentage)`;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">
          Product Discounts for {tierName}
        </h2>
        <div className="flex gap-2">
          <Button onClick={handleAddDiscount}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Product Discount
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-10">Loading...</div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Product</TableHead>
              <TableHead>Barcode</TableHead>
              <TableHead>Discount</TableHead>
              <TableHead>Status Toggle</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {discounts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-6">
                  No product discounts found for this tier.
                </TableCell>
              </TableRow>
            ) : (
              discounts.map((discount) => (
                <TableRow key={discount.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {discount.product.media &&
                        discount.product.media.length > 0 && (
                          <Image
                            src={discount.product.media[0].url}
                            alt={discount.product.name}
                            className="h-8 w-8 object-cover rounded"
                          />
                        )}
                      <span>{discount.product.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>{discount.product.barcode}</TableCell>
                  <TableCell>{formatDiscountValue(discount)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={discount.active}
                        onCheckedChange={() =>
                          handleToggleStatus(discount.id, discount.active)
                        }
                      />
                      <span
                        className={`px-2 py-1 rounded text-xs ${
                          discount.active
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {discount.active ? "Active" : "Inactive"}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleRemoveDiscount(discount.id)}
                    >
                      <Trash2Icon className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      )}

      <AddTierProductDiscountDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        tierId={tierId}
        onSuccess={fetchDiscounts}
      />
    </div>
  );
}
