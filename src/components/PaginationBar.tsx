import { PaginationData } from "@/frontend-types";
import {
  Pagin<PERSON>,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationLink,
  PaginationNext,
  PaginationEllipsis,
} from "./ui/pagination";

export type PaginationBarProps = {
  className?: string;
  pagination: PaginationData;
  handlePageChange: (page: number) => void;
};

export default function PaginationBar({
  className = "",
  pagination,
  handlePageChange,
}: PaginationBarProps) {
  const { page, totalPages } = pagination;

  // Function to determine which page numbers to show
  const getPageNumbers = () => {
    // For 7 or fewer pages, show all pages
    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // For more than 7 pages, use a more complex algorithm
    // Always show first, last, current, and some pages around current
    const pageNumbers = [];

    // Always show page 1
    pageNumbers.push(1);

    // Determine start and end of the middle section
    let startPage = Math.max(2, page - 1);
    let endPage = Math.min(page + 1, totalPages - 1);

    // Adjust to show at least 3 pages in the middle if possible
    if (page <= 3) {
      endPage = Math.min(5, totalPages - 1);
    } else if (page >= totalPages - 2) {
      startPage = Math.max(totalPages - 4, 2);
    }

    // Add ellipsis after page 1 if needed
    if (startPage > 2) {
      pageNumbers.push(-1); // -1 is a marker for ellipsis
    }

    // Add the middle pages
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    // Add ellipsis before the last page if needed
    if (endPage < totalPages - 1) {
      pageNumbers.push(-2); // -2 is another marker for ellipsis
    }

    // Always show the last page
    pageNumbers.push(totalPages);

    return pageNumbers;
  };

  return (
    <Pagination className={className}>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious
            href="#"
            onClick={(e) => {
              e.preventDefault();
              if (page > 1) handlePageChange(page - 1);
            }}
            className={page <= 1 ? "pointer-events-none opacity-50" : ""}
            aria-disabled={page <= 1}
          />
        </PaginationItem>

        {getPageNumbers().map((pageNumber, i) => (
          <PaginationItem key={i}>
            {pageNumber < 0 ? (
              <PaginationEllipsis />
            ) : (
              <PaginationLink
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(pageNumber);
                }}
                isActive={page === pageNumber}
              >
                {pageNumber}
              </PaginationLink>
            )}
          </PaginationItem>
        ))}

        <PaginationItem>
          <PaginationNext
            href="#"
            onClick={(e) => {
              e.preventDefault();
              if (page < totalPages) handlePageChange(page + 1);
            }}
            className={
              page >= totalPages ? "pointer-events-none opacity-50" : ""
            }
            aria-disabled={page >= totalPages}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}
