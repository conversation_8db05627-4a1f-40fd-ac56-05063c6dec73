"use client";

import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import {
  Bell,
  Box,
  Cable,
  CalendarClock,
  ChartBarStacked,
  FileText,
  Gift,
  Home,
  Image,
  LayoutGrid,
  Locate,
  LucideIcon,
  Rows3,
  Settings,
  ShieldCheck,
  ShoppingBag,
  Tag,
  Users,
  Warehouse,
} from "lucide-react";
import { useRouter } from "next/navigation";

// Menu items.
const items = [
  {
    title: "Dashboard",
    url: "/",
    icon: Home,
  },
  {
    title: "Users",
    url: "/users",
    icon: Users,
  },
  {
    title: "Products",
    url: "/products",
    icon: Box,
  },
  {
    title: "Categories",
    url: "/categories",
    icon: ChartBarStacked,
  },
  {
    title: "Suppliers",
    url: "/suppliers",
    icon: Cable,
  },
  {
    title: "Orders",
    url: "/orders",
    icon: ShoppingBag,
  },
  {
    title: "Reward Tiers",
    url: "/reward-tiers",
    icon: Gift,
  },
  {
    title: "Banners",
    url: "/banners",
    icon: Image,
  },
  {
    title: "Home Sections",
    url: "/home-sections",
    icon: LayoutGrid,
  },
  {
    title: "Inventories",
    url: "/inventories",
    icon: Rows3,
  },
  {
    title: "Coupons",
    url: "/coupons",
    icon: Tag,
  },
  {
    title: "Zones",
    url: "/zones",
    icon: Locate,
  },
  {
    title: "Warehouses",
    url: "/warehouses",
    icon: Warehouse,
  },
  {
    title: "Delivery Slots",
    url: "/delivery-slots",
    icon: CalendarClock,
  },
  {
    title: "Product Policies",
    url: "/product-policy",
    icon: FileText,
  },
  {
    title: "Notifications",
    url: "/notifications",
    icon: Bell,
  },
];

// Permissions menu items
const permissionItems = [
  {
    title: "Permission Features",
    url: "/permissions/features",
    icon: Settings,
  },
  {
    title: "Roles",
    url: "/permissions/roles",
    icon: ShieldCheck,
  },
];

function SidebarItems({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon: LucideIcon;
  }[];
}) {
  const router = useRouter();
  // const currentPath = router.pathname;
  const activePath = false;

  return (
    <>
      {items.map((item) => (
        <SidebarMenuItem
          className={cn(
            activePath ? "bg-gray-200 text-black font-bold rounded-md" : ""
          )}
          key={item.title}
        >
          <SidebarMenuButton asChild>
            <a href={item.url}>
              <item.icon />
              <span>{item.title}</span>
            </a>
          </SidebarMenuButton>
        </SidebarMenuItem>
      ))}
    </>
  );
}

export function AppSidebar() {
  return (
    <Sidebar>
      <SidebarHeader />
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Application</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarItems items={items} />
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarSeparator />
        <SidebarGroup>
          <SidebarGroupLabel>Permissions</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarItems items={permissionItems} />
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter />
    </Sidebar>
  );
}
