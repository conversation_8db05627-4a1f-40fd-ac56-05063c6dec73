"use client";

import { useEffect, useState } from "react";
import { toast } from "sonner";

import { apiService } from "@/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Role, UserRole } from "@/frontend-types";

interface AssignRolesProps {
  userId: number;
  onRolesUpdated?: () => void;
}

export function AssignRoles({ userId, onRolesUpdated }: AssignRolesProps) {
  const [roles, setRoles] = useState<Role[]>([]);
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch all active roles
      const rolesResponse = await apiService.get("admin/roles");
      const allRoles = rolesResponse.data.filter((role: Role) => role.active);
      setRoles(allRoles);

      // Fetch user's current roles
      const userRolesResponse = await apiService.get(
        `admin/roles/user/${userId}`
      );
      const currentUserRoles = userRolesResponse.data;
      setUserRoles(currentUserRoles);

      // Set selected role IDs
      const currentRoleIds = currentUserRoles.map((ur: UserRole) => ur.roleId);
      setSelectedRoleIds(currentRoleIds);
    } catch (error) {
      toast.error("Failed to load roles", {
        description: (error as Error).message,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchData();
    }
  }, [userId]);

  const handleRoleToggle = (roleId: number, checked: boolean) => {
    if (checked) {
      setSelectedRoleIds((prev) => [...prev, roleId]);
    } else {
      setSelectedRoleIds((prev) => prev.filter((id) => id !== roleId));
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      await apiService.post("admin/roles/assign-to-user", {
        userId,
        roleIds: selectedRoleIds,
      });

      toast.success("User roles updated successfully");

      if (onRolesUpdated) {
        onRolesUpdated();
      }

      // Refresh data
      await fetchData();
    } catch (error) {
      toast.error("Failed to update user roles", {
        description: (error as Error).message,
      });
    } finally {
      setSaving(false);
    }
  };

  const hasChanges = () => {
    const currentRoleIds = userRoles.map((ur) => ur.roleId).sort();
    const newRoleIds = [...selectedRoleIds].sort();

    return JSON.stringify(currentRoleIds) !== JSON.stringify(newRoleIds);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>User Roles</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600">Loading roles...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Roles</CardTitle>
        <div className="text-sm text-gray-600">
          Assign roles to grant specific permissions to this user
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {roles.length === 0 ? (
          <div className="text-sm text-gray-600">No roles available</div>
        ) : (
          <div className="space-y-3">
            {roles.map((role) => (
              <div key={role.id} className="flex items-start space-x-3">
                <Checkbox
                  id={`role-${role.id}`}
                  checked={selectedRoleIds.includes(role.id)}
                  onCheckedChange={(checked) =>
                    handleRoleToggle(role.id, checked as boolean)
                  }
                />
                <div className="flex-1">
                  <Label
                    htmlFor={`role-${role.id}`}
                    className="text-sm font-medium cursor-pointer"
                  >
                    {role.name}
                    {role.isSystemRole && (
                      <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                        System
                      </span>
                    )}
                  </Label>
                  {role.description && (
                    <div className="text-sm text-gray-600 mt-1">
                      {role.description}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {hasChanges() && (
          <div className="pt-4 border-t">
            <Button onClick={handleSave} disabled={saving} className="w-full">
              {saving ? "Saving..." : "Save Role Changes"}
            </Button>
          </div>
        )}

        {userRoles.length > 0 && (
          <div className="pt-4 border-t">
            <div className="text-sm font-medium mb-2">Current Assignments:</div>
            <div className="space-y-2">
              {userRoles.map((userRole) => (
                <div key={userRole.roleId} className="text-sm text-gray-600">
                  <span className="font-medium">{userRole.role?.name}</span>
                  <span className="mx-2">•</span>
                  <span>
                    Assigned{" "}
                    {new Date(userRole.assignedAt).toLocaleDateString()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
