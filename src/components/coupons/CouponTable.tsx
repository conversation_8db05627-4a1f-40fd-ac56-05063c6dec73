"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatCurrency, formatDate } from "@/lib/utils";
import { Coupon } from "@/frontend-types";
import { Circle, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Button } from "../ui/button";

export interface CouponTableProps {
  coupons: Coupon[];
  onClick: (coupon: Coupon) => void;
  onEdit: (coupon: Coupon) => void;
  onDelete: (coupon: Coupon) => void;
}

export function CouponTable({
  coupons,
  onClick,
  onEdit,
  onDelete,
}: CouponTableProps) {
  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Code</TableHead>
            <TableHead>Discount</TableHead>
            <TableHead>Usage limit</TableHead>
            <TableHead>Per User limit</TableHead>
            <TableHead>Max Discount</TableHead>
            <TableHead>Minimum order</TableHead>
            <TableHead>Active</TableHead>
            <TableHead>Expires At</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {coupons.map((item) => (
            <TableRow
              key={item.id}
              className="cursor-pointer"
              onClick={() => onClick(item)}
            >
              <TableCell>{item.code}</TableCell>
              <TableCell>{item.discountValue}</TableCell>
              <TableCell>{item.usageLimit}</TableCell>
              <TableCell>{item.perUserLimit}</TableCell>
              <TableCell>{formatCurrency(item.maxDiscount ?? 0)}</TableCell>
              <TableCell>{formatCurrency(item.minOrderValue ?? 0)}</TableCell>
              <TableCell>
                {item.isActive ? (
                  <Circle className="text-green-600" />
                ) : (
                  <Circle className="text-red-600" />
                )}
              </TableCell>
              <TableCell>{formatDate(item.expiresAt)}</TableCell>
              <TableCell>{formatDate(item.createdAt)}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onEdit(item)}>
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-red-500"
                      onClick={() => {
                        onDelete(item);
                      }}
                    >
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
