import { Coupon } from "@/frontend-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { But<PERSON> } from "../ui/button";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  // FormDescription,
  Form,
} from "../ui/form";
import { Separator } from "../ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Input } from "../ui/input";
import { useEffect } from "react";
import { Switch } from "../ui/switch";

const createCouponSchema = z.object({
  id: z.number().int(),
  code: z.string(),
  discountValue: z.number().min(0),
  discountType: z.enum(["FLAT", "PERCENTAGE"]),
  minOrderValue: z.number().optional(),
  maxDiscount: z.number().optional(),
  usageLimit: z.number().optional(),
  perUserLimit: z.number().optional(),
  expiresAt: z.date(),
  isActive: z.boolean(),
});

type FormValues = z.infer<typeof createCouponSchema>;

export interface CouponEditorProps {
  coupon?: Coupon;
  isLoading: boolean;
  onEdit?: (coupon: Coupon) => void;
  onCreate?: (coupon: Coupon) => void;
}

export function CouponEditor(params: CouponEditorProps) {
  const router = useRouter();

  const form = useForm<FormValues>({
    resolver: zodResolver(createCouponSchema),
    defaultValues: {
      id: params.coupon?.id ?? 0,
      code: params.coupon?.code ?? "",
      discountValue: params.coupon?.discountValue,
      discountType: params.coupon?.discountType ?? "FLAT",
      minOrderValue: params.coupon?.minOrderValue,
      maxDiscount: params.coupon?.maxDiscount,
      usageLimit: params.coupon?.usageLimit,
      perUserLimit: params.coupon?.perUserLimit,
      expiresAt: params.coupon?.expiresAt,
      isActive: params.coupon?.isActive,
    },
  });

  useEffect(() => {
    form.reset({
      id: params.coupon?.id ?? 0,
      code: params.coupon?.code ?? "",
      discountValue: params.coupon?.discountValue,
      discountType: params.coupon?.discountType ?? "FLAT",
      minOrderValue: params.coupon?.minOrderValue,
      maxDiscount: params.coupon?.maxDiscount,
      usageLimit: params.coupon?.usageLimit,
      perUserLimit: params.coupon?.perUserLimit,
      expiresAt: params.coupon?.expiresAt,
      isActive: params.coupon?.isActive,
    });
  }, [params.coupon, form, form.reset]);

  const onSubmit = async (data: FormValues) => {
    const coupon: Coupon = {
      createdAt: params.coupon?.createdAt ?? new Date(),
      updatedAt: params.coupon?.createdAt ?? new Date(),
      id: data.id,
      code: data.code,
      discountValue: data.discountValue,
      discountType: data.discountType,
      minOrderValue: data.minOrderValue,
      maxDiscount: data.maxDiscount,
      usageLimit: data.usageLimit,
      perUserLimit: data.perUserLimit,
      expiresAt: data.expiresAt,
      isActive: data.isActive,
    };

    if (params.coupon) {
      if (params.onEdit) params.onEdit(coupon);
    } else {
      if (params.onCreate) params.onCreate(coupon);
    }
  };

  return (
    <div>
      <div className="flex flex-row mb-4 items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/coupons")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="font-bold text-2xl">
          {params.coupon ? "Edit" : "Add"} Coupon
        </h1>
      </div>
      <Separator className="my-4" />

      <div className="max-w-2xl">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Code</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="discountValue"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Discount Value</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="discountType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Discount Type</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select the discount type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {["FLAT", "PERCENTAGE"].map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="minOrderValue"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum order</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="maxDiscount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Max discount</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="usageLimit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Usage limit</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="perUserLimit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Per user limit</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Active</FormLabel>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* <FormField
              control={form.control}
              name="bannerUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Banner URL</FormLabel>
                  <FormControl>
                    <Input {...field} type="url" />
                  </FormControl>
                  <FormDescription>
                    Optional. URL for the category banner image.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="iconUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icon URL</FormLabel>
                  <FormControl>
                    <Input {...field} type="url" />
                  </FormControl>
                  <FormDescription>
                    Optional. URL for the category icon.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            /> */}

            <Button type="submit" disabled={params.isLoading}>
              {params.isLoading
                ? "Creating..."
                : `${params.coupon ? "Edit" : "Create"} Coupon`}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}
