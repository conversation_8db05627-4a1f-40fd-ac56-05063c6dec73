"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Country, Supplier } from "@/frontend-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const supplierSchema = z.object({
  id: z.number().int(),
  name: z.string().min(1, "Name is required"),
  email: z.string().email(),
  phone: z.string().min(1),
  address: z.string().min(1),
  countryId: z.number().min(1),
});

type FormValues = z.infer<typeof supplierSchema>;

export interface SupplierEditorProps {
  supplier?: Supplier;
  countries: Country[];
  isLoading: boolean;
  onEdit?: (supplier: Supplier) => void;
  onCreate?: (supplier: Supplier) => void;
}

export function SupplierEditor(params: SupplierEditorProps) {
  const router = useRouter();

  const form = useForm<FormValues>({
    resolver: zodResolver(supplierSchema),
    defaultValues: {
      id: params.supplier?.id ?? 0,
      name: params.supplier?.name ?? "",
      email: params.supplier?.email ?? "",
      phone: params.supplier?.phone ?? "",
      address: params.supplier?.address ?? "",
      countryId:
        params.supplier?.country.id ?? (undefined as unknown as number),
    },
  });

  useEffect(() => {
    form.reset({
      id: params.supplier?.id ?? 0,
      name: params.supplier?.name ?? "",
      email: params.supplier?.email ?? "",
      phone: params.supplier?.phone ?? "",
      address: params.supplier?.address ?? "",
      countryId:
        params.supplier?.country.id ?? (undefined as unknown as number),
    });
  }, [params.supplier, form]);

  const onSubmit = (data: FormValues) => {
    const supplier: Supplier = {
      ...data,
      id: data.id,
      country: params.countries.find((c) => c.id === data.countryId)!,
      createdAt: params.supplier?.createdAt ?? new Date(),
      updatedAt: new Date(),
    };

    if (params.supplier && params.onEdit) params.onEdit(supplier);
    else if (!params.supplier && params.onCreate) params.onCreate(supplier);
  };

  return (
    <div>
      <div className="flex flex-row mb-4 items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/suppliers")}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="font-bold text-2xl">
          {params.supplier ? "Edit" : "Add"} Supplier
        </h1>
      </div>
      <Separator className="my-4" />

      <div className="max-w-2xl">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input {...field} type="email" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="countryId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Country</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(Number(value))} // ✅ convert to number
                    value={field.value?.toString() ?? ""}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a country" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {params.countries.map((country) => (
                        <SelectItem
                          key={country.id}
                          value={country.id.toString()} // 👈 must be string
                        >
                          {country.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" disabled={params.isLoading}>
              {params.isLoading
                ? `${params.supplier ? "Saving..." : "Creating..."}`
                : `${params.supplier ? "Save Changes" : "Create Supplier"}`}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}
