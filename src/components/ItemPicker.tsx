import { useState, useEffect, ReactNode, useCallback } from "react";
import { Search } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { PaginatedResponse, PaginationData } from "@/frontend-types";

export interface Column<T> {
  accessorKey?: keyof T;
  id?: string;
  header: string;
  render?: (item: T) => ReactNode;
}

export type SelectionMode = "single" | "multiple";

export interface ItemPickerProps<T> {
  title?: string;
  selectionMode?: SelectionMode;
  renderTrigger?: () => ReactNode;
  columns: Column<T>[];
  fetchItems: (
    page: number,
    pageSize: number,
    searchQuery: string
  ) => Promise<PaginatedResponse<T>>;
  onConfirmSelection: (selected: T | T[]) => void;
  initialSelectedItems?: T[];
  keyField?: keyof T;
  searchPlaceholder?: string;
}

export default function ItemPicker<T>({
  title = "Select Items",
  selectionMode = "single",
  renderTrigger,
  columns,
  fetchItems,
  onConfirmSelection,
  initialSelectedItems = [],
  keyField = "id" as keyof T,
  searchPlaceholder = "Search...",
}: ItemPickerProps<T>) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [items, setItems] = useState<T[]>([]);
  const [selectedItems, setSelectedItems] = useState<T[]>(
    initialSelectedItems || []
  );
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    pageSize: 5,
    totalPages: 1,
  });

  const loadItems = useCallback(
    async (page = 1, query = searchQuery) => {
      setLoading(true);
      try {
        const response = await fetchItems(page, pagination.pageSize, query);
        setItems(response.data);
        setPagination({
          total: response.total,
          page: response.page,
          pageSize: response.pageSize,
          totalPages: response.totalPages,
        });
      } catch (error) {
        console.error("Failed to fetch items:", error);
      } finally {
        setLoading(false);
      }
    },
    [fetchItems, pagination.pageSize, searchQuery]
  );

  useEffect(() => {
    if (open) {
      loadItems(1);
    }
  }, [loadItems, open]);

  const handleSearch = () => {
    loadItems(1, searchQuery);
  };

  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const handlePageChange = (page: number) => {
    loadItems(page);
  };

  const handleSelectItem = (item: T) => {
    if (selectionMode === "single") {
      setSelectedItems([item]);
    } else {
      const itemKey = item[keyField];
      const isSelected = selectedItems.some((i) => i[keyField] === itemKey);

      if (isSelected) {
        setSelectedItems(selectedItems.filter((i) => i[keyField] !== itemKey));
      } else {
        setSelectedItems([...selectedItems, item]);
      }
    }
  };

  const isItemSelected = (item: T): boolean => {
    return selectedItems.some((i) => i[keyField] === item[keyField]);
  };

  const handleConfirm = () => {
    onConfirmSelection(
      selectionMode === "single" ? selectedItems[0] : selectedItems
    );
    setOpen(false);
  };

  const handleCancel = () => {
    setSelectedItems(initialSelectedItems || []);
    setOpen(false);
  };

  // Generate pagination items
  const renderPaginationItems = () => {
    const items = [];
    const maxVisiblePages = 5;

    // Always show first page
    items.push(
      <PaginationItem key="first">
        <PaginationLink
          onClick={() => handlePageChange(1)}
          isActive={pagination.page === 1}
          className="cursor-pointer"
        >
          1
        </PaginationLink>
      </PaginationItem>
    );

    // Show ellipsis if needed
    if (pagination.page > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Calculate range of visible page numbers
    let startPage = Math.max(2, pagination.page - 1);
    let endPage = Math.min(pagination.totalPages - 1, pagination.page + 1);

    // Adjust range to show maxVisiblePages - 2 page numbers (excluding first and last)
    if (endPage - startPage < maxVisiblePages - 3) {
      if (pagination.page < pagination.totalPages / 2) {
        endPage = Math.min(
          pagination.totalPages - 1,
          startPage + maxVisiblePages - 3
        );
      } else {
        startPage = Math.max(2, endPage - (maxVisiblePages - 3));
      }
    }

    // Add middle page numbers
    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <PaginationItem key={i}>
          <PaginationLink
            onClick={() => handlePageChange(i)}
            isActive={pagination.page === i}
            className="cursor-pointer"
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    // Show ellipsis if needed
    if (pagination.page < pagination.totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Always show last page if there's more than one page
    if (pagination.totalPages > 1) {
      items.push(
        <PaginationItem key="last">
          <PaginationLink
            onClick={() => handlePageChange(pagination.totalPages)}
            isActive={pagination.page === pagination.totalPages}
            className="cursor-pointer"
          >
            {pagination.totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {renderTrigger ? (
          renderTrigger()
        ) : (
          <Button variant="outline">Select Item</Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <div className="flex items-center space-x-2 mb-4">
          <div className="relative flex-1">
            <Input
              placeholder={searchPlaceholder}
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={handleSearchKeyDown}
            />
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          </div>
          <Button size="sm" onClick={handleSearch}>
            Search
          </Button>
        </div>

        {selectedItems.length > 0 && selectionMode === "multiple" && (
          <div className="flex flex-wrap gap-2 mb-4">
            {selectedItems.map((item) => (
              <Badge
                key={String(item[keyField])}
                variant="secondary"
                className="px-2 py-1"
              >
                {typeof columns[0].render === "function"
                  ? columns[0].render(item)
                  : columns[0].accessorKey
                  ? String(item[columns[0].accessorKey])
                  : "Item"}
                <span
                  className="ml-1 cursor-pointer"
                  onClick={() => handleSelectItem(item)}
                >
                  ×
                </span>
              </Badge>
            ))}
          </div>
        )}

        <div className="border rounded-md">
          <div className="w-full overflow-auto">
            {/* Table Header */}
            <div className="bg-muted/50">
              <div className="flex w-full">
                <div className="w-12 p-2 flex items-center justify-center border-b"></div>
                {columns.map((column) => (
                  <div
                    key={String(column.accessorKey) || column.id}
                    className="p-2 py-3 text-sm font-medium text-left flex-1 border-b"
                  >
                    {column.header}
                  </div>
                ))}
              </div>
            </div>

            {/* Table Body */}
            <div>
              {loading ? (
                <div className="text-center py-8">Loading...</div>
              ) : items.length === 0 ? (
                <div className="text-center py-8">No items found</div>
              ) : (
                items.map((item) => (
                  <div
                    key={String(item[keyField])}
                    className="flex w-full cursor-pointer hover:bg-muted/50 group"
                    onClick={() => handleSelectItem(item)}
                  >
                    <div className="w-12 p-2 flex items-center justify-center border-b">
                      <Checkbox
                        checked={isItemSelected(item)}
                        onCheckedChange={() => handleSelectItem(item)}
                        aria-label="Select item"
                      />
                    </div>
                    {columns.map((column) => (
                      <div
                        key={String(column.accessorKey) || column.id}
                        className="p-2 py-3 text-sm flex-1 border-b"
                      >
                        {column.render
                          ? column.render(item)
                          : column.accessorKey
                          ? String(item[column.accessorKey])
                          : null}
                      </div>
                    ))}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {pagination.totalPages > 1 && (
          <Pagination className="mt-4">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    handlePageChange(Math.max(1, pagination.page - 1))
                  }
                  className={
                    pagination.page === 1
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>

              {renderPaginationItems()}

              <PaginationItem>
                <PaginationNext
                  onClick={() =>
                    handlePageChange(
                      Math.min(pagination.totalPages, pagination.page + 1)
                    )
                  }
                  className={
                    pagination.page === pagination.totalPages
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}

        <DialogFooter className="mt-4 gap-2">
          <div className="text-sm text-muted-foreground">
            {pagination.total} items total
          </div>
          <div className="flex-1"></div>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={
              selectionMode === "single" ? selectedItems.length === 0 : false
            }
          >
            {selectionMode === "single" ? "Select" : "Select Items"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
