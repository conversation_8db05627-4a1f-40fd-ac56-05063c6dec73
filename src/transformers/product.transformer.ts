import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { InventoryService } from 'src/admin/inventories/inventories.service';
import {
  ProductQueryResult,
  ProductResponse,
  ProductVariationResponse,
  ProductWithStockResponse,
} from 'src/types';

@Injectable()
export class ProductTransformer {
  constructor(private readonly inventoryService: InventoryService) {}

  async getProductWithStockResponse(
    product: ProductQueryResult,
    warehouseId?: number,
    variationId?: number,
    selectedVariationFromOptions?: any,
  ): Promise<ProductWithStockResponse> {
    try {
      if (!warehouseId) {
        throw new Error('Warehouse ID is required');
      }

      // Check if product has variations
      if (product.hasVariations) {
        // Process attributes for the selection UI
        const attributes =
          product.attributes?.map((attr) => ({
            id: attr.id,
            name: attr.name,
            options: attr.options.map((opt) => ({
              id: opt.id,
              name: opt.name,
              colorCode: opt.colorCode,
              imageUrl: opt.imageUrl,
            })),
          })) || [];

        // If we have a selected variation from option IDs
        if (selectedVariationFromOptions) {
          try {
            // Try to get inventory for the specific variation
            // Create a default inventory object with proper values
            let inventory: {
              quantity: number;
              sellingPrice: {
                toFixed: (digits?: number) => string;
                toNumber: () => number;
              };
            } = {
              quantity: 0,
              sellingPrice: {
                toFixed: (_?: number) => '0.00',
                toNumber: () => 0,
              },
            };

            try {
              inventory =
                await this.inventoryService.getProductInventoryByWarehouse(
                  product.id,
                  warehouseId,
                  selectedVariationFromOptions.id,
                );
            } catch (inventoryError) {
              // If no inventory found, we'll use the default inventory object
              console.error(
                'No inventory found for variation, using defaults:',
                inventoryError,
              );
            }

            // Get the variation details from the inventory service
            let variation: ProductVariationResponse | undefined;
            try {
              variation = await this.inventoryService
                .getProductVariationsWithStock(product.id, warehouseId)
                .then((variations) =>
                  variations.find(
                    (v) => v.id === selectedVariationFromOptions.id,
                  ),
                );
            } catch (variationError) {
              // If error getting variation with stock, continue with raw variation
              console.error(
                'Error getting variation with stock:',
                variationError,
              );
            }

            // Get the original price from inventory
            const originalPrice = inventory.sellingPrice.toFixed(2);

            // Calculate discounted price if applicable
            let discountedPrice = originalPrice; // Start with original price
            if (product.discountType && product.discountValue) {
              let calculatedDiscount =
                product.discountType === 'PERCENTAGE'
                  ? inventory.sellingPrice.toNumber() *
                    (1 - (product.discountValue ?? 0) / 100)
                  : inventory.sellingPrice.toNumber() -
                    (product.discountValue ?? 0);

              // Ensure the discounted price is never negative
              calculatedDiscount = Math.max(0, calculatedDiscount);
              discountedPrice = calculatedDiscount.toFixed(2);
            }

            // If product is out of stock, set price to 0
            if (inventory.quantity <= 0) {
              discountedPrice = '0.00';
            }

            // First create the variation response with the original price
            let variationResponse: ProductVariationResponse;

            if (variation) {
              // If we have a variation from inventory service, map it
              variationResponse = this.mapVariationToResponse(variation);
            } else {
              // Otherwise create from the database variation
              variationResponse = this.createVariationResponseFromRaw(
                selectedVariationFromOptions as Prisma.ProductVariationGetPayload<{
                  include: {
                    options: {
                      include: {
                        option: {
                          include: {
                            attribute: true;
                          };
                        };
                      };
                    };
                    media: true;
                  };
                }>,
                inventory.quantity,
                originalPrice, // Use original price here
                inventory.quantity > 0,
              );
            }

            // Now update the variation response with the correct prices
            variationResponse.originalPrice = originalPrice;
            variationResponse.price = discountedPrice;

            // Get all variations with stock information for the complete list
            const allVariations =
              await this.inventoryService.getProductVariationsWithStock(
                product.id,
                warehouseId,
              );

            // Return product with selected variation and its stock info
            return {
              ...(await this.getProductResponse(product)),
              // Override base product stock info with variation stock info
              quantity: inventory.quantity,
              originalPrice: originalPrice,
              price: discountedPrice,
              inStock: inventory.quantity > 0,
              selectedVariation: variationResponse,
              variations: allVariations.map((v) =>
                this.mapVariationToResponse(v),
              ), // Include all variations
              attributes: attributes,
              productPolicies: product.productPolicies?.map((pp) => {
                return {
                  ...pp,
                  details: Prisma.parseJson<Record<string, any>>(pp.details),
                };
              }),
            } satisfies ProductWithStockResponse;
          } catch (error) {
            // If there's an error getting inventory for the variation,
            // continue with the normal flow
            console.error(
              'Error getting inventory for selected variation:',
              error,
            );
          }
        }

        // If product has variations but no variationId is provided, get all variations
        if (!variationId) {
          // Get all variations with stock information
          const variations =
            await this.inventoryService.getProductVariationsWithStock(
              product.id,
              warehouseId,
            );

          // Find the variation with the lowest price that has stock
          let lowestPrice: number | null = null;
          let anyInStock = false;

          // Sort variations by price (lowest first)
          const sortedVariations = [...variations].sort((a, b) => {
            const priceA = parseFloat(a.price || '0');
            const priceB = parseFloat(b.price || '0');
            return priceA - priceB;
          });

          // Find the first variation with stock
          for (const variation of sortedVariations) {
            if (variation.inStock) {
              anyInStock = true;
              lowestPrice = parseFloat(variation.price || '0');
              break; // Take the first one with stock
            }
          }

          // Format the price or use default
          const originalPrice =
            lowestPrice !== null ? lowestPrice.toFixed(2) : '0.00';

          // Calculate discounted price if applicable
          let discountedPrice = originalPrice;
          if (
            lowestPrice !== null &&
            product.discountType &&
            product.discountValue
          ) {
            let calculatedDiscount =
              product.discountType === 'PERCENTAGE'
                ? lowestPrice * (1 - (product.discountValue ?? 0) / 100)
                : lowestPrice - (product.discountValue ?? 0);

            // Ensure the discounted price is never negative
            calculatedDiscount = Math.max(0, calculatedDiscount);
            discountedPrice = calculatedDiscount.toFixed(2);
          }

          // If no product is in stock, set price to 0
          if (!anyInStock) {
            discountedPrice = '0.00';
          }

          // Return the product with variations and attributes
          return {
            ...(await this.getProductResponse(product)),
            quantity: 0, // Base product doesn't have its own quantity
            originalPrice: originalPrice, // Lowest price among variations
            price: discountedPrice, // Apply discount to the lowest price
            inStock: anyInStock, // True if any variation has stock
            variations: variations.map((v) => this.mapVariationToResponse(v)),
            attributes: attributes,
            productPolicies: product.productPolicies?.map((pp) => {
              return {
                ...pp,
                details: Prisma.parseJson<Record<string, any>>(pp.details),
              };
            }),
          } satisfies ProductWithStockResponse;
        } else {
          // Get inventory for the specific variation
          const inventory =
            await this.inventoryService.getProductInventoryByWarehouse(
              product.id,
              warehouseId,
              variationId,
            );

          // Get all variations with stock information
          const allVariations =
            await this.inventoryService.getProductVariationsWithStock(
              product.id,
              warehouseId,
            );

          // Find the selected variation
          const selectedVariation = allVariations.find(
            (v) => v.id === variationId,
          );

          // Calculate original price and discounted price
          const originalPrice = inventory.sellingPrice.toFixed(2);
          let discountedPrice = originalPrice;

          if (product.discountType && product.discountValue) {
            let calculatedDiscount =
              product.discountType === 'PERCENTAGE'
                ? inventory.sellingPrice.toNumber() *
                  (1 - (product.discountValue ?? 0) / 100)
                : inventory.sellingPrice.toNumber() -
                  (product.discountValue ?? 0);

            // Ensure the discounted price is never negative
            calculatedDiscount = Math.max(0, calculatedDiscount);
            discountedPrice = calculatedDiscount.toFixed(2);
          }

          // If product is out of stock, set price to 0
          if (inventory.quantity <= 0) {
            discountedPrice = '0.00';
          }

          return {
            ...(await this.getProductResponse(product)),
            quantity: inventory.quantity,
            originalPrice: originalPrice,
            price: discountedPrice,
            inStock: inventory.quantity > 0,
            selectedVariation: selectedVariation
              ? this.mapVariationToResponse(selectedVariation)
              : undefined,
            variations: allVariations.map((v) =>
              this.mapVariationToResponse(v),
            ), // Include all variations
            attributes: attributes,
            productPolicies: product.productPolicies?.map((pp) => {
              return {
                ...pp,
                details: Prisma.parseJson<Record<string, any>>(pp.details),
              };
            }),
          } satisfies ProductWithStockResponse;
        }
      } else {
        // Regular product without variations
        const inventory =
          await this.inventoryService.getProductInventoryByWarehouse(
            product.id,
            warehouseId,
          );

        // Process attributes for the selection UI (even for regular products)
        const attributes =
          product.attributes?.map((attr) => ({
            id: attr.id,
            name: attr.name,
            options: attr.options.map((opt) => ({
              id: opt.id,
              name: opt.name,
              colorCode: opt.colorCode,
              imageUrl: opt.imageUrl,
            })),
          })) || [];

        // Calculate original price
        const originalPrice = inventory.sellingPrice.toFixed(2);

        // Calculate discounted price
        let discountedPrice = originalPrice;
        if (product.discountType && product.discountValue) {
          let calculatedDiscount =
            product.discountType === 'PERCENTAGE'
              ? inventory.sellingPrice.toNumber() *
                (1 - (product.discountValue ?? 0) / 100)
              : inventory.sellingPrice.toNumber() -
                (product.discountValue ?? 0);

          // Ensure the discounted price is never negative
          calculatedDiscount = Math.max(0, calculatedDiscount);
          discountedPrice = calculatedDiscount.toFixed(2);
        }

        // If product is out of stock, set price to 0
        const isInStock = inventory.quantity > 0;
        if (!isInStock) {
          discountedPrice = '0.00';
        }

        return {
          ...(await this.getProductResponse(product)),
          quantity: inventory.quantity,
          originalPrice: originalPrice,
          price: discountedPrice,
          inStock: isInStock,
          attributes: attributes,
          productPolicies: product.productPolicies?.map((pp) => {
            return {
              ...pp,
              details: Prisma.parseJson<Record<string, any>>(pp.details),
            };
          }),
        } satisfies ProductWithStockResponse;
      }
    } catch (error) {
      return {
        ...(await this.getProductResponse(product)),
        quantity: 0,
        originalPrice: '0.00',
        price: '0.00',
        inStock: false,
        attributes:
          product.attributes?.map((attr) => ({
            id: attr.id,
            name: attr.name,
            options: attr.options.map((opt) => ({
              id: opt.id,
              name: opt.name,
              colorCode: opt.colorCode,
              imageUrl: opt.imageUrl,
            })),
          })) || [],
        productPolicies: product.productPolicies?.map((pp) => {
          return {
            ...pp,
            details: Prisma.parseJson<Record<string, any>>(pp.details),
          };
        }),
      } satisfies ProductWithStockResponse;
    }
  }

  async getProductResponse(
    product: ProductQueryResult,
  ): Promise<ProductResponse> {
    const translation = product.productTranslation?.[0];
    const category = {
      ...product.category,
      name:
        product.category.categoryTranslation?.[0]?.name ??
        product.category.name,
    };

    // Get product policies if they exist
    const productPolicies = product.productPolicies;

    return {
      ...product,
      name: translation?.name ?? product.name,
      description: translation?.description ?? product.description,
      category: category,
      productPolicies: productPolicies,
    };
  }

  /**
   * Maps a ProductVariationWithStockModel to a ProductVariationResponse
   *
   * @param variation The variation model to map to a response
   * @returns A properly formatted ProductVariationResponse
   * @throws Error if required data is missing
   */
  mapVariationToResponse(
    variation: ProductVariationResponse,
  ): ProductVariationResponse {
    if (!variation) {
      throw new Error('Cannot map undefined or null variation to response');
    }

    // Ensure options are properly defined
    if (!variation.options || !Array.isArray(variation.options)) {
      throw new Error('Variation options are missing or not an array');
    }

    // For the variation response, price is the discounted price and originalPrice is the price before discount
    // Since ProductVariationWithStockModel doesn't have originalPrice, we use price as the original price
    // The actual discounted price should be calculated elsewhere and passed in the price field
    return {
      createdAt: variation.createdAt,
      updatedAt: variation.updatedAt,
      productId: variation.productId,
      id: variation.id,
      barcode: variation.barcode,
      quantity: variation.quantity,
      inStock: variation.inStock,
      price: variation.price || '0.00', // Ensure price is never null
      originalPrice: variation.price || '0.00', // Use price as originalPrice since it's the non-discounted price
      options: variation.options.map((mapping) => {
        if (!mapping.option) {
          throw new Error(
            `Option mapping is missing option data for variation ${variation.id}`,
          );
        }

        return {
          option: {
            id: mapping.option.id,
            name: mapping.option.name,
            colorCode: mapping.option.colorCode,
            imageUrl: mapping.option.imageUrl,
            attribute: mapping.option.attribute
              ? {
                  id: mapping.option.attribute.id,
                  name: mapping.option.attribute.name,
                }
              : {
                  // If attribute is missing, provide default values but log a warning
                  id: 0,
                  name: `Unknown (Option: ${mapping.option.id})`,
                },
          },
        };
      }),
      media: variation.media ?? [],
    };
  }

  /**
   * Creates a ProductVariationResponse from a database variation object
   * This is used when we have a variation from the database but not from the inventory service
   *
   * @param dbVariation The variation object from the database with its relations
   * @param quantity The quantity from inventory
   * @param price The price from inventory
   * @param inStock Whether the variation is in stock
   * @returns A properly formatted ProductVariationResponse
   */
  createVariationResponseFromRaw(
    dbVariation: Prisma.ProductVariationGetPayload<{
      include: {
        options: {
          include: {
            option: {
              include: {
                attribute: true;
              };
            };
          };
        };
        media: true;
      };
    }>,
    quantity: number,
    price: string,
    inStock: boolean,
  ): ProductVariationResponse {
    if (!dbVariation) {
      throw new Error(
        'Cannot create variation response from undefined or null variation',
      );
    }

    return {
      createdAt: dbVariation.createdAt,
      updatedAt: dbVariation.updatedAt,
      productId: dbVariation.productId,
      id: dbVariation.id,
      barcode: dbVariation.barcode,
      quantity: quantity,
      inStock: inStock,
      price: price || '0.00', // Ensure price is never null
      originalPrice: price || '0.00', // Ensure originalPrice is never null
      options: dbVariation.options.map(
        (mapping: {
          option: {
            id: number;
            name: string;
            colorCode: string | null;
            imageUrl: string | null;
            attribute?: {
              id: number;
              name: string;
            } | null;
          };
        }) => {
          return {
            option: {
              id: mapping.option.id,
              name: mapping.option.name,
              colorCode: mapping.option.colorCode,
              imageUrl: mapping.option.imageUrl,
              attribute: mapping.option.attribute
                ? {
                    id: mapping.option.attribute.id,
                    name: mapping.option.attribute.name,
                  }
                : {
                    id: 0,
                    name: 'Unknown',
                  },
            },
          };
        },
      ),
      media: dbVariation.media,
    };
  }
}
