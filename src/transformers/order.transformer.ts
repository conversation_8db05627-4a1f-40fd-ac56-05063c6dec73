import { Injectable } from '@nestjs/common';
import { OrderQueryResult, OrderResponse } from 'src/types';
import { ProductTransformer } from './product.transformer';

@Injectable()
export class OrderTransformer {
  constructor(private readonly productTransformer: ProductTransformer) {}

  async getOrderResponse(
    orderQueryResult: OrderQueryResult,
  ): Promise<OrderResponse> {
    // Create a new object to avoid modifying the original
    const order: any = { ...orderQueryResult };

    // Transform each order item
    order.items = await Promise.all(
      orderQueryResult.items.map(async (orderItem) => {
        // Transform the product
        const transformedProduct =
          await this.productTransformer.getProductResponse(orderItem.product);

        // Create a new order item with the transformed product
        const transformedOrderItem: any = {
          ...orderItem,
          product: transformedProduct,
        };

        // If the order item has a variation, include it in the response
        if (orderItem.variationId && orderItem.variation) {
          transformedOrderItem.variation = {
            id: orderItem.variation.id,
            barcode: orderItem.variation.barcode,
            options: orderItem.variation.options || [],
            media: orderItem.variation.media || [],
          };
        }

        return transformedOrderItem;
      }),
    );

    return order;
  }
}
