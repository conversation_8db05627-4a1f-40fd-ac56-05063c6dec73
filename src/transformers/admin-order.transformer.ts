import { Injectable } from '@nestjs/common';
import { AdminOrderResponse, ProductVariationResponse } from 'src/types';
import { ProductTransformer } from './product.transformer';

@Injectable()
export class AdminOrderTransformer {
  constructor(private readonly productTransformer: ProductTransformer) {}

  async getAdminOrderResponse(
    orderQueryResult: any,
  ): Promise<AdminOrderResponse> {
    // Create a new object to avoid modifying the original
    const order: any = {
      ...orderQueryResult,
      // Ensure user is defined
      user: orderQueryResult.user || {
        id: 0,
        name: 'Unknown User',
        email: '',
        phone: '',
        type: 'USER',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    };

    // Transform each order item
    order.items = await Promise.all(
      orderQueryResult.items.map(async (orderItem) => {
        // Transform the product
        // Make sure category and categoryTranslation are properly defined
        const product = {
          ...orderItem.product,
          category: {
            ...orderItem.product.category,
            categoryTranslation:
              orderItem.product.category?.categoryTranslation || [],
          },
          productTranslation: orderItem.product.productTranslation || [],
        };

        const transformedProduct =
          await this.productTransformer.getProductResponse(product);

        // Create a new order item with the transformed product
        const transformedOrderItem: any = {
          ...orderItem,
          product: transformedProduct,
        };

        // If the order item has a variation, include it in the response with stock information
        if (orderItem.variationId && orderItem.variation) {
          // Get inventory information from the order item's inventory
          const quantity = orderItem.inventory?.quantity || 0;
          const price = orderItem.inventory?.sellingPrice?.toFixed(2) || '0.00';
          const originalPrice =
            orderItem.inventory?.sellingPrice?.toFixed(2) || '0.00';
          const inStock = quantity > 0;

          // Create a properly formatted variation response
          const transformedVariation: ProductVariationResponse = {
            createdAt: orderItem.variation.createdAt,
            updatedAt: orderItem.variation.updatedAt,
            productId: orderItem.variation.productId,
            id: orderItem.variation.id,
            barcode: orderItem.variation.barcode,
            quantity: quantity,
            inStock: inStock,
            price: price,
            originalPrice: originalPrice,
            options:
              orderItem.variation.options.map((mapping) => ({
                option: {
                  id: mapping.option.id,
                  name: mapping.option.name,
                  colorCode: mapping.option.colorCode,
                  imageUrl: mapping.option.imageUrl,
                  attribute: {
                    id: mapping.option.attribute?.id || 0,
                    name: mapping.option.attribute?.name || 'Unknown',
                  },
                },
              })) || [],
            media:
              orderItem.variation.media.map((media) => ({
                id: media.id,
                url: media.url,
                mediaType: media.mediaType,
              })) || [],
          };

          transformedOrderItem.variation = transformedVariation;
        }

        return transformedOrderItem;
      }),
    );

    return order;
  }
}
