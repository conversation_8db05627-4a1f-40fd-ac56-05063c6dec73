import { Injectable } from '@nestjs/common';
import { Context } from 'src/context';
import { PrismaService } from 'src/prisma/prisma.service';
import { RewardPointsService } from 'src/reward-points/reward-points.service';
import {
  UpdateFirebaseTokenDto,
  ChangePasswordDto,
  UpdateNameDto,
  UpdateEmailDto,
} from './users.schema';
import * as bcrypt from 'bcryptjs';
import { Exception } from 'src/exceptions';
import { UserSafe } from 'src/types';

@Injectable()
export class UsersService {
  constructor(
    private readonly context: Context,
    private readonly prisma: PrismaService,
    private readonly rewardPointService: RewardPointsService,
  ) {}

  async getUserDetails() {
    const user = await this.prisma.user.findUniqueOrThrow({
      where: {
        id: this.context.user!.id,
      },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        phoneCountry: true,
        countryId: true,
        type: true,
        profilePicture: true,
        createdAt: true,
        updatedAt: true,
        rewardTier: true,
        rewardTierId: true,
        referralCode: true,
      },
    });

    const points = await this.rewardPointService.getUserRewardPoints(user.id);

    return {
      ...user,
      rewardPoints: points,
    };
  }

  async updateFirebaseToken(dto: UpdateFirebaseTokenDto) {
    const user = await this.prisma.user.update({
      where: {
        id: this.context.user!.id,
      },
      data: {
        firebaseToken: dto.token,
      },
    });

    return {
      token: user.firebaseToken,
    };
  }

  async changePassword(dto: ChangePasswordDto) {
    // Get the current user
    const user = await this.prisma.user.findUniqueOrThrow({
      where: {
        id: this.context.user!.id,
      },
    });

    // Verify the current password
    const isPasswordValid = await bcrypt.compare(
      dto.currentPassword,
      user.password,
    );
    if (!isPasswordValid) {
      throw new Exception('Current password is incorrect', 400);
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(dto.newPassword, 10);

    // Update the password
    await this.prisma.user.update({
      where: {
        id: this.context.user!.id,
      },
      data: {
        password: hashedPassword,
      },
    });

    return {
      message: 'Password updated successfully',
    };
  }

  async updateName(dto: UpdateNameDto) {
    const user = await this.prisma.user.update({
      where: {
        id: this.context.user!.id,
      },
      data: {
        name: dto.name,
      },
      select: {
        id: true,
        name: true,
      },
    });

    return this.getUserDetails();
  }

  async updateEmail(dto: UpdateEmailDto) {
    // Check if email already exists
    const existingUser = await this.prisma.user.findUnique({
      where: {
        email: dto.email,
      },
    });

    if (existingUser && existingUser.id !== this.context.user!.id) {
      throw new Exception('Email already in use', 400);
    }

    const user = await this.prisma.user.update({
      where: {
        id: this.context.user!.id,
      },
      data: {
        email: dto.email,
      },
      select: {
        id: true,
        email: true,
      },
    });

    return this.getUserDetails();
  }

  async getUserDetailsById(userId: number): Promise<UserSafe> {
    const user = await this.prisma.user.findUniqueOrThrow({
      where: {
        id: userId,
      },
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true,
        updatedAt: true,
        type: true,
        phone: true,
        profilePicture: true,
        countryId: true,
        phoneCountry: true,
        rewardTierId: true,
        rewardTier: true,
        referralCode: true,
        referredById: true,
        firebaseToken: true,
        tierExpiresAt: true,
      },
    });

    return user;
  }
}
