import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

const UpdateFirebaseToken = z.object({
  token: z.string(),
});

export class UpdateFirebaseTokenDto extends createZodDto(UpdateFirebaseToken) {}

const ChangePassword = z.object({
  currentPassword: z
    .string()
    .min(6, 'Current password must be at least 6 characters'),
  newPassword: z.string().min(6, 'New password must be at least 6 characters'),
});

export class ChangePasswordDto extends createZodDto(ChangePassword) {}

const UpdateName = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
});

export class UpdateNameDto extends createZodDto(UpdateName) {}

const UpdateEmail = z.object({
  email: z.string().email('Invalid email format'),
});

export class UpdateEmailDto extends createZodDto(UpdateEmail) {}
