import {
  Body,
  Controller,
  Get,
  Patch,
  UseGuards,
  UsePipes,
} from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';
import { AuthGuard } from 'src/auth/auth.guard';
import { UsersService } from './users.service';
import {
  UpdateFirebaseTokenDto,
  ChangePasswordDto,
  UpdateNameDto,
  UpdateEmailDto,
} from './users.schema';
import { User } from 'src/auth/auth.decorator';

@Controller('users')
@UseGuards(AuthGuard)
@UsePipes(ZodValidationPipe)
export class UsersController {
  constructor(private readonly userService: UsersService) {}

  @User()
  @Get()
  async getUserDetails() {
    return this.userService.getUserDetails();
  }

  @User()
  @Patch('firebase')
  async updateFirebaseToken(@Body() dto: UpdateFirebaseTokenDto) {
    return this.userService.updateFirebaseToken(dto);
  }

  @User()
  @Patch('password')
  async changePassword(@Body() dto: ChangePasswordDto) {
    return this.userService.changePassword(dto);
  }

  @User()
  @Patch('name')
  async updateName(@Body() dto: UpdateNameDto) {
    return this.userService.updateName(dto);
  }

  @User()
  @Patch('email')
  async updateEmail(@Body() dto: UpdateEmailDto) {
    return this.userService.updateEmail(dto);
  }
}
