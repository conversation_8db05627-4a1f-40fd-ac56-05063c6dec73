import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class BannersService {
  constructor(private readonly prisma: PrismaService) {}

  async getActiveBanners(lang: string) {
    const language = await this.prisma.language.findUnique({
      where: {
        code: lang,
      },
    });

    return this.prisma.banner.findMany({
      where: {
        active: true,
        OR: [
          {
            languageId: language?.id,
          },
          {
            languageId: null,
          },
        ],
      },
      include: {
        media: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
}
