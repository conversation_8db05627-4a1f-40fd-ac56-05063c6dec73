import { Controller, DefaultValuePipe, Get, Query } from '@nestjs/common';
import { BannersService } from './banners.service';

@Controller('banners')
export class BannersController {
  constructor(private readonly bannerService: BannersService) {}

  @Get()
  async getActiveBanners(
    @Query('lang', new DefaultValuePipe('en')) lang: string,
  ) {
    return this.bannerService.getActiveBanners(lang);
  }
}
