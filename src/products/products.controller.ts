import {
  Controller,
  DefaultValuePipe,
  Get,
  Param,
  ParseFloatPipe,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
  UsePipes,
} from '@nestjs/common';
import { WarehouseType } from '@prisma/client';
import { ZodValidationPipe } from 'nestjs-zod';
import { AuthGuard } from 'src/auth/auth.guard';
import { ProductsService } from './products.service';

@Controller('products')
@UseGuards(AuthGuard)
@UsePipes(ZodValidationPipe)
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Get()
  async getProducts(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
    @Query('lat') lat: string,
    @Query('long') long: string,
    @Query('warehouseType') warehouseType: WarehouseType,
    @Query('lang', new DefaultValuePipe('en')) lang: string,
    @Query(
      'categoryId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    categoryId?: number,
    @Query('search', new DefaultValuePipe(undefined)) search?: string,
    @Query(
      'overrideWarehouseId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideWarehouseId?: number,
  ) {
    return this.productsService.getProducts({
      page,
      pageSize,
      categoryId,
      userLocation: { lat: +lat, long: +long },
      warehouseType,
      lang,
      search,
      overrideWarehouseId,
    });
  }

  @Get(':id/related')
  async getRelatedProducts(
    @Param('id', ParseIntPipe) id: number,
    @Query('lat') lat: number,
    @Query('long') long: number,
    @Query('warehouseType') warehouseType: WarehouseType,
    @Query('lang', new DefaultValuePipe('en')) lang: string,
  ) {
    return this.productsService.getRelatedProducts({
      id: id,
      userLocation: { lat, long },
      warehouseType,
      lang: lang,
    });
  }

  @Post(':id/subscribe')
  async subscribeToProductAvailability(
    @Param('id', ParseIntPipe) productId: number,
  ) {
    return this.productsService.subscribeToProductAvailability(productId);
  }

  @Get(':slug')
  async getProduct(
    @Param('slug') slug: string,
    @Query('lat', ParseFloatPipe) lat: number,
    @Query('long', ParseFloatPipe) long: number,
    @Query('warehouseType') warehouseType: WarehouseType,
    @Query('lang', new DefaultValuePipe('en')) lang: string,
    @Query(
      'variationId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    variationId?: number,
    @Query('optionIds') optionIdsString?: string,
    @Query(
      'overrideWarehouseId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideWarehouseId?: number,
  ) {
    // Parse option IDs if provided
    let optionIds: number[] | undefined;
    if (optionIdsString) {
      optionIds = optionIdsString
        .split(',')
        .map((id) => parseInt(id.trim(), 10))
        .filter((id) => !isNaN(id));

      if (optionIds.length === 0) {
        optionIds = undefined;
      }
    }

    return this.productsService.getProduct({
      slug: slug,
      userLocation: { lat, long },
      warehouseType,
      lang: lang,
      variationId,
      optionIds,
      overrideWarehouseId,
    });
  }
}
