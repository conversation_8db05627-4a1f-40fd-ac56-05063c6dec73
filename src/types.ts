import {
  Category,
  Language,
  Media,
  Prisma,
  User,
  Warehouse,
} from '@prisma/client';

declare module '@prisma/client' {
  export namespace Prisma {
    function parseJson<T = Record<string, any>>(json: Prisma.JsonValue): T;
  }
}

Prisma.parseJson = <
  T = string | number | boolean | Record<string, any> | Array<any> | null,
>(
  json: Prisma.JsonValue,
): T => {
  return json as T;
};

export type UserSafe = Omit<User, 'password'>;

export interface Coordinate {
  lat: number;
  long: number;
}

export interface MulterFile {
  originalname: string;
  mimetype: string;
  buffer: Buffer;
  size: number;
}

export type ProductQueryResult = Prisma.ProductGetPayload<{
  include: {
    media: true;
    thumbnail: true;
    productTranslation: true;
    category: {
      include: {
        categoryTranslation: true;
      };
    };
    productPolicies: {
      include: {
        productPolicyType: true;
      };
    };
  };
}> & {
  attributes?: Array<{
    id: number;
    name: string;
    options: Array<{
      id: number;
      name: string;
      colorCode: string | null;
      imageUrl: string | null;
    }>;
  }>;
};

export type ProductResponse = Prisma.ProductGetPayload<{
  include: {
    media: true;
    thumbnail: true;
    category: true;
    productPolicies: {
      include: {
        productPolicyType: true;
      };
    };
  };
}>;

export type ProductVariationResponse = {
  createdAt: Date;
  updatedAt: Date;
  id: number;
  productId: number;
  barcode: string;
  quantity: number;
  inStock: boolean;
  price: string | null;
  originalPrice?: string; // Added to match base product structure
  options: {
    option: {
      id: number;
      name: string;
      colorCode: string | null;
      imageUrl: string | null;
      attribute: {
        id: number;
        name: string;
      };
    };
  }[];
  media: Media[];
};

/**
 * Interface representing a Product Variation with stock information
 * This combines data from both the product variation and inventory contexts
 */

export type ProductAttributeResponse = {
  id: number;
  name: string;
  options: {
    id: number;
    name: string;
    colorCode: string | null;
    imageUrl: string | null;
  }[];
};

export type ProductWithStockResponse = ProductResponse & {
  quantity: number;
  originalPrice: string;
  price: string;
  inStock: boolean;
  hasVariations?: boolean;
  variations?: ProductVariationResponse[];
  selectedVariation?: ProductVariationResponse;
  attributes?: ProductAttributeResponse[];
  productPolicies?: ProductPolicy[] | null;
};

export type OrderQueryResult = Prisma.OrderGetPayload<{
  include: {
    items: {
      include: {
        product: {
          include: {
            media: true;
            thumbnail: true;
            category: {
              include: {
                categoryTranslation: true;
              };
            };
            productTranslation: true;
            productPolicies: {
              include: {
                productPolicyType: true;
              };
            };
          };
        };
        variation: {
          include: {
            options: {
              include: {
                option: {
                  include: {
                    attribute: true;
                  };
                };
              };
            };
            media: true;
          };
        };
      };
    };
    address: {
      include: {
        country: true;
      };
    };
    orderStatusHistory: true;
    deliveryDriver: {
      select: {
        id: true;
        name: true;
        email: true;
        createdAt: true;
        updatedAt: true;
        type: true;
        phone: true;
        profilePicture: true;
        countryId: true;
        phoneCountry: true;
        rewardTierId: true;
        rewardTier: true;
        referralCode: true;
        referredById: true;
        firebaseToken: true;
        tierExpiresAt: true;
      };
    };
    warehouse: true;
  };
}>;

export type OrderResponse = Prisma.OrderGetPayload<{
  include: {
    items: {
      include: {
        product: {
          include: {
            thumbnail: true;
            media: true;
            category: true;
          };
        };
        variation: {
          include: {
            options: {
              include: {
                option: {
                  include: {
                    attribute: true;
                  };
                };
              };
            };
            media: true;
          };
        };
      };
    };
    address: {
      include: {
        country: true;
      };
    };
    orderStatusHistory: true;
    warehouse: true;
  };
}> & {
  deliveryDriver?: UserSafe;
  user?: UserSafe;
};

// Extended order response type for admin, staff, and delivery driver interfaces
export type AdminOrderResponse = Prisma.OrderGetPayload<{
  include: {
    items: {
      include: {
        product: {
          include: {
            media: true;
            category: {
              include: {
                categoryTranslation: true;
              };
            };
            productTranslation: true;
          };
        };
        variation: {
          include: {
            options: {
              include: {
                option: {
                  include: {
                    attribute: true;
                  };
                };
              };
            };
            media: true;
          };
        };
        inventory: true;
      };
    };
    address: {
      include: {
        country: true;
      };
    };
    orderStatusHistory: true;
    warehouse: true;
  };
}> & {
  user: UserSafe;
};

export type CartItemResponse = {
  product: ProductWithStockResponse;
  itemTotal: string;
  originalTotal: string;
  gstAmount: string;
  createdAt: Date;
  updatedAt: Date;
  id: number;
  userId: number;
  productId: number;
  quantity: number;
  variationId: number | null;
  variation?: ProductVariationResponse | null;
};

export type CartResponse = {
  items: CartItemResponse[];
  subtotal: string;
  handlingCharge: string;
  deliveryFee: string;
  couponCode: string | null;
  couponValid: boolean;
  couponDiscount: string;
  useRewardPoints: boolean;
  rewardDiscount: string;
  rewardPointsUsed: number;
  total: string;
  canOrder: boolean;
};

export type HomeSectionType = 'CATEGORY' | 'PRODUCT';

export interface HomeSection {
  createdAt: Date;
  updatedAt: Date;
  id: string;
  title: string;
  onlyDiscount: boolean;
  type: HomeSectionType;
  warehouseId: number | null;
  warehouse?: Warehouse | null;
  displayOrder: number;
  homeSectionCategory?: HomeSectionCategory[];
  translations?: HomeSectionTranslation[];
}

export interface HomeSectionCategory {
  id: string;
  homeSectionId: string;
  categoryId: number;
  category?: Category;
  displayOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface HomeSectionTranslation {
  id: number;
  homeSectionId: string;
  languageId: number;
  language: Language;
  title: string;
}

export type HomeSectionResponse = Prisma.HomeSectionGetPayload<object> & {
  categories?: Category[];
  products?: ProductWithStockResponse[];
};

export type TravelMode = 'driving' | 'walking' | 'bicycling' | 'transit';

export const S3_COLLECTIONS = [
  'profile-pictures',
  'products',
  'banners',
  'categories',
  'category_icons',
] as const;
export type S3_COLLECTIONS = (typeof S3_COLLECTIONS)[number];

export const ALLOWED_CONTENT_TYPES = [
  'image/jpeg',
  'image/png',
  'image/webp',
  // "application/pdf",
  // "video/mp4",
  // "audio/mpeg",
] as const;

export type ContentType = (typeof ALLOWED_CONTENT_TYPES)[number];

export interface PaginationData {
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface PaginatedResponse<T> extends PaginationData {
  data: T[];
}

export interface ProductPolicyType {
  id: number;
  name: string;
  icon: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductPolicy {
  id: number;
  productId: number;
  description: string | null;
  details: Record<string, string>;
  productPolicyTypeId: number;
  productPolicyType: ProductPolicyType;
  createdAt: Date;
  updatedAt: Date;
}
