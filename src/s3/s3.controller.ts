import { <PERSON>, <PERSON> } from '@nestjs/common';
import { SignedUrlForGetDto, SignedUrlForPutDto } from './s3.schema';
import { S3Service } from './s3.service';

@Controller('s3')
export class S3Controller {
  constructor(private readonly s3Service: S3Service) {}

  @Post('/get')
  async getSignedUrlForGet(dto: SignedUrlForGetDto) {
    return this.s3Service.getSignedGetUrl(dto);
  }

  @Post('/put')
  async getSignedUrlForPut(dto: SignedUrlForPutDto) {
    return this.s3Service.getSignedPutUrl(dto);
  }
}
