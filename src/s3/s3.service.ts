import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { Injectable } from '@nestjs/common';
import { S3_BUCKET, s3Client } from 'src/util/s3';
import { SignedUrlForGetDto, SignedUrlForPutDto } from './s3.schema';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

@Injectable()
export class S3Service {
  async getSignedGetUrl(dto: SignedUrlForGetDto) {
    const { key } = dto;

    const command = new GetObjectCommand({
      Bucket: S3_BUCKET,
      Key: key,
    });

    const url = await getSignedUrl(s3Client, command, { expiresIn: 3600 });

    return {
      url,
    };
  }

  async getSignedPutUrl(dto: SignedUrlForPutDto) {
    const uniqueFile = `${Date.now()}_${dto.filename}`;
    const key = `${dto.collection}/${uniqueFile}`;

    const command = new PutObjectCommand({
      Bucket: S3_BUCKET,
      Key: key,
      ContentType: dto.contentType,
    });

    const url = await getSignedUrl(s3Client, command, { expiresIn: 3600 });

    return { url: url, key: key };
  }
}
