import { createZodDto } from 'nestjs-zod';
import { ALLOWED_CONTENT_TYPES, S3_COLLECTIONS } from 'src/types';
import { z } from 'zod';

export const signedUrlForUploadSchema = z.object({
  filename: z.string(),
  contentType: z.enum(ALLOWED_CONTENT_TYPES),
  collection: z.enum(S3_COLLECTIONS),
});

export class SignedUrlForPutDto extends createZodDto(
  signedUrlForUploadSchema,
) {}

export const signedUrlForGetSchema = z.object({
  key: z.string(),
});

export class SignedUrlForGetDto extends createZodDto(signedUrlForGetSchema) {}
