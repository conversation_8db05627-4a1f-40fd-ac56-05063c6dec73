import { WarehouseType } from '@prisma/client';
import { createZodDto } from 'nestjs-zod';
import { timeRegex } from 'src/admin/delivery-slot/delivery-slot.schema';
import { z } from 'zod';

const CreateOrderSchema = z.object({
  shippingAddressId: z.number(),
  deliveryDate: z.coerce.date(),
  deliveryStartTime: z.string().refine((value) => timeRegex.test(value), {
    message: 'deliveryStartTime must be in HH:mm format (00:00 - 23:59)',
  }),
  deliveryEndTime: z.string().refine((value) => timeRegex.test(value), {
    message: 'deliveryEndTime must be in HH:mm format (00:00 - 23:59)',
  }),
  note: z.string().optional().nullable(),
  cashToPay: z.number().optional().default(0),
  lat: z.number(),
  long: z.number(),
  warehouseType: z
    .nativeEnum(WarehouseType)
    .optional()
    .default(WarehouseType.GENERAL),
  overrideWarehouseId: z.number().optional(),
  couponCode: z.string().optional().nullable(),
  useRewardPoints: z.boolean().optional().default(false),
});

export class CreateOrderDto extends createZodDto(CreateOrderSchema) {}
