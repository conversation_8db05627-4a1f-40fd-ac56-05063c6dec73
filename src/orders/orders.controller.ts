import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards,
  UsePipes,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './orders.schema';
import { ZodValidationPipe } from 'nestjs-zod';
import { AuthGuard } from 'src/auth/auth.guard';
import { User } from 'src/auth/auth.decorator';
import { UsersService } from 'src/user/users.service';

@Controller('orders')
@UseGuards(AuthGuard)
@UsePipes(ZodValidationPipe)
export class OrdersController {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly usersService: UsersService,
  ) {}

  @User()
  @Get()
  async getOrders(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
    @Query('lang', new DefaultValuePipe('en')) lang: string,
    @Query(
      'overrideUserId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideUserId?: number,
  ) {
    return this.ordersService.getOrders({
      page: page,
      pageSize: pageSize,
      lang: lang,
      overrideUser: overrideUserId
        ? await this.usersService.getUserDetailsById(overrideUserId)
        : undefined,
    });
  }

  @User()
  @Get('/:id')
  async getOrder(
    @Param('id', ParseIntPipe) id: number,
    @Query('lang', new DefaultValuePipe('en')) lang: string,
    @Query(
      'overrideUserId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideUserId?: number,
  ) {
    return this.ordersService.getOrder(
      id,
      lang,
      overrideUserId
        ? await this.usersService.getUserDetailsById(overrideUserId)
        : undefined,
    );
  }

  @User()
  @Post()
  async createOrder(
    @Body() createOrderDto: CreateOrderDto,
    @Query(
      'overrideUserId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideUserId?: number,
  ) {
    return this.ordersService.createOrder(
      createOrderDto,
      overrideUserId
        ? await this.usersService.getUserDetailsById(overrideUserId)
        : undefined,
    );
  }

  @User()
  @Delete('/:id')
  async cancelOrder(
    @Param('id', ParseIntPipe) id: number,
    @Query(
      'overrideUserId',
      new DefaultValuePipe(undefined),
      new ParseIntPipe({ optional: true }),
    )
    overrideUserId?: number,
  ) {
    return this.ordersService.cancelOrder(
      id,
      overrideUserId
        ? await this.usersService.getUserDetailsById(overrideUserId)
        : undefined,
    );
  }
}
