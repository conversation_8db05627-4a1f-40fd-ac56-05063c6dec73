import { Controller, Get } from '@nestjs/common';
import { RewardTiersService } from './reward-tiers.service';

@Controller('reward-tiers')
export class RewardTiersController {
  constructor(private readonly rewardTiersService: RewardTiersService) {}

  @Get('spending')
  async getSpending() {
    const spending = await this.rewardTiersService.getRollingSpend();
    return {
      spending,
    };
  }

  @Get('all')
  async getAllRewardTiers() {
    return this.rewardTiersService.getAllRewardTiers();
  }

  @Get()
  async getMyTier() {
    return this.rewardTiersService.getMyTier();
  }
}
