import { Injectable } from '@nestjs/common';
import { addDays, subDays } from 'date-fns';
import { Context } from 'src/context';
import { PrismaService } from 'src/prisma/prisma.service';
import { RewardPointsService } from 'src/reward-points/reward-points.service';
import { ROLLING_DAYS } from 'src/util/utils';

@Injectable()
export class RewardTiersService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly context: Context,
    private readonly rewardPointsService: RewardPointsService,
  ) {}

  private rollingWindowStart(now: Date) {
    return subDays(now, ROLLING_DAYS); // date‑fns
  }

  async getRollingSpend(
    userId: number = this.context.user!.id,
    now: Date = new Date(),
  ): Promise<number> {
    const res = await this.prisma.order.aggregate({
      _sum: { totalAmount: true },
      where: {
        userId,
        paymentStatus: 'PAID',
        status: { in: ['CONFIRMED', 'SHIPPED', 'DELIVERED'] },
        createdAt: { gte: this.rollingWindowStart(now), lt: now },
      },
    });
    return res._sum.totalAmount?.toNumber() ?? 0;
  }

  async getMyTier() {
    const user = await this.prisma.user.findUnique({
      where: { id: this.context.user!.id },
      select: {
        rewardTier: true,
        tierExpiresAt: true,
      },
    });
    return {
      rewardTier: user?.rewardTier,
      tierExpiresAt: user?.tierExpiresAt,
      points: await this.rewardPointsService.getUserRewardPoints(
        this.context.user!.id,
      ),
      spending: (await this.getRollingSpend()).toFixed(2),
    };
  }

  getAllRewardTiers() {
    return this.prisma.rewardTier.findMany({
      orderBy: { requiredRollingSpend: 'asc' },
    });
  }

  async processExpiringTiers(now = new Date()): Promise<void> {
    const expiring = await this.prisma.user.findMany({
      where: { tierExpiresAt: { lte: now } },
      select: { id: true, rewardTierId: true },
    });
    if (!expiring.length) return;

    const tiers = await this.prisma.rewardTier.findMany({
      orderBy: { requiredRollingSpend: 'desc' },
    });

    for (const u of expiring) {
      const spend = await this.getRollingSpend(u.id, now);
      const newTier =
        tiers.find((t) => spend >= t.requiredRollingSpend.toNumber()) ?? null;

      await this.prisma.$transaction([
        this.prisma.user.update({
          where: { id: u.id },
          data: {
            rewardTierId: newTier?.id ?? null,
            tierExpiresAt: newTier ? addDays(now, ROLLING_DAYS) : null,
          },
        }),
      ]);
    }
  }
}
