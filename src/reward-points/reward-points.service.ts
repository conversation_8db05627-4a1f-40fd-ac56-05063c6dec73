import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { isAfter } from 'date-fns';
import { Context } from 'src/context';

@Injectable()
export class RewardPointsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly context: Context,
  ) {}

  async getUserRewardPoints(userId: number): Promise<number> {
    const transactions = await this.prisma.rewardPointTransaction.findMany({
      where: {
        userId: userId,
      },
    });

    const now = new Date();

    let totalPoints = 0;
    for (const transaction of transactions) {
      if (transaction.type === 'CREDIT') {
        if (!transaction.expiry || isAfter(transaction.expiry, now)) {
          totalPoints += transaction.amount.toNumber();
        }
      } else {
        totalPoints -= transaction.amount.toNumber();
      }
    }

    return totalPoints;
  }

  async determineUserRewardTier(userId: number): Promise<{
    tier: { id: number; name: string; requiredRollingSpend: any } | null;
    points: number;
  }> {
    // Calculate user's total points
    const points = await this.getUserRewardPoints(userId);

    // Get all reward tiers ordered by required points (descending)
    const tiers = await this.prisma.rewardTier.findMany({
      orderBy: {
        requiredRollingSpend: 'desc',
      },
    });

    // Find the highest tier the user qualifies for
    let userTier: {
      id: number;
      name: string;
      requiredRollingSpend: any;
    } | null = null;
    for (const tier of tiers) {
      const requiredRollingSpend = tier.requiredRollingSpend.toNumber();
      if (points >= requiredRollingSpend) {
        userTier = {
          id: tier.id,
          name: tier.name,
          requiredRollingSpend: tier.requiredRollingSpend,
        };
        break;
      }
    }

    return { tier: userTier, points };
  }

  async getUserTransactions(userId: number) {
    return this.prisma.rewardPointTransaction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      include: {
        order: {
          select: {
            id: true,
            totalAmount: true,
          },
        },
      },
    });
  }

  async getExpiringPoints(): Promise<{
    expiryDate: Date | null;
    expiringPoints: number;
  }> {
    const now = new Date();

    const nextExpiry = await this.prisma.rewardPointTransaction.findFirst({
      where: {
        userId: this.context.user!.id,
        type: 'CREDIT',
        expiry: {
          gte: now,
        },
      },
      orderBy: {
        expiry: 'asc',
      },
      select: {
        expiry: true,
      },
    });

    if (!nextExpiry)
      return {
        expiryDate: null,
        expiringPoints: 0,
      };

    const credits = await this.prisma.rewardPointTransaction.findMany({
      where: {
        userId: this.context.user!.id,
        type: 'CREDIT',
        expiry: nextExpiry.expiry,
      },
    });

    const expiringPoints = credits.reduce(
      (sum, tx) => sum + tx.amount.toNumber(),
      0,
    );

    return {
      expiryDate: nextExpiry.expiry,
      expiringPoints,
    };
  }
}
