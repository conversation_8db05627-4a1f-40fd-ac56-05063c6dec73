import {
  <PERSON>,
  DefaultV<PERSON>ue<PERSON><PERSON>e,
  Get,
  ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import { RewardPointsService } from './reward-points.service';
import { Context } from 'src/context';
import { AuthGuard } from 'src/auth/auth.guard';
import { User } from 'src/auth/auth.decorator';

@UseGuards(AuthGuard)
@Controller('reward-points')
export class RewardPointsController {
  constructor(
    private readonly rewardPointService: RewardPointsService,
    private readonly context: Context,
  ) {}

  @User()
  @Get()
  async getUserRewardPoints() {
    const userId = this.context.user!.id;
    const points = await this.rewardPointService.getUserRewardPoints(userId);

    return {
      points,
    };
  }

  @User()
  @Get('transactions')
  async getUserTransactions() {
    const userId = this.context.user!.id;
    return this.rewardPointService.getUserTransactions(userId);
  }

  @User()
  @Get('expiring')
  async getExpiringPoints() {
    return this.rewardPointService.getExpiringPoints();
  }
}
