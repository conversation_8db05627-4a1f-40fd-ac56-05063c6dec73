import { Injectable } from '@nestjs/common';
import { Resend } from 'resend';
import WelcomeToVegmove from '../../emails/welcome-to-vegmove';
import VerifyEmail from '../../emails/verify-email';
import OrderShipped from '../../emails/order-shipped';
import OrderAccepted from '../../emails/order-accepted';
import OrderConfirmation from '../../emails/order-confirmation';
import NewOrderReceived from '../../emails/new-order-received';
import InviteFriends from '../../emails/invite-friends';
import LateDeliveryAlert from '../../emails/late-delivery-alert';
import LowStockAlert from '../../emails/low-stock-alert';
import DailySalesSummary from '../../emails/daily-sales-summary';
import WeeklySalesSummary from '../../emails/weekly-sales-summary';
import GoodNews from '../../emails/good-news';
import Oops from '../../emails/Oops';
import { ReactElement } from 'react';
import {
  CartItemResponse,
  OrderResponse,
  ProductWithStockResponse,
} from 'src/types';
import { Warehouse } from '@prisma/client';

interface DeliveryPerson {
  name: string;
  phone: string;
  assignedTime: string;
  currentStatus: string;
}

interface SalesSummary {
  totalOrders: number;
  totalRevenue: number;
  avgOrderValue: number;
}

@Injectable()
export class EmailService {
  private readonly resend: Resend;

  constructor() {
    if (!process.env.RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY environment variable is not set');
    }
    this.resend = new Resend(process.env.RESEND_API_KEY);
  }

  private async sendEmail({
    to,
    subject,
    react,
    from = 'Vegmove Notifications <<EMAIL>>',
  }: {
    to: string;
    subject: string;
    react: ReactElement;
    from?: string;
  }) {
    try {
      await this.resend.emails.send({
        from,
        to,
        subject,
        react,
      });
    } catch (error) {
      console.error('Failed to send email:', error);
      throw error;
    }
  }

  async sendWelcomeEmail(to: string, name: string) {
    await this.sendEmail({
      to,
      subject: 'Welcome to VegMove! 🌱',
      react: WelcomeToVegmove({
        id: '0',
        name,
        email: to,
      }),
    });
  }

  async sendVerificationEmail(to: string, _verificationLink: string) {
    await this.sendEmail({
      to,
      subject: 'Verify your email address',
      react: VerifyEmail(),
    });
  }

  async sendOrderShippedEmail(to: string, orderDetails: OrderResponse) {
    await this.sendEmail({
      to,
      subject: 'Your order has been shipped! 📦',
      react: OrderShipped({ order: orderDetails }),
    });
  }

  async sendOrderAcceptedEmail(to: string, orderDetails: OrderResponse) {
    await this.sendEmail({
      to,
      subject: 'Your order has been accepted! ✅',
      react: OrderAccepted({ order: orderDetails }),
    });
  }

  async sendOrderConfirmationEmail(to: string, orderDetails: OrderResponse) {
    await this.sendEmail({
      to,
      subject: 'Order Confirmation',
      react: OrderConfirmation({ order: orderDetails }),
    });
  }

  async sendNewOrderReceivedEmail(to: string, orderDetails: OrderResponse) {
    await this.sendEmail({
      to,
      subject: 'New Order Received',
      react: NewOrderReceived({ order: orderDetails }),
    });
  }

  async sendInviteFriendsEmail(to: string, referralCode: string) {
    await this.sendEmail({
      to,
      subject: 'Invite your friends to VegMove! 🤝',
      react: InviteFriends({ referralCode }),
    });
  }

  async sendLateDeliveryAlertEmail(
    to: string,
    orderDetails: OrderResponse,
    deliveryPerson: DeliveryPerson,
  ) {
    await this.sendEmail({
      to,
      subject: 'Delivery Update for Your Order',
      react: LateDeliveryAlert({ order: orderDetails, deliveryPerson }),
    });
  }

  async sendLowStockAlertEmail(
    to: string,
    warehouse: Warehouse,
    products: ProductWithStockResponse[],
  ) {
    await this.sendEmail({
      to,
      subject: 'Low Stock Alert',
      react: LowStockAlert({ warehouse, products }),
    });
  }

  async sendDailySalesSummaryEmail(to: string, summary: SalesSummary) {
    await this.sendEmail({
      to,
      subject: 'Daily Sales Summary',
      react: DailySalesSummary(summary),
    });
  }

  async sendWeeklySalesSummaryEmail(to: string, summary: SalesSummary) {
    await this.sendEmail({
      to,
      subject: 'Weekly Sales Summary',
      react: WeeklySalesSummary(summary),
    });
  }

  async sendGoodNewsEmail(to: string, products: ProductWithStockResponse[]) {
    await this.sendEmail({
      to,
      subject: 'Good News! 🎉',
      react: GoodNews({ products }),
    });
  }

  async sendOops(to: string, cartItems: CartItemResponse[]) {
    await this.sendEmail({
      to,
      subject: 'Oops! Something went wrong',
      react: Oops({ cartItems }),
    });
  }
}
