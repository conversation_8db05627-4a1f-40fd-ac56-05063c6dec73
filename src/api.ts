import axios from "axios";

const API_BASE_URL = `${process.env.NEXT_PUBLIC_API_URL}`;

type Config = { ContentType?: string };

export const apiService = {
  async get(endpoint: string, params?: unknown) {
    const token = localStorage.getItem("token");
    return axios.get(`${API_BASE_URL}/${endpoint}`, {
      params: params,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  },

  async post(endpoint: string, data: unknown, config?: Config) {
    const token = localStorage.getItem("token");
    return axios.post(`${API_BASE_URL}/${endpoint}`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": config?.ContentType ?? "application/json",
      },
    });
  },

  async delete(endpoint: string) {
    const token = localStorage.getItem("token");
    return axios.delete(`${API_BASE_URL}/${endpoint}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });
  },

  async patch(endpoint: string, data: unknown) {
    const token = localStorage.getItem("token");
    return axios.patch(`${API_BASE_URL}/${endpoint}`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });
  },

  async put(endpoint: string, data: unknown) {
    const token = localStorage.getItem("token");
    return axios.put(`${API_BASE_URL}/${endpoint}`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });
  },

  // Add other methods like put, delete as needed
};
