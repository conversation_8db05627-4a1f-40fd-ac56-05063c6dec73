import {
  <PERSON>,
  Get,
  Param,
  ParseIntPipe,
  Res,
  UseGuards,
} from '@nestjs/common';
import { Response } from 'express';
import { User } from 'src/auth/auth.decorator';
import { AuthGuard } from 'src/auth/auth.guard';
import { InvoicesService } from './invoices.service';

@Controller('invoices')
@UseGuards(AuthGuard)
export class InvoicesController {
  constructor(private readonly invoiceService: InvoicesService) {}

  @User()
  @Get(':id')
  async generateInvoice(
    @Param('id', ParseIntPipe) orderId: number,
    @Res({ passthrough: true }) res: Response,
  ) {
    const streamableFile = await this.invoiceService.generateInvoice(orderId);

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader(
      'Content-Disposition',
      `inline; filename=invoice-${orderId}.pdf`,
    );

    return streamableFile;
  }
}
