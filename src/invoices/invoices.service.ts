import { Injectable, StreamableFile } from '@nestjs/common';
import PDFDocument from 'pdfkit';
import { Address, Country } from '@prisma/client';
import { AdminOrderResponse } from 'src/types';
import { OrdersService } from 'src/admin/orders/orders.service';
import { PassThrough } from 'stream';

// Use AdminOrderResponse directly since user is now required
type OrderDetails = AdminOrderResponse & {
  address: Address & {
    country: Country;
  };
};

@Injectable()
export class InvoicesService {
  constructor(private readonly orderService: OrdersService) {}

  async generateInvoice(orderId: number): Promise<StreamableFile> {
    const order = await this.orderService.getOrderById(orderId);

    const stream = new PassThrough();
    // Create a new PDF document
    const doc = new PDFDocument({ margin: 50 });

    // Pipe the PDF into the response
    doc.pipe(stream);
    // doc.pipe(res);

    // Add company logo and header
    this.generateHeader(doc);

    // Add invoice information
    this.generateInvoiceInfo(doc, order);

    // Add customer information
    this.generateCustomerInfo(doc, order);

    // Add invoice table
    this.generateInvoiceTable(doc, order);

    // Add footer
    this.generateFooter(doc);

    // Finalize the PDF and end the stream
    doc.end();

    return new StreamableFile(stream);
  }

  private generateHeader(doc: PDFDocument): void {
    doc
      .fillColor('#444444')
      .fontSize(20)
      .text('VegMove', 50, 45)
      .fontSize(10)
      .text('VegMove Inc.', 50, 70)
      .text('123 Main Street', 50, 85)
      .text('Dhaka, Bangladesh', 50, 100)
      .text('Email: <EMAIL>', 50, 115)
      .text('Phone: +880 1234 567890', 50, 130)
      .moveDown();
  }

  private generateInvoiceInfo(doc: PDFDocument, order: OrderDetails): void {
    const createdAt = new Date(order.createdAt);

    doc.fillColor('#444444').fontSize(20).text('Invoice', 50, 160);

    this.generateHr(doc, 185);

    const invoiceInfoY = 200;

    doc
      .fontSize(10)
      .text('Invoice Number:', 50, invoiceInfoY)
      .font('Helvetica-Bold')
      .text(`INV-${order.id.toString().padStart(6, '0')}`, 150, invoiceInfoY)
      .font('Helvetica')
      .text('Date:', 50, invoiceInfoY + 15)
      .text(this.formatDate(createdAt), 150, invoiceInfoY + 15)
      .text('Order Status:', 50, invoiceInfoY + 30)
      .text(order.status, 150, invoiceInfoY + 30)
      .text('Payment Status:', 50, invoiceInfoY + 45)
      .text(order.paymentStatus, 150, invoiceInfoY + 45)
      .moveDown();

    this.generateHr(doc, invoiceInfoY + 70);
  }

  private generateCustomerInfo(doc: PDFDocument, order: OrderDetails): void {
    const customerInfoY = 285;

    doc
      .fontSize(10)
      .text('Customer Information:', 50, customerInfoY)
      .font('Helvetica-Bold')
      .text(order.user?.name || 'N/A', 50, customerInfoY + 15)
      .font('Helvetica')
      .text(order.user?.email || 'N/A', 50, customerInfoY + 30)
      .text(order.user?.phone || 'N/A', 50, customerInfoY + 45);

    // Shipping Address
    doc
      .fontSize(10)
      .text('Shipping Address:', 300, customerInfoY)
      .text(
        `${order.address.apartment}, ${order.address.block}`,
        300,
        customerInfoY + 15,
      )
      .text(order.address.streetName, 300, customerInfoY + 30)
      .text(
        `${order.address?.city || ''}, ${order.address?.state || ''}, ${
          order.address?.zipCode || ''
        }`,
        300,
        customerInfoY + 45,
      )
      .text(order.address?.country?.name || '', 300, customerInfoY + 60)
      .moveDown();

    this.generateHr(doc, customerInfoY + 85);
  }

  private generateInvoiceTable(doc: PDFDocument, order: OrderDetails): void {
    const invoiceTableTop = 400;
    const tableTop = invoiceTableTop + 30;

    // Table headers - removed GST column, adjusted spacing
    doc
      .font('Helvetica-Bold')
      .fontSize(10)
      .text('Item', 50, invoiceTableTop)
      .text('Qty', 320, invoiceTableTop)
      .text('Unit Price', 380, invoiceTableTop)
      .text('Amount', 480, invoiceTableTop);

    this.generateHr(doc, invoiceTableTop + 20);

    // Table rows
    let position = tableTop;

    for (const item of order.items) {
      const unitPrice = item.price.toNumber() / item.quantity;
      const lineTotal = item.price.toNumber();

      // Handle long product names with text wrapping
      const productName = item.product.name;
      const maxWidth = 260; // Maximum width for product name column

      // Split long product names into multiple lines if needed
      const words = productName.split(' ');
      let currentLine = '';
      let lines: string[] = [];

      for (const word of words) {
        const testLine = currentLine ? `${currentLine} ${word}` : word;
        const testWidth = doc.widthOfString(testLine, { fontSize: 10 });

        if (testWidth <= maxWidth) {
          currentLine = testLine;
        } else {
          if (currentLine) {
            lines.push(currentLine);
            currentLine = word;
          } else {
            // Single word is too long, truncate it
            lines.push(word.substring(0, 30) + '...');
            currentLine = '';
          }
        }
      }

      if (currentLine) {
        lines.push(currentLine);
      }

      // Draw the first line with other item details
      doc
        .font('Helvetica')
        .fontSize(10)
        .text(lines[0] || productName, 50, position)
        .text(item.quantity.toString(), 320, position)
        .text(`Rs ${unitPrice.toFixed(2)}`, 380, position)
        .text(`Rs ${lineTotal.toFixed(2)}`, 480, position);

      // Draw additional lines for long product names
      for (let i = 1; i < lines.length; i++) {
        position += 15;
        doc.text(lines[i], 50, position);
      }

      position += 20;

      // Add a page break if needed
      if (position > 700) {
        doc.addPage();
        position = 50;
      }
    }

    this.generateHr(doc, position);

    // Use Order model pricing structure
    const subtotal = order.subtotal.toNumber();
    const handlingCharge = order.handlingCharge.toNumber();
    const deliveryFee = order.deliveryFee.toNumber();
    const couponDiscount = order.couponDiscount.toNumber();
    const rewardDiscount = order.rewardDiscount.toNumber();
    const totalAmount = order.totalAmount.toNumber();

    // Pricing breakdown following Order model structure
    let currentPosition = position + 20;

    // Subtotal (including GST)
    doc
      .font('Helvetica-Bold')
      .fontSize(10)
      .text('Subtotal (incl. GST):', 350, currentPosition)
      .font('Helvetica')
      .text(`Rs ${subtotal.toFixed(2)}`, 480, currentPosition);

    // Handling Charge (if applicable)
    if (handlingCharge > 0) {
      currentPosition += 20;
      doc
        .font('Helvetica-Bold')
        .text('Handling Charge:', 350, currentPosition)
        .font('Helvetica')
        .text(`Rs ${handlingCharge.toFixed(2)}`, 480, currentPosition);
    }

    // Delivery Fee (if applicable)
    if (deliveryFee > 0) {
      currentPosition += 20;
      doc
        .font('Helvetica-Bold')
        .text('Delivery Fee:', 350, currentPosition)
        .font('Helvetica')
        .text(`Rs ${deliveryFee.toFixed(2)}`, 480, currentPosition);
    }

    // Coupon Discount (if applicable)
    if (couponDiscount > 0) {
      currentPosition += 20;
      doc
        .font('Helvetica-Bold')
        .text('Coupon Discount:', 350, currentPosition)
        .font('Helvetica')
        .text(`-Rs ${couponDiscount.toFixed(2)}`, 480, currentPosition);
    }

    // Reward Discount (if applicable)
    if (rewardDiscount > 0) {
      currentPosition += 20;
      doc
        .font('Helvetica-Bold')
        .text('Reward Discount:', 350, currentPosition)
        .font('Helvetica')
        .text(`-Rs ${rewardDiscount.toFixed(2)}`, 480, currentPosition);
    }

    // Total Amount
    currentPosition += 25;
    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .text('TOTAL:', 350, currentPosition)
      .text(`Rs ${totalAmount.toFixed(2)}`, 480, currentPosition);
  }

  private generateFooter(doc: PDFDocument): void {
    doc
      .fontSize(10)
      .text(
        'Thank you for shopping with VegMove. We appreciate your business!',
        50,
        730,
        { align: 'center', width: 500 },
      );
  }

  private generateHr(doc: PDFDocument, y: number): void {
    doc
      .strokeColor('#aaaaaa')
      .lineWidth(1)
      .moveTo(50, y)
      .lineTo(550, y)
      .stroke();
  }

  private formatDate(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }
}
