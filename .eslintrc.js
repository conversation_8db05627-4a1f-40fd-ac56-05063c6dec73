module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint/eslint-plugin'],
  extends: [
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js'],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    // 'no-unused-vars': [
    //   'error',
    //   {
    //     varsIgnorePattern: '^_', // ignore any var named _something
    //     argsIgnorePattern: '^_', // ignore any function arg named _something
    //     caughtErrors: 'none', // (optional) ignore unused catch params
    //   },
    // ],
    '@typescript-eslint/no-unused-vars': [
      'error',
      {
        varsIgnorePattern: '^_',
        argsIgnorePattern: '^_',
        ignoreRestSiblings: true, // handy if you do const { a, ..._rest } = obj
        caughtErrors: 'none',
      },
    ],
    '@typescript-eslint/no-namespace': 'off',
  },
};
