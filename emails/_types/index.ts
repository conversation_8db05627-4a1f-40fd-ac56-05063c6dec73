/* eslint-disable prettier/prettier */

import { Decimal } from '@prisma/client/runtime/library';

// User types
export interface User {
  id: string;
  name: string;
  email?: string;
  phone?: string;
}

export interface UserResponse {
  id: number;
  name: string;
  email: string;
  phone: string | null;
  profilePicture: string | null;
}

export interface ProductsResponse {
  data: Product[];
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

export interface ProductsQueryParams {
  categoryId?: number;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: string;
  sortOrder?: string;
  inStock?: boolean;
  minDiscount?: number;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

// Auth types
export interface LoginCredentials {
  email?: string;
  phone?: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email?: string;
  phone?: string;
  password: string;
  countryId: number;
}

export type Product = {
  id: number;
  barcode: string;
  name: string;
  description: string;
  categoryId: number;
  weight: number;
  weightUnit: string;
  length: number;
  width: number;
  height: number;
  gstPercentage: number;
  supplierId: number | null;
  slug: string | null;
  discountValue: number | null;
  discountType: string | null;
  hasVariations: boolean;
  thumbnail: {
    id: number;
    url: string;
    mediaType: string;
    productId: number | null;
    variationId: number | null;
  } | null;
  category: {
    id: number;
    name: string;
    parentId: number | null;
    type: string;
    slug: string;
    isActive: boolean;
    bannerUrl: string | null;
    iconUrl: string | null;
  };
  attributes?: {
    id: number;
    name: string;
    options: {
      id: number;
      name: string;
      colorCode: string | null;
      imageUrl: string | null;
    }[];
  }[];
  variations?:
    | {
        id: number;
        barcode: string;
        options: {
          option: {
            id: number;
            name: string;
            colorCode: string | null;
            imageUrl: string | null;
            attribute: {
              id: number;
              name: string;
            };
          };
        }[];
        media: {
          id: number;
          url: string;
          mediaType: string;
        }[];
      }[]
    | null;
};

export type CartItem = {
  id: number;
  userId: number;
  productId: number;
  quantity: number;
  product: Product;
  itemTotal: string;
  gstAmount: string;
  originalTotal: string;
  createdAt: string;
  updatedAt: string;
};

export type Cart = {
  items: CartItem[];
  subtotal: string;
  handlingCharge: string;
  deliveryFee: string;
  couponCode: string | null;
  couponValid: boolean;
  couponDiscount: string;
  useRewardPoints: boolean;
  rewardDiscount: string;
  rewardPointsUsed: number;
  total: string;
  canOrder: boolean;
  itemTotal: string;
  gstAmount: string;
  originalTotal: string;
};

interface CategoryTranslation {
  id: number;
  name: string;
  language: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  parentId: number | null;
  type: 'COLLECTION' | 'SEGMENT' | 'CATEGORY';
  isActive: boolean;
  bannerUrl: string | null;
  iconUrl: string | null;
  categoryTranslation: CategoryTranslation[];
  createdAt: string;
  updatedAt: string;
}
export interface HomeSection {
  id: string;
  type: 'CATEGORY' | 'PRODUCT';
  title: string;
  onlyDiscount: boolean;
  displayOrder: number;
  warehouseId: string | null;
  warehouse: unknown | null;
  categories?: Category[];
  products?: Product[];
  createdAt: string;
  updatedAt: string;
}

export type OrderData = {
  shippingAddressId: number;
  deliveryDate: string;
  deliveryStartTime: string;
  deliveryEndTime: string;
  note: string;
  lat: number;
  long: number;
  warehouseType: string;
  couponCode: string;
  useRewardPoints: boolean;
  cashToPay?: number;
};

export type Address = {
  type: 'BILLING';
  apartment: string;
  block: string;
  streetName: string;
  city: string;
  state: string;
  countryId: number;
  zipCode: string;
  lat: string;
  long: string;
  isDeleted: boolean;
};

export type AddressResponse = {
  id: number;
  userId: number;
  type: string;
  apartment: string | null;
  block: string | null;
  streetName: string;
  city: string;
  state: string;
  countryId: number;
  zipCode: string;
  lat: string;
  long: string;
  isDeleted: boolean;
  country: {
    id: number;
    name: string;
    code: string;
    dialCode: string;
    flagUrl: string;
  };
};

export type OrderStatusHistory = {
  id: number;
  orderId: number;
  status: string;
};

export type OrderItem = {
  id: number;
  orderId: number;
  productId: number;
  quantity: number;
  price: Decimal;
  originalPrice: Decimal;
  gstAmount: Decimal;
  inventoryId: number;
  variationId: number | null;
  product: Product;
  variation?: {
    id: number;
    barcode: string;
    options: {
      variationId: number;
      optionId: number;
      option: {
        id: number;
        attributeId: number;
        name: string;
        colorCode: string | null;
        imageUrl: string | null;
        iconUrl: string | null;
        attribute: {
          id: number;
          name: string;
          productId: number;
        };
      };
    }[];
    media: {
      id: number;
      url: string;
      mediaType: string;
      productId: number | null;
      variationId: number | null;
    }[];
  } | null;
};

export type OrderResponse = {
  createdAt: Date;
  updatedAt: Date;
  id: number;
  userId: number;
  status: string;
  addressId: number;
  subtotal: Decimal;
  handlingCharge: Decimal;
  deliveryFee: Decimal;
  discountAmount: Decimal;
  couponDiscount: Decimal;
  rewardDiscount: Decimal;
  totalAmount: Decimal;
  deliveryDate: Date;
  deliveryStartTime: string;
  deliveryEndTime: string;
  note: string | null;
  couponId: number | null;
  deliveryDriverId: number | null;
  useRewardPoints: boolean;
  rewardPointsUsed: number;
  warehouseId: number | null;
  deliveryDriverAssignedDate: Date | null;
  paymentStatus: string;
  items: OrderItem[];
  address: AddressResponse;
  orderStatusHistory: OrderStatusHistory[];
  user?: UserResponse;
};

export type OrdersResponse = {
  data: OrderResponse[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
};

export type Language = {
  id: number;
  name: string;
  code: string;
  flagUrl: string;
};

export type CategoryResponse = {
  id: number;
  name: string;
  type: 'COLLECTION' | 'SEGMENT' | 'CATEGORY';
  slug: string;
  isActive: boolean;
  bannerUrl: string | null;
  iconUrl: string | null;
  categories?: [
    {
      createdAt: string;
      updatedAt: string;
      id: number;
      name: string;
      parentId: number;
      type: string;
      slug: string;
      isActive: boolean;
      bannerUrl: string | null;
      iconUrl: string | null;
      categoryTranslation: CategoryTranslation[];
    },
  ];

  parent?: {
    createdAt: string;
    updatedAt: string;
    id: number;
    name: string;
    parentId: number;
    type: string;
    slug: string;
    isActive: boolean;
    bannerUrl: string | null;
    iconUrl: string | null;
    categoryTranslation: CategoryTranslation[];
  };

  segments: [
    {
      createdAt: string;
      updatedAt: string;
      id: number;
      name: string;
      parentId: number;
      type: string;
      slug: string;
      isActive: boolean;
      bannerUrl: string | null;
      iconUrl: string | null;
      categoryTranslation: CategoryTranslation[];
    },
  ];
};

export type DeliveryTimeResponse = {
  id: number;
  startTime: string;
  endTime: string;
  date: string | null;
  weekday: string | null;
  type: string;
  reason: string | null;
  createdAt: string;
  updatedAt: string;
};

export type BannerResponse = {
  createdAt: string;
  updatedAt: string;
  id: number;
  active: boolean;
  mediaId: number;
  languageId: number;
  categoryId: number;
  media: {
    createdAt: string;
    updatedAt: string;
    id: number;
    url: string;
    mediaType: string;
    productId: number;
  };
};

export type RewardTiers = {
  createdAt: string;
  updatedAt: string;
  id: number;
  name: string;
  requiredRollingSpend: string;
  earnPercentage: number;
  redeemPercentage: number;
  maxDiscountValue: string;
  minOrderAmountForBonus: string | null;
  type: string;
};

export type RewardTiersResponse = {
  rewardTier: RewardTiers;
  tierExpiresAt: string | null;
  points: number;
  spending: string;
};

export type RewardPoint = {
  createdAt: string;
  updatedAt: string;
  id: string;
  amount: string;
  type: string;
  userId: number;
  orderId: number | null;
  note: string;
  expiry: string;
  order: number | null;
};

export type RewardPointExpiry = {
  expiryDate: string;
  expiringPoints: number;
};
