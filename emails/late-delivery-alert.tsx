/* eslint-disable prettier/prettier */
import { Hr, Img, Section } from '@react-email/components';
// import { OrderResponse } from './_types';
import Layout from './_components/layout';
import Header from './_components/header';
import ButtonLink from './_components/button';
import { OrderResponse } from 'src/types';

export default function LateDeliveryAlert({
  order,
  deliveryPerson,
}: {
  order: OrderResponse;
  deliveryPerson: {
    name: string;
    phone: string;
    assignedTime: string;
    currentStatus: string;
  };
}) {
  return (
    <Layout>
      <Header
        title="⏰ Late Delivery Alert"
        subTitle="A delivery is delayed beyond the expected time. Please review the details below and take necessary action."
        orderId={order.id.toString()}
      />

      <Section className=" bg-white p-4 mt-5 rounded-2xl">
        {order.items.map((item) => (
          <div className="flex gap-3 justify-start items-start" key={item.id}>
            <Img
              alt={item.product.name}
              height={70}
              width={70}
              src={item.product.thumbnail!.url}
              className="w-[70px] h-[70px] rounded-lg"
            />
            <div className="  max-w-[262px]">
              <span className="text-[15px] font-bold text-dark">
                {item.product.name}
              </span>
              <div className="flex justify-start items-center text-[15px] font-medium mt-1">
                <div>
                  <span className="text-gray">Quantity: </span>
                  <span className="text-dark">{item.quantity} pc</span>
                </div>
                {/* <div>
                        <span className="text-gray">Color: </span>
                        <span className="text-dark">
                          {item.variation?.options.map((item) => (
                            <div className="">{item.option.name}</div>
                          ))}{' '}
                          pc
                        </span>
                      </div> */}
              </div>
            </div>
            <div className="flex items-center gap-3 justify-end">
              <span className="line-through text-md font-semibold text-gray">
                ₹{item.originalPrice.toString()}
              </span>
              <span className="font-bold text-[23px] text-dark">
                ₹{item.price.toString()}
              </span>
            </div>
          </div>
        ))}

        <Hr className="mt-3" />

        <div className="flex flex-col gap-3 mt-6">
          <span className="text-[15px] font-bold text-dark">Order Details</span>

          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Item Total & GST
            </span>
            <span className="font-semibold text-dark text-[15px]">
              ₹{parseInt(order?.subtotal.toString())}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Handling Charge
            </span>
            <span className="font-semibold text-dark text-[15px]">
              ₹{parseInt(order?.handlingCharge.toString())}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Delivery Fee
            </span>
            <span className="font-semibold text-dark text-[15px]">
              ₹{parseInt(order?.deliveryFee.toString())}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Coupon Discount
            </span>
            <span className="font-semibold text-dark text-[15px]">
              ₹{parseInt(order?.couponDiscount.toString())}
            </span>
          </div>
        </div>
        <Hr className="mt-3" />

        <div className="flex justify-between items-center  border-y border-gray-200">
          <span className="text-dark text-[15px] font-bold ">Total Paid</span>
          <span className="font-bold text-dark text-[18px]">
            ₹{parseInt(order?.totalAmount.toString())}
          </span>
        </div>

        <div className="flex flex-col gap-3 mt-6">
          <span className="text-[15px] font-bold text-dark">
            Receiver Details
          </span>

          <div className="flex items-center gap-3 px-5 py-4 bg-[#E3E9E6] rounded-2xl">
            <div className="h-[50px] w-[50px] bg-brand rounded-full flex items-center justify-center">
              <svg
                width="22"
                height="18"
                viewBox="0 0 22 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="elements">
                  <circle
                    id="Ellipse 1647"
                    cx="16"
                    cy="15"
                    r="2"
                    stroke="white"
                    strokeWidth="1.5"
                  />
                  <circle
                    id="Ellipse 1648"
                    cx="6"
                    cy="15"
                    r="2"
                    stroke="white"
                    strokeWidth="1.5"
                  />
                  <path
                    id="Vector 5060"
                    d="M4 14.9724C2.90328 14.9178 2.2191 14.7546 1.73223 14.2678C1.24536 13.7809 1.08222 13.0967 1.02755 12M8 15H14M18 14.9724C19.0967 14.9178 19.7809 14.7546 20.2678 14.2678C21 13.5355 21 12.357 21 10V8H16.3C15.5555 8 15.1832 8 14.882 7.90211C14.2731 7.70428 13.7957 7.22691 13.5979 6.61803C13.5 6.31677 13.5 5.94451 13.5 5.2C13.5 4.08323 13.5 3.52485 13.3532 3.07295C13.0564 2.15964 12.3404 1.44358 11.4271 1.14683C10.9752 1 10.4168 1 9.3 1H1"
                    stroke="white"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    id="Vector 5026"
                    d="M1 5H7"
                    stroke="white"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    id="Vector 5027"
                    d="M1 8H5"
                    stroke="white"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    id="Vector 5061"
                    d="M13.5 3H15.3212C16.7766 3 17.5042 3 18.0964 3.35371C18.6886 3.70742 19.0336 4.34811 19.7236 5.6295L21 8"
                    stroke="white"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </g>
              </svg>
            </div>
            <div className="flex-1">
              <p className="font-medium">
                {order?.address.streetName}, {order?.address.city}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                {order?.address.apartment}, {order?.address.block}
              </p>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">Order ID</span>
            <span className="font-semibold text-dark text-[15px]">
              #{order?.id}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Receiver Name
            </span>
            <span className="font-semibold text-dark text-[15px]">
              {order?.user?.name}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Phone Number
            </span>
            <span className="font-semibold text-dark text-[15px]">
              {order?.user?.phone}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">Placed On</span>
            <span className="font-semibold text-dark text-[15px]">
              {new Date(order?.createdAt).toLocaleString('en-US', {
                day: 'numeric',
                month: 'long',
                year: 'numeric',
                hour: 'numeric',
                minute: 'numeric',
                hour12: true,
              })}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Delivery Slot Chosen
            </span>
            <span className="font-semibold text-dark text-[15px]">
              {order?.deliveryStartTime} - {order?.deliveryEndTime}
            </span>
          </div>
        </div>
      </Section>

      <Section className=" bg-white p-4 mt-5 rounded-2xl">
        <div className="flex flex-col gap-3">
          <span className="text-[15px] font-bold text-dark">
            🚚 Delivery Person
          </span>

          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">Name</span>
            <span className="font-semibold text-dark text-[15px]">
              {deliveryPerson.name}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Phone Number
            </span>
            <span className="font-semibold text-dark text-[15px]">
              {deliveryPerson.phone}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Assigned Time
            </span>
            <span className="font-semibold text-dark text-[15px]">
              {deliveryPerson.assignedTime}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Current Status
            </span>
            <span className="font-semibold text-dark text-[15px]">
              {deliveryPerson.currentStatus}
            </span>
          </div>
        </div>
      </Section>

      <Section className="mt-5">
        <ButtonLink href="https://vegmove.techsfera.dev">
          View Order in Dashboard
        </ButtonLink>
      </Section>
    </Layout>
  );
}

LateDeliveryAlert.PreviewProps = {
  order: {
    createdAt: '2025-05-21T10:04:01.634Z',
    updatedAt: '2025-05-22T07:45:29.754Z',
    id: 9,
    userId: 2,
    status: 'READY',
    addressId: 4,
    subtotal: '954.8',
    handlingCharge: '0',
    deliveryFee: '0',
    discountAmount: '0',
    couponDiscount: '0',
    rewardDiscount: '0',
    totalAmount: '954.8',
    deliveryDate: '2025-05-21T10:02:59.491Z',
    deliveryTime: null,
    deliveryStartTime: '15:00',
    deliveryEndTime: '17:00',
    note: '',
    cashToPay: '0',
    couponId: null,
    deliveryDriverId: null,
    useRewardPoints: false,
    rewardPointsUsed: 0,
    warehouseId: 1,
    deliveryDriverAssignedDate: null,
    paymentStatus: 'PENDING',
    items: [
      {
        id: 9,
        orderId: 9,
        productId: 1,
        quantity: 2,
        price: '954.8',
        originalPrice: '1020.8',
        gstAmount: '43.4',
        inventoryId: 1,
        variationId: null,
        product: {
          createdAt: '2025-05-15T16:16:17.757Z',
          updatedAt: '2025-05-22T06:12:44.138Z',
          id: 1,
          barcode: '8906010070281',
          name: 'Aashirvaad Atta - Shudh Chakki Atta, No Maida | Pouch',
          description: 'Aashirvaad Atta - Shudh Chakki Atta, No Maida | Pouch',
          highlights: {
            Brand: 'Aashirvaad',
            Weight: '10 kg',
            'Model Name': 'Shudh Chakki',
            'Key Features':
              '100% Whole Wheat, No Maida, Soft and Fluffy Rotis, Traditional Chakki grinding',
            'Product Type': 'Chakki Atta',
            'Material Type Free': 'Maida-free',
          },
          information: {
            Disclaimer:
              'All images are for representational purposes only. It is advised that you read the batch and manufacturing details, directions for use, allergen information, health and nutritional claims (wherever applicable), and other details mentioned on the label before consuming the product. For combo items, individual prices can be viewed on the page.',
            'Shelf Life': '93 days',
            'Seller Name': 'Commodum Groceries Private Limited',
            'Seller Address':
              'COMMODUM GROCERIES PRIVATE LIMITED, Regd. Office: 44, Saket Building, Mullick Bazaar, Park Street, Kolkata, West Bengal, India, 700016. For Support ReachOut : <EMAIL>',
            'Country of Origin': 'India',
            'Manufacturer Name': 'ITC Limited',
            'Seller License No.': '12822999000310',
            'Manufacturer Address':
              'ITC Limited, 37, J.L. Nehru Road, Kolkata- 700071',
            'Customer Care Details':
              'In case of any issue, contact us E-mail address: <EMAIL>',
          },
          categoryId: 6,
          weight: 10000,
          weightUnit: 'GRAM',
          length: 0,
          width: 0,
          height: 0,
          gstPercentage: 10,
          supplierId: null,
          slug: 'aashirvaad-atta-shudh-chakki-atta-no-maida-pouch',
          discountValue: 30,
          discountType: 'FLAT',
          hasVariations: false,
          thumbnailId: 9,
          active: true,
          maxCartQuantity: null,
          media: [
            {
              createdAt: '2025-05-22T06:12:44.132Z',
              updatedAt: '2025-05-22T06:12:44.132Z',
              id: 8,
              url: 'https://tsfera-vegmove.s3.ap-southeast-1.amazonaws.com/29b48f67-a49f-4053-bc25-f153ebedfc62.webp',
              mediaType: 'IMAGE',
              productId: 1,
              variationId: null,
            },
          ],
          thumbnail: {
            createdAt: '2025-05-22T06:12:44.135Z',
            updatedAt: '2025-05-22T06:12:44.135Z',
            id: 9,
            url: 'https://tsfera-vegmove.s3.ap-southeast-1.amazonaws.com/c17ad8d8-703d-4178-81b9-b0f6b56d1e42.webp',
            mediaType: 'IMAGE',
            productId: null,
            variationId: null,
          },
          category: {
            createdAt: '2025-05-15T14:16:53.879Z',
            updatedAt: '2025-05-24T08:10:36.800Z',
            id: 6,
            name: 'Atta/Maida',
            parentId: 1,
            type: 'CATEGORY',
            slug: 'atta-maida',
            isActive: true,
            bannerUrl: null,
            iconUrl:
              'https://tsfera-vegmove.s3.ap-southeast-1.amazonaws.com/ba2f11ce-7a3b-4c98-ae2c-97c0f5ec851a.png',
            categoryTranslation: [],
          },
          productTranslation: [],
          productPolicies: [
            {
              createdAt: '2025-05-22T06:12:44.145Z',
              updatedAt: '2025-05-22T06:12:44.145Z',
              id: 3,
              productId: 1,
              description: 'You can return within 7 days',
              details: {
                Basic: 'Template',
                Lorem: 'Ipsum',
              },
              productPolicyTypeId: 2,
              productPolicyType: {
                createdAt: '2025-05-20T06:25:37.367Z',
                updatedAt: '2025-05-20T06:25:37.367Z',
                id: 2,
                name: '7 days return',
                icon: 'https://tsfera-vegmove.s3.ap-southeast-1.amazonaws.com/fd695236-91cf-4d62-b9f8-0c8e5f8cdd54.png',
              },
            },
            {
              createdAt: '2025-05-22T06:12:44.145Z',
              updatedAt: '2025-05-22T06:12:44.145Z',
              id: 4,
              productId: 1,
              description: 'Get fast delivery!',
              details: {
                Lorem: 'Ipsum',
              },
              productPolicyTypeId: 1,
              productPolicyType: {
                createdAt: '2025-05-20T06:25:12.854Z',
                updatedAt: '2025-05-20T06:25:12.854Z',
                id: 1,
                name: 'Fast Delivery',
                icon: 'https://tsfera-vegmove.s3.ap-southeast-1.amazonaws.com/6c73b145-c5e4-4ff6-bab1-93b2e4a145d3.png',
              },
            },
          ],
          attributes: [],
          quantity: 82,
          originalPrice: '464.00',
          price: '434.00',
          inStock: true,
        },
        variation: null,
      },
    ],
    address: {
      createdAt: '2025-05-20T11:38:27.287Z',
      updatedAt: '2025-05-20T11:38:27.287Z',
      id: 4,
      userId: 2,
      type: 'BILLING',
      apartment: 'House ',
      block: 'Tverskoy District',
      streetName:
        'Moscow Kremlin, Mokhovaya Street, 18, 40, Tverskoy District, Moscow, Central Federal District, 103009, Russia',
      city: 'Moscow',
      state: 'Moscow',
      countryId: 1,
      zipCode: '103009',
      lat: '55.7516212',
      long: '37.6181220',
      isDeleted: false,
      country: {
        id: 1,
        name: 'India',
        code: 'IN',
        dialCode: '91',
        flagUrl: 'https://flagsapi.com/IN/flat/64.png',
      },
    },
    orderStatusHistory: [
      {
        createdAt: '2025-05-21T10:04:01.634Z',
        id: 19,
        orderId: 9,
        status: 'PENDING',
      },
      {
        createdAt: '2025-05-22T07:45:29.765Z',
        id: 20,
        orderId: 9,
        status: 'PACKING',
      },
    ],
    deliveryDriver: null,
    warehouse: {
      createdAt: '2025-05-15T14:21:52.037Z',
      updatedAt: '2025-05-15T14:21:52.037Z',
      id: 1,
      name: 'Moscow Pervomayskaya',
      lat: '55.78616290000001',
      long: '37.6408663',
      type: 'GENERAL',
      active: true,
    },
    user: {
      id: 2,
      name: 'Satya Ranjan',
      email: '<EMAIL>',
      createdAt: '2025-05-15T14:16:33.925Z',
      updatedAt: '2025-05-20T08:03:39.309Z',
      type: 'CUSTOMER',
      phone: null,
      profilePicture: null,
      countryId: 1,
      phoneCountry: {
        id: 1,
        name: 'India',
        code: 'IN',
        dialCode: '91',
        flagUrl: 'https://flagsapi.com/IN/flat/64.png',
      },
      rewardTierId: null,
      rewardTier: null,
      referralCode: 'SA335OJ',
      referredById: null,
      firebaseToken: null,
      tierExpiresAt: null,
    },
  },
  deliveryPerson: {
    name: 'John Doe',
    phone: '1234567890',
    assignedTime: '10:00 AM',
    currentStatus: 'On the way',
  },
};
