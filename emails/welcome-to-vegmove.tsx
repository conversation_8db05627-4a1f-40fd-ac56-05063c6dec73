/* eslint-disable prettier/prettier */
import { Text } from '@react-email/components';
import { User } from './_types';
import Layout from './_components/layout';
import Header from './_components/header';
import ButtonLink from './_components/button';

export default function WelcomeToVegmove(user: User) {
  return (
    <Layout>
      <Header
        title="🎉 Welcome to Vegmove"
        description={
          <Text>
            <span className="text-[15px] font-medium text-dark">
              {user.name},
            </span>
            <span className="text-text-sm text-gray block max-w-[400px] ">
              We’re excited to have you on board!
            </span>
            <span className="text-text-sm text-gray block">
              With Vegmove, you can enjoy fresh groceries, lightning-fast
              delivery,{' '}
            </span>
          </Text>
        }
      />
      <ButtonLink href="https://vegmove.techsfera.dev">
        Browse Products
      </ButtonLink>
    </Layout>
  );
}

WelcomeToVegmove.PreviewProps = {
  id: 2,
  name: '<PERSON><PERSON><PERSON>',
  email: '<EMAIL>',
  createdAt: '2025-05-15T14:16:33.925Z',
  updatedAt: '2025-05-20T08:03:39.309Z',
  type: 'CUSTOMER',
  phone: null,
  profilePicture: null,
  countryId: 1,
  phoneCountry: {
    id: 1,
    name: 'India',
    code: 'IN',
    dialCode: '91',
    flagUrl: 'https://flagsapi.com/IN/flat/64.png',
  },
  rewardTierId: null,
  rewardTier: null,
  referralCode: 'SA335OJ',
  referredById: null,
  firebaseToken: null,
  tierExpiresAt: null,
};
