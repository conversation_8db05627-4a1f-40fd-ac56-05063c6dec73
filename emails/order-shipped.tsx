/* eslint-disable prettier/prettier */
import { Hr, Img, Link, Section, Text } from '@react-email/components';
// import { OrderResponse } from './_types';
import Layout from './_components/layout';
import OrderStatus from './_components/order-status';
import Header from './_components/header';
import { OrderResponse } from 'src/types';

export default function OrderShipped({ order }: { order: OrderResponse }) {
  return (
    <Layout>
      <Header
        title="🚚 Order Shipped"
        orderId={order.id.toString()}
        description={
          <Text>
            <span className="text-[15px] font-medium text-dark">
              {order.user?.name},
            </span>
            <span className="text-text-sm text-gray block max-w-[400px] ">
              Your order has been shipped and is on its way to you
            </span>
          </Text>
        }
      />
      <OrderStatus
        status={order.status as 'CONFIRMED' | 'READY' | 'SHIPPED' | 'DELIVERED'}
      />

      <Section className=" bg-white p-4 mt-5 rounded-2xl">
        {order.items.map((item) => (
          <div className="flex gap-3 justify-start items-start" key={item.id}>
            <Img
              alt={item.product.name}
              height={70}
              width={70}
              src={item.product.thumbnail!.url}
              className="w-[70px] h-[70px] rounded-lg"
            />
            <div className="  max-w-[262px]">
              <span className="text-[15px] font-bold text-dark">
                {item.product.name}
              </span>
              <div className="flex justify-start items-center text-[15px] font-medium mt-1">
                <div>
                  <span className="text-gray">Quantity: </span>
                  <span className="text-dark">{item.quantity} pc</span>
                </div>
                {/* <div>
                        <span className="text-gray">Color: </span>
                        <span className="text-dark">
                          {item.variation?.options.map((item) => (
                            <div className="">{item.option.name}</div>
                          ))}{' '}
                          pc
                        </span>
                      </div> */}
              </div>
            </div>
            <div className="flex items-center gap-3 justify-end">
              <span className="line-through text-md font-semibold text-gray">
                ₹{item.originalPrice.toString()}
              </span>
              <span className="font-bold text-[23px] text-dark">
                ₹{item.price.toString()}
              </span>
            </div>
          </div>
        ))}

        <Hr className="mt-3" />

        <div className="flex flex-col gap-3 mt-6">
          <span className="text-[15px] font-bold text-dark">Order Details</span>

          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Item Total & GST
            </span>
            <span className="font-semibold text-dark text-[15px]">
              ₹{order?.subtotal.toString()}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Handling Charge
            </span>
            <span className="font-semibold text-dark text-[15px]">
              ₹{order?.handlingCharge.toString() ?? '0.00'}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Delivery Fee
            </span>
            <span className="font-semibold text-dark text-[15px]">
              ₹{order?.deliveryFee.toString() ?? '0.00'}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Coupon Discount
            </span>
            <span className="font-semibold text-dark text-[15px]">
              ₹{order?.couponDiscount.toString() ?? '0.00'}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray text-[15px] font-medium">
              Reward Points Discount
            </span>
            <span className="font-semibold text-dark text-[15px]">
              ₹{order?.rewardDiscount.toString() ?? '0.00'}
            </span>
          </div>
        </div>
        <Hr className="mt-3" />

        <div className="flex justify-between items-center  border-y border-gray-200">
          <span className="text-dark text-[15px] font-bold ">Total Paid</span>
          <span className="font-bold text-dark text-[18px]">
            ₹{order?.totalAmount.toString()}
          </span>
        </div>
      </Section>

      <Text className="text-gray text-[15px] font-normal mt-5 mb-0">
        If you don’t recognize this order, please{' '}
        <Link href="#" className="text-brand font-medium  underline">
          contact us
        </Link>{' '}
        immediately.
      </Text>
      <span className="text-dark text-[15px] font-medium mt-1">
        Your Order ID is #{order.id}
      </span>
    </Layout>
  );
}

OrderShipped.PreviewProps = {
  order: {
    createdAt: '2025-05-21T10:04:01.634Z',
    updatedAt: '2025-05-22T07:45:29.754Z',
    id: 9,
    userId: 2,
    status: 'SHIPPED',
    addressId: 4,
    subtotal: '954.8',
    handlingCharge: '0',
    deliveryFee: '0',
    discountAmount: '0',
    couponDiscount: '0',
    rewardDiscount: '0',
    totalAmount: '954.8',
    deliveryDate: '2025-05-21T10:02:59.491Z',
    deliveryTime: null,
    deliveryStartTime: '15:00',
    deliveryEndTime: '17:00',
    note: '',
    cashToPay: '0',
    couponId: null,
    deliveryDriverId: null,
    useRewardPoints: false,
    rewardPointsUsed: 0,
    warehouseId: 1,
    deliveryDriverAssignedDate: null,
    paymentStatus: 'PENDING',
    items: [
      {
        id: 9,
        orderId: 9,
        productId: 1,
        quantity: 2,
        price: '954.8',
        originalPrice: '1020.8',
        gstAmount: '43.4',
        inventoryId: 1,
        variationId: null,
        product: {
          createdAt: '2025-05-15T16:16:17.757Z',
          updatedAt: '2025-05-22T06:12:44.138Z',
          id: 1,
          barcode: '8906010070281',
          name: 'Aashirvaad Atta - Shudh Chakki Atta, No Maida | Pouch',
          description: 'Aashirvaad Atta - Shudh Chakki Atta, No Maida | Pouch',
          highlights: {
            Brand: 'Aashirvaad',
            Weight: '10 kg',
            'Model Name': 'Shudh Chakki',
            'Key Features':
              '100% Whole Wheat, No Maida, Soft and Fluffy Rotis, Traditional Chakki grinding',
            'Product Type': 'Chakki Atta',
            'Material Type Free': 'Maida-free',
          },
          information: {
            Disclaimer:
              'All images are for representational purposes only. It is advised that you read the batch and manufacturing details, directions for use, allergen information, health and nutritional claims (wherever applicable), and other details mentioned on the label before consuming the product. For combo items, individual prices can be viewed on the page.',
            'Shelf Life': '93 days',
            'Seller Name': 'Commodum Groceries Private Limited',
            'Seller Address':
              'COMMODUM GROCERIES PRIVATE LIMITED, Regd. Office: 44, Saket Building, Mullick Bazaar, Park Street, Kolkata, West Bengal, India, 700016. For Support ReachOut : <EMAIL>',
            'Country of Origin': 'India',
            'Manufacturer Name': 'ITC Limited',
            'Seller License No.': '12822999000310',
            'Manufacturer Address':
              'ITC Limited, 37, J.L. Nehru Road, Kolkata- 700071',
            'Customer Care Details':
              'In case of any issue, contact us E-mail address: <EMAIL>',
          },
          categoryId: 6,
          weight: 10000,
          weightUnit: 'GRAM',
          length: 0,
          width: 0,
          height: 0,
          gstPercentage: 10,
          supplierId: null,
          slug: 'aashirvaad-atta-shudh-chakki-atta-no-maida-pouch',
          discountValue: 30,
          discountType: 'FLAT',
          hasVariations: false,
          thumbnailId: 9,
          active: true,
          maxCartQuantity: null,
          media: [
            {
              createdAt: '2025-05-22T06:12:44.132Z',
              updatedAt: '2025-05-22T06:12:44.132Z',
              id: 8,
              url: 'https://tsfera-vegmove.s3.ap-southeast-1.amazonaws.com/29b48f67-a49f-4053-bc25-f153ebedfc62.webp',
              mediaType: 'IMAGE',
              productId: 1,
              variationId: null,
            },
          ],
          thumbnail: {
            createdAt: '2025-05-22T06:12:44.135Z',
            updatedAt: '2025-05-22T06:12:44.135Z',
            id: 9,
            url: 'https://tsfera-vegmove.s3.ap-southeast-1.amazonaws.com/c17ad8d8-703d-4178-81b9-b0f6b56d1e42.webp',
            mediaType: 'IMAGE',
            productId: null,
            variationId: null,
          },
          category: {
            createdAt: '2025-05-15T14:16:53.879Z',
            updatedAt: '2025-05-24T08:10:36.800Z',
            id: 6,
            name: 'Atta/Maida',
            parentId: 1,
            type: 'CATEGORY',
            slug: 'atta-maida',
            isActive: true,
            bannerUrl: null,
            iconUrl:
              'https://tsfera-vegmove.s3.ap-southeast-1.amazonaws.com/ba2f11ce-7a3b-4c98-ae2c-97c0f5ec851a.png',
            categoryTranslation: [],
          },
          productTranslation: [],
          productPolicies: [
            {
              createdAt: '2025-05-22T06:12:44.145Z',
              updatedAt: '2025-05-22T06:12:44.145Z',
              id: 3,
              productId: 1,
              description: 'You can return within 7 days',
              details: {
                Basic: 'Template',
                Lorem: 'Ipsum',
              },
              productPolicyTypeId: 2,
              productPolicyType: {
                createdAt: '2025-05-20T06:25:37.367Z',
                updatedAt: '2025-05-20T06:25:37.367Z',
                id: 2,
                name: '7 days return',
                icon: 'https://tsfera-vegmove.s3.ap-southeast-1.amazonaws.com/fd695236-91cf-4d62-b9f8-0c8e5f8cdd54.png',
              },
            },
            {
              createdAt: '2025-05-22T06:12:44.145Z',
              updatedAt: '2025-05-22T06:12:44.145Z',
              id: 4,
              productId: 1,
              description: 'Get fast delivery!',
              details: {
                Lorem: 'Ipsum',
              },
              productPolicyTypeId: 1,
              productPolicyType: {
                createdAt: '2025-05-20T06:25:12.854Z',
                updatedAt: '2025-05-20T06:25:12.854Z',
                id: 1,
                name: 'Fast Delivery',
                icon: 'https://tsfera-vegmove.s3.ap-southeast-1.amazonaws.com/6c73b145-c5e4-4ff6-bab1-93b2e4a145d3.png',
              },
            },
          ],
          attributes: [],
          quantity: 82,
          originalPrice: '464.00',
          price: '434.00',
          inStock: true,
        },
        variation: null,
      },
    ],
    address: {
      createdAt: '2025-05-20T11:38:27.287Z',
      updatedAt: '2025-05-20T11:38:27.287Z',
      id: 4,
      userId: 2,
      type: 'BILLING',
      apartment: 'House ',
      block: 'Tverskoy District',
      streetName:
        'Moscow Kremlin, Mokhovaya Street, 18, 40, Tverskoy District, Moscow, Central Federal District, 103009, Russia',
      city: 'Moscow',
      state: 'Moscow',
      countryId: 1,
      zipCode: '103009',
      lat: '55.7516212',
      long: '37.6181220',
      isDeleted: false,
      country: {
        id: 1,
        name: 'India',
        code: 'IN',
        dialCode: '91',
        flagUrl: 'https://flagsapi.com/IN/flat/64.png',
      },
    },
    orderStatusHistory: [
      {
        createdAt: '2025-05-21T10:04:01.634Z',
        id: 19,
        orderId: 9,
        status: 'PENDING',
      },
      {
        createdAt: '2025-05-22T07:45:29.765Z',
        id: 20,
        orderId: 9,
        status: 'PACKING',
      },
    ],
    deliveryDriver: null,
    warehouse: {
      createdAt: '2025-05-15T14:21:52.037Z',
      updatedAt: '2025-05-15T14:21:52.037Z',
      id: 1,
      name: 'Moscow Pervomayskaya',
      lat: '55.78616290000001',
      long: '37.6408663',
      type: 'GENERAL',
      active: true,
    },
    user: {
      id: 2,
      name: 'Satya Ranjan',
      email: '<EMAIL>',
      createdAt: '2025-05-15T14:16:33.925Z',
      updatedAt: '2025-05-20T08:03:39.309Z',
      type: 'CUSTOMER',
      phone: null,
      profilePicture: null,
      countryId: 1,
      phoneCountry: {
        id: 1,
        name: 'India',
        code: 'IN',
        dialCode: '91',
        flagUrl: 'https://flagsapi.com/IN/flat/64.png',
      },
      rewardTierId: null,
      rewardTier: null,
      referralCode: 'SA335OJ',
      referredById: null,
      firebaseToken: null,
      tierExpiresAt: null,
    },
  },
};
