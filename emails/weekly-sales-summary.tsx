/* eslint-disable prettier/prettier */
import { Img, Section, Text } from '@react-email/components';
import Layout from './_components/layout';
import Header from './_components/header';
import ButtonLink from './_components/button';

export default function WeeklySalesSummary({
  totalOrders,
  totalRevenue,
  avgOrderValue,
}: {
  totalOrders: number;
  totalRevenue: number;
  avgOrderValue: number;
}) {
  return (
    <Layout>
      <Header
        title="📈 Weekly Sales Summary"
        description={
          <Text>
            <span className="text-text-sm text-gray block max-w-[400px] ">
              Date:{' '}
              <span className="text-dark font-medium">
                May 15 – May 21, 2025
              </span>
            </span>
          </Text>
        }
      />
      <Img
        src="https://i.ibb.co/MyZ661CM/Group.png"
        alt="Group"
        width={270}
        height={158}
        className="mt-3 mb-7 select-none"
      />

      <Section className="p-4 rounded-2xl bg-white mb-5 ">
        <div className="flex justify-between items-center w-full">
          <div className="flex justify-start items-center gap-3">
            <div className="w-[40px] h-[40px] bg-[#E8F1ED] rounded-full flex items-center justify-center">
              <svg
                width="18"
                height="18"
                viewBox="0 0 18 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M11.894 0.0419922C12.6464 0.0419733 13.2757 0.0419183 13.7915 0.0957031C14.3371 0.152617 14.8311 0.275103 15.2866 0.566406C15.742 0.857689 16.0494 1.24784 16.314 1.70898C16.5641 2.14506 16.8069 2.70197 17.0962 3.36816L17.896 5.20996C17.9521 5.33925 17.9682 5.47475 17.9517 5.60449C17.9537 5.62516 17.9585 5.64578 17.9585 5.66699L17.9585 10.2393C17.9585 11.8813 17.9585 13.1797 17.8169 14.1953C17.6711 15.2413 17.3648 16.0799 16.6831 16.7373C16.0045 17.3916 15.1444 17.683 14.0708 17.8223C13.0221 17.9582 11.679 17.958 9.97217 17.958H8.02881C6.32195 17.958 4.97886 17.9582 3.93018 17.8223C2.85647 17.6831 1.99647 17.3916 1.31787 16.7373C0.636102 16.0798 0.329933 15.2414 0.184082 14.1953C0.0425054 13.1797 0.042467 11.8813 0.0424805 10.2393L0.0424805 5.66699C0.0424805 5.64579 0.0462787 5.62515 0.0483398 5.60449C0.0317603 5.47469 0.0488106 5.33931 0.10498 5.20996L0.904785 3.36816C1.19405 2.70215 1.43594 2.14499 1.68604 1.70898C1.95065 1.24774 2.25886 0.857709 2.71436 0.566406C3.16979 0.275194 3.66299 0.152612 4.2085 0.0957031C4.72441 0.0418864 5.3544 0.0419733 6.10693 0.0419922L11.894 0.0419922ZM7.3335 8.16699C6.87341 8.16717 6.50049 8.53987 6.50049 9C6.50049 9.46013 6.87341 9.83283 7.3335 9.83301L10.6675 9.83301C11.1274 9.83266 11.5005 9.46002 11.5005 9C11.5005 8.53998 11.1274 8.16734 10.6675 8.16699L7.3335 8.16699ZM6.1499 1.60449C5.3427 1.60449 4.80183 1.60488 4.38428 1.64844C3.98685 1.68992 3.7776 1.76398 3.61768 1.86621C3.45769 1.96853 3.3057 2.12566 3.11279 2.46191C2.91031 2.81489 2.70153 3.29313 2.39111 4.00781L1.94189 5.04199L8.1665 5.04199L8.1665 1.60449L6.1499 1.60449ZM9.8335 1.60449L9.8335 5.04199L16.0581 5.04199L15.6089 4.00781C15.2985 3.29312 15.0897 2.81488 14.8872 2.46191C14.6943 2.12567 14.5423 1.96852 14.3823 1.86621C14.2224 1.76403 14.0131 1.68989 13.6157 1.64844C13.1982 1.60488 12.6573 1.60449 11.8501 1.60449L9.8335 1.60449Z"
                  fill="#00AB55"
                />
              </svg>
            </div>
            <div className="flex flex-col">
              <span className="text-gray text-sm">Total Orders</span>
              <span className="text-dark text-[15px] font-bold">
                {totalOrders}
              </span>
            </div>
          </div>
          <div className="flex justify-start items-center gap-3 ">
            <div className="w-[40px] h-[40px] bg-[#E8F1ED] rounded-full flex items-center justify-center">
              <svg
                width="18"
                height="19"
                viewBox="0 0 18 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12.7334 5.125C13.1894 5.13966 13.6017 5.16335 13.9746 5.20313C14.9429 5.30642 15.7281 5.52211 16.3809 6.00879C16.5763 6.15455 16.7581 6.31572 16.9229 6.49023C17.478 7.07829 17.7257 7.79154 17.8438 8.66602C17.9298 9.30309 17.9517 10.067 17.957 10.9814L17.958 11.9473V12.0303C17.958 13.387 17.9585 14.462 17.8438 15.3115C17.7257 16.186 17.478 16.8992 16.9229 17.4873C16.7581 17.6618 16.5763 17.822 16.3809 17.9678C15.7281 18.4546 14.943 18.6701 13.9746 18.7734C13.2631 18.8493 12.4082 18.8683 11.3789 18.873L10.29 18.875H7.70996C6.17766 18.875 4.97413 18.8747 4.02539 18.7734C3.05698 18.6701 2.27192 18.4546 1.61914 17.9678C1.42373 17.822 1.24188 17.6618 1.07715 17.4873C0.522003 16.8992 0.27433 16.186 0.15625 15.3115C0.0702312 14.6745 0.048341 13.9105 0.0429687 12.9961L0.0419922 12.0303L0.0419922 11.9473C0.0419812 10.5906 0.0415691 9.5155 0.15625 8.66602C0.274328 7.79154 0.522022 7.07829 1.07715 6.49023C1.2419 6.31572 1.42371 6.15455 1.61914 6.00879C2.27187 5.52211 3.05714 5.30642 4.02539 5.20313C4.39826 5.16335 4.81055 5.13966 5.2666 5.125C5.30679 5.125 5.3439 5.147 5.36426 5.18164L5.3877 5.22168C5.50545 5.41697 5.69897 5.66461 5.78223 5.77051C6.02191 6.07625 6.3794 6.53211 6.75 6.91309C6.93491 7.10313 7.18143 7.33585 7.46973 7.53418C7.70548 7.69636 8.25685 8.04199 9 8.04199C9.74309 8.04194 10.2945 7.69634 10.5303 7.53418C10.8185 7.33588 11.0642 7.10309 11.249 6.91309C11.6197 6.53208 11.978 6.07633 12.2178 5.77051C12.2801 5.69124 12.4039 5.53199 12.5117 5.375L12.6113 5.22168C12.6171 5.21225 12.6259 5.19849 12.6357 5.18164C12.6563 5.14707 12.6932 5.125 12.7334 5.125ZM9 9.90332C7.61938 9.90343 6.5 11.0253 6.5 12.4082C6.50024 13.7909 7.61953 14.912 9 14.9121C10.3806 14.9121 11.4998 13.791 11.5 12.4082C11.5 11.0252 10.3807 9.90332 9 9.90332ZM3.57617 11.5703C3.11593 11.5703 2.74219 11.9443 2.74219 12.4053C2.74233 12.8661 3.11602 13.2393 3.57617 13.2393H3.58301C4.04316 13.2393 4.41685 12.8661 4.41699 12.4053C4.41699 11.9443 4.04325 11.5703 3.58301 11.5703H3.57617ZM14.416 11.5703C13.9558 11.5703 13.582 11.9443 13.582 12.4053C13.5822 12.8661 13.9559 13.2393 14.416 13.2393H14.4229C14.883 13.2393 15.2567 12.8661 15.2568 12.4053C15.2568 11.9443 14.8831 11.5703 14.4229 11.5703H14.416Z"
                  fill="#00AB55"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M9.83805 0.958242C9.83805 0.498004 9.46496 0.124908 9.00472 0.124908C8.54448 0.124908 8.17139 0.498004 8.17139 0.958242L8.17139 3.04161L7.67617 3.04159C7.52981 3.04149 7.3501 3.04136 7.20306 3.05975L7.20028 3.0601C7.0949 3.07324 6.61487 3.1331 6.38623 3.60443C6.1571 4.07678 6.40871 4.49357 6.46315 4.58374L6.46518 4.5871C6.54202 4.71462 6.65381 4.85701 6.74577 4.97414L6.76554 4.99935C7.01107 5.31254 7.32938 5.71605 7.6464 6.04189C7.80459 6.20448 7.98565 6.37218 8.17807 6.50455C8.34912 6.62222 8.64095 6.79161 8.99984 6.79161C9.35873 6.79161 9.65055 6.62222 9.8216 6.50455C10.014 6.37218 10.1951 6.20448 10.3533 6.04189C10.6703 5.71605 10.9886 5.31254 11.2341 4.99935L11.2539 4.97415C11.3459 4.85702 11.4577 4.71462 11.5345 4.5871L11.5365 4.58374C11.591 4.49357 11.8426 4.07678 11.6134 3.60443C11.3848 3.1331 10.9048 3.07324 10.7994 3.0601L10.7966 3.05975C10.6496 3.04136 10.4699 3.04149 10.3235 3.04159L9.83805 3.04161L9.83805 0.958242Z"
                  fill="#00AB55"
                />
              </svg>
            </div>
            <div className="flex flex-col">
              <span className="text-gray text-sm">Total Revenue</span>
              <span className="text-dark text-[15px] font-bold">
                ₹{totalRevenue}
              </span>
            </div>
          </div>
          <div className="flex justify-start items-center gap-3">
            <div className="w-[40px] h-[40px] bg-[#E8F1ED] rounded-full flex items-center justify-center">
              <svg
                width="18"
                height="20"
                viewBox="0 0 18 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M9 2.39583C7.61525 2.39583 6.46168 3.43833 6.34668 4.79365L6.21319 6.36693C6.17671 6.79691 5.79223 7.11643 5.35443 7.0806C4.91664 7.04477 4.59131 6.66716 4.62779 6.23718L4.76128 4.66389C4.94499 2.49874 6.78785 0.833344 9 0.833344C11.2121 0.833344 13.055 2.49874 13.2387 4.66389L13.3722 6.23718C13.4087 6.66716 13.0834 7.04477 12.6456 7.0806C12.2078 7.11643 11.8233 6.79691 11.7868 6.36693L11.6533 4.79365C11.5383 3.43833 10.3847 2.39583 9 2.39583Z"
                  fill="#00AB55"
                />
                <path
                  d="M11.8579 5.83496C12.5297 5.83872 13.0998 5.85376 13.5796 5.91406C14.246 5.99784 14.8132 6.17626 15.2964 6.58594C15.7785 6.99482 16.0499 7.52645 16.2476 8.17285C16.4378 8.79514 16.5786 9.59208 16.7524 10.5781L17.0728 12.3936C17.3143 13.7632 17.5074 14.8572 17.5376 15.7227C17.5687 16.6149 17.4331 17.3806 16.9106 18.0127C16.3868 18.6463 15.6625 18.9175 14.7856 19.0439C13.9373 19.1663 12.838 19.167 11.4653 19.167H6.53369C5.16121 19.167 4.0626 19.1662 3.21436 19.0439C2.33734 18.9175 1.61326 18.6464 1.08936 18.0127C0.566759 17.3805 0.431294 16.615 0.462402 15.7227C0.492606 14.8572 0.685717 13.7633 0.927246 12.3936L1.24756 10.5781L1.37256 9.87402C1.49433 9.20474 1.6088 8.63963 1.75146 8.17285C1.94911 7.5263 2.22134 6.99487 2.70361 6.58594C3.18678 6.17628 3.75399 5.99783 4.42041 5.91406C5.06004 5.83369 5.86034 5.83299 6.84814 5.83301L11.1519 5.83301L11.8579 5.83496ZM11.5757 8.12891C11.1176 8.08696 10.7116 8.42371 10.6694 8.88184C10.6066 9.56516 9.94257 10.208 8.99951 10.208C8.05658 10.2079 7.39246 9.56511 7.32959 8.88184C7.28735 8.42361 6.88159 8.08674 6.42334 8.12891C5.96508 8.17112 5.62727 8.57688 5.66943 9.03516C5.82347 10.7067 7.33334 11.8749 8.99951 11.875C10.6658 11.875 12.1755 10.7067 12.3296 9.03516C12.3718 8.57685 12.034 8.17108 11.5757 8.12891Z"
                  fill="#00AB55"
                />
              </svg>
            </div>
            <div className="flex flex-col">
              <span className="text-gray text-sm">Avg. Order Value</span>
              <span className="text-dark text-[15px] font-bold">
                ₹{avgOrderValue}
              </span>
            </div>
          </div>
        </div>
      </Section>

      <ButtonLink href="https://vegmove.techsfera.dev">
        View Full Report
      </ButtonLink>
    </Layout>
  );
}

WeeklySalesSummary.PreviewProps = {
  totalOrders: 120,
  totalRevenue: 5000,
  avgOrderValue: 12524,
};
