/* eslint-disable prettier/prettier */
import { Img, Text } from '@react-email/components';
import Header from './_components/header';
import Layout from './_components/layout';

interface InviteFriendsProps {
  referralCode: string;
}

export default function InviteFriends({ referralCode }: InviteFriendsProps) {
  return (
    <Layout>
      <Header
        title="🎉 Invite Friends"
        description={
          <Text>
            <span className="text-text-sm text-gray block max-w-[400px] ">
              Share the freshness, get rewarded 🌟
            </span>
            <span className="text-text-sm text-gray block">
              Refer a friend to Vegmove and earn{' '}
              <span className="text-brand  font-medium">
                500 loyalty points
              </span>{' '}
              when
            </span>
            <span className="text-text-sm text-gray block">
              they <span className="text-brand font-medium">spend ₹5000</span>{' '}
              in their first month!
            </span>
            <span className="text-text-sm text-gray block mt-2">
              Your referral code:{' '}
              <span className="text-brand font-medium">{referralCode}</span>
            </span>
          </Text>
        }
      />

      <Img
        src="https://i.ibb.co/DDmhrYvg/Container.png"
        alt="Container"
        width={173.58}
        height={150.4}
        className="mt-3 mb-7 select-none"
      />

      {/* <ButtonLink href="https://vegmove.techsfera.dev">
        Refer a Friend
      </ButtonLink> */}
    </Layout>
  );
}

InviteFriends.PreviewProps = {
  referralCode: 'FRIEND123',
};
