/* eslint-disable prettier/prettier */
import { Text } from '@react-email/components';
import Layout from './_components/layout';
import Header from './_components/header';
import ButtonLink from './_components/button';

export default function VerifyEmail() {
  return (
    <Layout>
      <Header
        title="🔐 Verify Email"
        description={
          <Text>
            <span className="text-text-sm text-gray block max-w-[400px] ">
              This helps us keep your account secure and ensures you never miss
              an update
            </span>
            <span className="text-text-sm text-gray block">
              If you didn’t create this account, ignore this message.
            </span>
          </Text>
        }
      />
      <ButtonLink href="https://vegmove.techsfera.dev">Verify Email</ButtonLink>
    </Layout>
  );
}

VerifyEmail.PreviewProps = {};
