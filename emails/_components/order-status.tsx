/* eslint-disable prettier/prettier */

interface OrderStatusProps {
  status: 'CONFIRMED' | 'READY' | 'SHIPPED' | 'DELIVERED';
}

export default function OrderStatus({
  status = 'CONFIRMED',
}: OrderStatusProps) {
  const steps = ['CONFIRMED', 'READY', 'SHIPPED', 'DELIVERED'] as const;
  const currentStepIndex = steps.indexOf(status);

  if (currentStepIndex === -1) {
    return;
  }
  const progressPercentage = (currentStepIndex / (steps.length - 1)) * 100;

  const isActive = (step: (typeof steps)[number]) =>
    steps.indexOf(step) <= currentStepIndex;

  return (
    <div className="relative flex items-center justify-between bg-white p-4 rounded-xl ">
      {/* Progress Line */}
      <div className="absolute top-[31%] -translate-y-1/2 left-[10%] right-[10%] h-1 bg-[#D2DED8] z-0">
        <div
          className="h-full  bg-brand"
          style={{ width: `${progressPercentage}%` }}
        ></div>
      </div>

      {/* Steps */}
      <div className="relative flex flex-1 justify-between">
        {/* Confirmed Step */}
        <div className="flex flex-col items-center">
          <div
            className={`w-[42px] h-[42px] rounded-full flex items-center justify-center ${
              isActive('CONFIRMED') ? 'bg-brand' : 'bg-[#D2DED8]'
            }`}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="12"
              viewBox="0 0 16 12"
              fill="none"
            >
              <path
                d="M15.2657 0.640036C15.4418 1.11468 15.1998 1.64222 14.7251 1.81833C13.7545 2.17845 12.6897 2.92804 11.6117 3.91844C10.5439 4.89942 9.51501 6.06819 8.61231 7.2018C7.7114 8.33316 6.94742 9.41531 6.40841 10.2153C6.13922 10.6148 5.92694 10.9427 5.78259 11.1697C5.71043 11.2832 5.6553 11.3714 5.61857 11.4306L5.57748 11.4972L5.56762 11.5134L5.56542 11.517C5.39704 11.7945 5.09396 11.963 4.76934 11.9588C4.44467 11.9546 4.14646 11.779 3.98536 11.497C3.11523 9.9743 2.39642 9.28867 1.96391 8.98591C1.74899 8.83547 1.60278 8.77802 1.53847 8.75755C1.52329 8.75272 1.51214 8.74978 1.50513 8.74811C1.03052 8.71342 0.65625 8.31737 0.65625 7.83391C0.65625 7.32765 1.06666 6.91724 1.57292 6.91724C1.68108 6.92453 1.9368 6.95339 2.09434 7.01052C2.34514 7.09032 2.65726 7.23339 3.01526 7.48399C3.52839 7.84318 4.13054 8.4193 4.78666 9.34183C4.81953 9.29266 4.85332 9.24231 4.88801 9.19083C5.44596 8.36275 6.23897 7.23917 7.17813 6.05976C8.11551 4.88261 9.20975 3.63558 10.3713 2.56839C11.5226 1.51061 12.7933 0.579645 14.0874 0.09949C14.562 -0.0766177 15.0896 0.165393 15.2657 0.640036Z"
                fill={isActive('CONFIRMED') ? 'white' : '#6C7073'}
              />
            </svg>
          </div>
          <span className="text-[15px] text-brand font-medium  mt-3">
            Confirmed
          </span>
        </div>

        {/* Packed Step */}
        <div className="flex flex-col items-center">
          <div
            className={`w-[42px] h-[42px] rounded-full flex items-center justify-center ${
              isActive('READY') ? 'bg-brand' : 'bg-[#D2DED8]'
            }`}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                d="M19.2573 12.406C19.4472 12.8753 19.2208 13.4097 18.7515 13.5996C18.3537 13.7606 17.8733 14.1181 17.3491 14.6435C16.8367 15.157 16.3366 15.7756 15.8924 16.3841C15.4501 16.9901 15.0741 17.571 14.8084 18.0012C14.6759 18.2157 14.4677 18.5726 14.3971 18.6938L14.3963 18.6953C14.2269 18.9997 13.8995 19.1821 13.5516 19.1656C13.2035 19.149 12.8949 18.9366 12.7552 18.6173C12.3972 17.7989 12.0798 17.484 11.9366 17.3749C11.901 17.3477 11.8746 17.3321 11.8588 17.3238C11.4151 17.2598 11.0742 16.878 11.0742 16.4166C11.0742 15.9103 11.4846 15.4999 11.9909 15.4999C12.1807 15.4999 12.3202 15.5435 12.4133 15.5738C12.6011 15.6352 12.8176 15.7413 13.0477 15.9166C13.2319 16.0569 13.4202 16.2379 13.6085 16.4701C13.8389 16.1161 14.1103 15.716 14.4116 15.3032C14.8875 14.6513 15.449 13.9522 16.0513 13.3486C16.6418 12.7567 17.3291 12.1975 18.0636 11.9002C18.5329 11.7103 19.0673 11.9367 19.2573 12.406Z"
                fill={isActive('READY') ? 'white' : '#6C7073'}
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.23828 1.89551C8.62438 1.89551 8.03259 2.15317 6.34387 2.95318L3.73304 4.19003C2.9899 4.54208 2.52434 4.76511 2.23252 4.95801C2.52434 5.1509 2.9899 5.37394 3.73304 5.72599L4.71785 6.19253L11.7268 2.76155C10.3436 2.1114 9.80023 1.89551 9.23828 1.89551ZM13.7587 3.72349L6.74972 7.15446C8.13291 7.80461 8.67634 8.02051 9.23828 8.02051C9.85218 8.02051 10.444 7.76285 12.1327 6.96283L14.7435 5.72599C15.4867 5.37394 15.9522 5.1509 16.244 4.95801C15.9522 4.76511 15.4867 4.54208 14.7435 4.19002L13.7587 3.72349ZM5.74659 1.29195C7.18083 0.611633 8.16352 0.145508 9.23828 0.145508C10.313 0.145508 11.2957 0.611633 12.73 1.29195C12.7896 1.32024 12.85 1.3489 12.9113 1.37791L15.5827 2.64346C16.2471 2.95813 16.8329 3.23557 17.245 3.50811C17.6576 3.78097 18.1758 4.22852 18.1758 4.95801C18.1758 5.68749 17.6576 6.13504 17.245 6.40791C16.8329 6.68044 16.2471 6.95789 15.5827 7.27256L12.9113 8.5381C12.85 8.56712 12.7896 8.59578 12.73 8.62407C11.2957 9.30438 10.313 9.77051 9.23828 9.77051C8.16353 9.77051 7.18084 9.30438 5.7466 8.62407C5.68695 8.59578 5.62652 8.56712 5.56528 8.5381L2.95445 7.30126C2.93417 7.29165 2.91397 7.28208 2.89383 7.27254C2.22947 6.95788 1.64369 6.68044 1.23157 6.40791C0.818947 6.13504 0.300781 5.68749 0.300781 4.95801C0.300781 4.22852 0.818947 3.78097 1.23157 3.50811C1.64369 3.23558 2.22947 2.95814 2.89383 2.64347C2.91397 2.63394 2.93417 2.62436 2.95445 2.61476L5.56528 1.37791C5.62652 1.3489 5.68694 1.32024 5.74659 1.29195Z"
                fill={isActive('READY') ? 'white' : '#6C7073'}
              />
              <path
                d="M9.23828 19.8555C9.80565 19.8555 10.3521 19.7122 11.0098 19.4658C11.259 19.3724 11.3842 19.3255 11.4277 19.207C11.4711 19.0883 11.4089 18.9768 11.2842 18.7549C11.2725 18.7341 11.2608 18.7139 11.25 18.6953C11.2157 18.6366 11.1985 18.6071 11.168 18.5791C11.1373 18.551 11.0949 18.531 11.0107 18.4912C10.234 18.1243 9.69727 17.3344 9.69727 16.418C9.69749 15.1526 10.7229 14.1271 11.9883 14.127C12.399 14.127 12.705 14.2252 12.8379 14.2686C12.8539 14.2738 12.8697 14.2796 12.8857 14.2852C13.1151 14.3642 13.2303 14.4036 13.3154 14.3779C13.4006 14.3522 13.4661 14.2653 13.5977 14.0928C14.0335 13.5213 14.5342 12.9212 15.0752 12.3789C15.7157 11.7369 16.559 11.0274 17.5459 10.6279C17.5939 10.6085 17.6419 10.5902 17.6904 10.5742C17.9442 10.4906 18.0717 10.449 18.124 10.377C18.1762 10.3048 18.1758 10.1942 18.1758 9.97363L18.1758 4.95996C18.1758 4.93322 18.1749 4.9068 18.1719 4.88086C18.1574 4.75519 18.1498 4.69239 18.0254 4.62402C17.9008 4.5556 17.8096 4.5993 17.6279 4.6875L14.9844 5.9707L12.3066 7.2666C10.5947 8.095 9.9327 8.39746 9.23828 8.39746C8.54394 8.39737 7.88184 8.09498 6.16992 7.2666L3.49219 5.9707L0.848633 4.6875C0.667199 4.59941 0.576665 4.55566 0.452148 4.62402C0.327549 4.69244 0.320122 4.75511 0.305664 4.88086C0.302685 4.90682 0.300786 4.93321 0.300781 4.95996L0.300781 14.7334C0.301003 15.7602 1.05064 16.4428 1.97266 16.9961C2.90613 17.5562 4.47278 18.2182 6.20801 18.9512C7.50864 19.501 8.34737 19.8554 9.23828 19.8555ZM6.18652 11.2246C6.01672 11.5642 5.60426 11.702 5.26465 11.5322L3.43164 10.6162C3.09203 10.4464 2.95422 10.033 3.12402 9.69336C3.2938 9.35392 3.70638 9.2162 4.0459 9.38574L5.87988 10.3027C6.21923 10.4726 6.3562 10.8851 6.18652 11.2246Z"
                fill={isActive('READY') ? 'white' : '#6C7073'}
              />
            </svg>
          </div>
          <span className="text-[15px] text-brand font-medium  mt-3">
            Packed
          </span>
        </div>

        {/* On the Way Step */}
        <div className="flex flex-col items-center">
          <div
            className={`w-[42px] h-[42px] rounded-full flex items-center justify-center ${
              isActive('SHIPPED') ? 'bg-brand' : 'bg-[#D2DED8]'
            }`}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="21"
              height="17"
              viewBox="0 0 21 17"
              fill="none"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M0.0859375 5.33366C0.0859375 4.8274 0.496343 4.41699 1.0026 4.41699L6.5026 4.41699C7.00887 4.41699 7.41927 4.8274 7.41927 5.33366C7.41927 5.83992 7.00887 6.25033 6.5026 6.25033L1.0026 6.25033C0.496343 6.25033 0.0859375 5.83992 0.0859375 5.33366ZM0.0859375 8.08366C0.0859375 7.5774 0.496343 7.16699 1.0026 7.16699L4.66927 7.16699C5.17553 7.16699 5.58594 7.5774 5.58594 8.08366C5.58594 8.58992 5.17553 9.00033 4.66927 9.00033L1.0026 9.00033C0.496343 9.00033 0.0859375 8.58992 0.0859375 8.08366Z"
                fill={isActive('SHIPPED') ? 'white' : '#6C7073'}
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8.74617 0.979524C9.64737 0.979169 10.263 0.978926 10.7833 1.1479C11.5567 1.39904 12.2067 1.91208 12.631 2.58466L13.9581 2.58465C14.589 2.58463 15.1292 2.58461 15.5775 2.62985C16.0566 2.67821 16.5046 2.78359 16.9322 3.03887C17.3599 3.29414 17.6651 3.63846 17.9349 4.03716C18.1872 4.41018 18.4433 4.88561 18.7424 5.44075L19.9344 7.65321C20.007 7.78797 20.0424 7.93281 20.0442 8.07594L20.0443 8.08813V9.97187C20.0443 11.0109 20.0443 11.8601 19.9541 12.5304C19.8599 13.2311 19.6559 13.8386 19.1707 14.3236C18.9874 14.5068 18.7862 14.6502 18.5684 14.7628C18.483 14.8069 18.4403 14.829 18.3992 14.8305C18.3268 14.8332 18.2572 14.7909 18.2263 14.7254C18.2088 14.6882 18.2088 14.6284 18.2088 14.5088C18.2088 12.6092 16.668 11.0692 14.7674 11.0692C12.8667 11.0692 11.3259 12.6092 11.3259 14.5088C11.3259 14.5879 11.3286 14.6663 11.3338 14.744C11.3486 14.963 11.356 15.0725 11.3013 15.1308C11.2466 15.189 11.1454 15.1885 10.9431 15.1876L9.41093 15.1806C9.21087 15.1796 9.11084 15.1792 9.05675 15.1212C9.00266 15.0632 9.0098 14.9549 9.02407 14.7383C9.02907 14.6625 9.0316 14.5859 9.0316 14.5088C9.0316 12.6092 7.49081 11.0692 5.59014 11.0692C3.68947 11.0692 2.14868 12.6092 2.14868 14.5088C2.14868 14.6285 2.14868 14.6883 2.13119 14.7254C2.10029 14.791 2.03075 14.8333 1.95829 14.8306C1.91727 14.829 1.87455 14.8069 1.78912 14.7628C1.5713 14.6502 1.37006 14.5068 1.18679 14.3236C0.543592 13.6807 0.390481 12.8167 0.339341 11.7913C0.338801 11.7805 0.338517 11.7696 0.33849 11.7588L0.336034 10.7789C0.335493 10.563 0.335222 10.455 0.394136 10.4004C0.45305 10.3457 0.575868 10.3553 0.821504 10.3743C0.880904 10.3789 0.940938 10.3813 1.00152 10.3813L4.67242 10.3813C5.93953 10.3813 6.96673 9.35463 6.96673 8.0882C6.96673 7.86475 6.96673 7.75303 6.99234 7.6979C7.00861 7.66289 7.01281 7.65658 7.03888 7.62809C7.07992 7.58325 7.18347 7.54007 7.39058 7.45371C8.21954 7.10807 8.80217 6.29029 8.80217 5.33649C8.80217 4.07005 7.77498 3.0434 6.50786 3.0434L1.00152 3.0434C0.80706 3.0434 0.70983 3.0434 0.66079 3.00648C0.625986 2.98027 0.603388 2.94603 0.592988 2.90374C0.578335 2.84414 0.608902 2.77281 0.670036 2.63015C0.791712 2.3462 0.958813 2.08686 1.1899 1.85532C1.67538 1.36888 2.28428 1.16439 2.98672 1.06991C3.65878 0.979516 4.51023 0.979534 5.55227 0.979557L8.74617 0.979524ZM17.0988 7.4002C17.3732 7.4002 17.5103 7.4002 17.5637 7.31083C17.6171 7.22145 17.5521 7.10075 17.4221 6.85937L17.1469 6.34862C16.8216 5.74482 16.6087 5.35199 16.4144 5.06475C16.2317 4.79468 16.1069 4.68295 15.9911 4.61379C15.8752 4.54462 15.7176 4.48779 15.3931 4.45504C15.0479 4.4202 14.6009 4.41913 13.9148 4.41913H13.5323C13.3535 4.41913 13.2642 4.41913 13.2103 4.47368C13.1564 4.52824 13.1575 4.6191 13.1598 4.80082C13.1622 4.98309 13.1619 5.16541 13.1615 5.34769L13.1613 5.51987C13.1613 6.25631 13.1698 6.46138 13.2175 6.60796C13.331 6.95701 13.6048 7.23067 13.954 7.34409C14.1007 7.39172 14.3058 7.4002 15.0427 7.4002L17.0988 7.4002Z"
                fill={isActive('SHIPPED') ? 'white' : '#6C7073'}
              />
              <circle
                cx="14.7526"
                cy="14.4997"
                r="2.29167"
                fill={isActive('SHIPPED') ? 'white' : '#6C7073'}
              />
              <circle
                cx="5.58464"
                cy="14.4997"
                r="2.29167"
                fill={isActive('SHIPPED') ? 'white' : '#6C7073'}
              />
            </svg>
          </div>
          <span className="text-[15px] text-brand font-medium  mt-3">
            On the Way
          </span>
        </div>

        {/* Delivered Step */}
        <div className="flex flex-col items-center">
          <div
            className={`w-[42px] h-[42px] rounded-full flex items-center justify-center ${
              isActive('DELIVERED') ? 'bg-brand' : 'bg-[#D2DED8]'
            }`}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="17"
              height="20"
              viewBox="0 0 17 20"
              fill="none"
            >
              <path
                d="M11.4111 10.6875C12.2347 10.6875 12.9216 10.6876 13.4668 10.6143C14.0422 10.5369 14.562 10.3667 14.9795 9.94922C15.3969 9.53173 15.5672 9.01192 15.6445 8.43652C15.6812 8.16407 15.6989 7.85622 15.708 7.51367L15.7178 6.38086L15.7178 4.45215C15.7178 3.62862 15.7178 2.94166 15.6445 2.39648C15.5672 1.82108 15.397 1.30126 14.9795 0.883788C14.562 0.466323 14.0422 0.296133 13.4668 0.218749C13.1941 0.182092 12.8858 0.164425 12.543 0.155272L11.4111 0.145507L9.48242 0.145507C8.6589 0.145481 7.97195 0.145465 7.42676 0.218749C6.85136 0.29611 6.33155 0.466394 5.91406 0.883788C5.49661 1.30125 5.32641 1.8211 5.24902 2.39648C5.21237 2.66914 5.19372 2.97744 5.18457 3.32031L5.17578 4.45215L5.17578 6.38086C5.17576 7.20441 5.17573 7.89132 5.24902 8.43652C5.3264 9.012 5.49655 9.5317 5.91406 9.94922C6.33158 10.3667 6.85128 10.5369 7.42676 10.6143C7.69922 10.6509 8.00703 10.6696 8.34961 10.6787L9.48242 10.6875L11.4111 10.6875ZM9.53027 4.5C9.02401 4.5 8.61328 4.08927 8.61328 3.58301C8.61345 3.07689 9.02412 2.66699 9.53027 2.66699L11.3633 2.66699C11.8694 2.667 12.2801 3.0769 12.2803 3.58301C12.2803 4.08926 11.8695 4.49999 11.3633 4.5L9.53027 4.5Z"
                fill={isActive('DELIVERED') ? 'white' : '#6C7073'}
              />
              <path
                d="M3.07357 11.1455C2.67682 11.1455 2.31306 11.1454 2.01715 11.1852C1.69129 11.229 1.34688 11.3321 1.06361 11.6154C0.780335 11.8986 0.677258 12.2431 0.633448 12.5689C0.593664 12.8648 0.593706 13.2286 0.593753 13.6253L0.593754 16.0162C0.593717 16.317 0.593682 16.5989 0.621136 16.8345C0.651688 17.0966 0.722435 17.3722 0.912349 17.6266C1.10226 17.8811 1.34636 18.0273 1.58897 18.1312C1.80697 18.2245 2.07719 18.3047 2.36558 18.3902L6.94791 19.7499C7.61199 19.947 8.32374 19.8612 8.92415 19.5148L15.8134 15.5398C16.6688 15.0463 16.889 13.9082 16.3193 13.1193C15.6841 12.2398 14.5745 11.8522 13.5382 12.1744L13.5368 12.1748L11.5885 12.7734C11.3605 12.8434 11.2465 12.8785 11.2108 12.9442C11.1751 13.0099 11.2177 13.1598 11.303 13.4595C11.3679 13.6879 11.3756 13.9071 11.3645 14.0305C11.3645 14.9301 10.7513 15.6564 9.95908 15.8754L7.6299 16.5194C6.78503 16.753 5.88005 16.6727 5.08897 16.2899L3.60371 15.5713C3.31889 15.4335 3.19971 15.0909 3.33752 14.806C3.47533 14.5212 3.81794 14.402 4.10277 14.5398L5.58803 15.2585C6.12759 15.5195 6.74658 15.5748 7.32456 15.415L9.65373 14.771C9.97465 14.6823 10.2187 14.3891 10.2187 14.0305C10.2187 12.7352 9.1401 12.4061 7.8575 12.4061L6.9018 12.406C6.73518 12.406 6.57168 12.3685 6.42449 12.2972L4.55268 11.3916C4.21753 11.2294 3.84916 11.1455 3.47651 11.1455L3.07357 11.1455Z"
                fill={isActive('DELIVERED') ? 'white' : '#6C7073'}
              />
            </svg>
          </div>
          <span className="text-[15px] text-gray-400 font-medium mt-3">
            Delivered
          </span>
        </div>
      </div>
    </div>
  );
}
