/* eslint-disable prettier/prettier */
import {
  Body,
  Container,
  Head,
  Html,
  Preview,
  Tailwind,
} from '@react-email/components';
import Footer from './footer';

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <Html>
      <Head>
        <style
          dangerouslySetInnerHTML={{
            __html: `
          @import url('https://fonts.googleapis.com/css2?family=Red+Hat+Display:ital,wght@0,300..900;1,300..900&display=swap');
          
          body {
            font-family: 'Red Hat Display', Verdana, sans-serif;
          }
        `,
          }}
        />
      </Head>
      <Preview>See your stats from </Preview>
      <Tailwind
        config={{
          theme: {
            extend: {
              colors: {
                brand: '#00AB55',
                dark: '#1A1A1A',
                gray: '#6C7073',
                body: '#F8F8F8',
              },
            },
          },
        }}
      >
        <Body className="bg-body font-['Red_Hat_Display']">
          <Container className="mx-auto w-full max-w-[600px] p-[52px]">
            {children}
            <Footer />
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
}
