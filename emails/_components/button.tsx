/* eslint-disable prettier/prettier */
import { But<PERSON> } from '@react-email/components';

export default function ButtonLink({
  children,
  href,
}: {
  children: React.ReactNode;
  href: string;
}) {
  return (
    <Button
      href={href}
      className=" bg-brand  w-fit text-[15px] font-bold text-white rounded-full"
    >
      <div className="flex  h-[50px] justify-center items-center px-7  gap-2">
        {children}

        <svg
          width="8"
          height="14"
          viewBox="0 0 8 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M1.00005 1C1.00005 1 6.99999 5.41893 7 7.00005C7.00002 8.58116 1 13 1 13"
            stroke="white"
            strokeWidth="1.63043"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
    </Button>
  );
}
