/* eslint-disable prettier/prettier */
import { Img, Section, Text } from '@react-email/components';

export default function Header({
  title,
  orderId,
  description,
  subTitle,
}: {
  title: string;
  orderId?: string;
  description?: React.ReactNode;
  subTitle?: string;
}) {
  return (
    <>
      <Img
        alt="Braun Collection"
        height={74}
        width={84}
        src="https://i.ibb.co/NnKcmTmD/Logo.png"
      />
      <Section>
        <Text className=" flex justify-start flex-col gap-2">
          <span className={`font-bold text-3xl`}>{title}</span>
          {subTitle && (
            <span className="text-gray text-[15px]">{subTitle}</span>
          )}
          {orderId && (
            <span className="text-brand text-md font-semibold">
              Order ID: #{orderId}
            </span>
          )}
        </Text>
        {description}
      </Section>
    </>
  );
}
