---
description: 
globs: 
alwaysApply: true
---
When working on the backend,
1. Use proper typing. Do not use 'any' type 
2. Do not create new nestjs modules
3. To define types for responses put the types in [types.ts](mdc:backend/src/types.ts) file. 
4. Follow the code style and patterns present in the project. 
5. To generate DTO for any endpoint, follow the feature.schema.ts pattern where you make use of zod and createZodDto to generate the DTO. 
6. Do not write tests unless you are asked to. 
7. Do not try to run the backend unless you are asked to.