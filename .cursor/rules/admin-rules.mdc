---
description: 
globs: 
alwaysApply: true
---
Follow these rules when working on the vegmove-admin project.
1. Use proper typing, do not use 'any' type. 
2. Use the [frontend-types.ts](mdc:vegmove-admin/src/frontend-types.ts) file to create types for API responses. 
3. Use shadcn for all UI elements. 
4. Use react-hook-form for Forms. 
5. For creating new features, please use tanstack query and use the existing apiService for the handler. For old existing features, keep the usual useEffect. 
