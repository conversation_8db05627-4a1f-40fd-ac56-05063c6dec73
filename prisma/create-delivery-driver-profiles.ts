import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createDeliveryDriverProfiles() {
  try {
    console.log('Starting delivery driver profile creation...');

    // Find all users with type DELIVERY_PERSON who don't have a profile yet
    const deliveryDrivers = await prisma.user.findMany({
      where: {
        type: 'DELIVERY_PERSON',
        deliveryDriverProfile: null,
      },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    console.log(
      `Found ${deliveryDrivers.length} delivery drivers without profiles`,
    );

    if (deliveryDrivers.length === 0) {
      console.log('No delivery drivers found or all already have profiles');
      return;
    }

    // Create profiles for all delivery drivers
    const profilesData = deliveryDrivers.map((driver) => ({
      userId: driver.id,
      isActive: true, // Default to active for existing drivers
    }));

    const result = await prisma.deliveryDriverProfile.createMany({
      data: profilesData,
      skipDuplicates: true,
    });

    console.log(
      `Successfully created ${result.count} delivery driver profiles`,
    );

    // Log the created profiles
    for (const driver of deliveryDrivers) {
      console.log(`✓ Created profile for: ${driver.name} (${driver.email})`);
    }
  } catch (error) {
    console.error('Error creating delivery driver profiles:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
createDeliveryDriverProfiles()
  .then(() => {
    console.log('Migration completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  });
