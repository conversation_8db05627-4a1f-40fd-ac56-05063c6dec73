import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';
import {
  AlgoliaProductRecord,
  AlgoliaService,
} from '../src/algolia/algolia.service';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();
const algoliaService = new AlgoliaService();

async function migrateProductsToAlgolia() {
  console.log('Starting Algolia migration...');

  try {
    // Clear existing index
    console.log('Clearing existing Algolia index...');
    await algoliaService.clearIndex();

    // Get all active products with their relations
    console.log('Fetching products from database...');
    const products = await prisma.product.findMany({
      where: {
        active: true,
      },
      include: {
        category: true,
        thumbnail: true,
      },
    });

    console.log(`Found ${products.length} products to migrate`);

    // Process products in batches to avoid overwhelming Algolia
    const batchSize = 100;
    let totalRecords = 0;

    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);
      const algoliaRecords: AlgoliaProductRecord[] = [];

      console.log(
        `Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(products.length / batchSize)}`,
      );

      for (const product of batch) {
        try {
          const record =
            AlgoliaService.transformProductToAlgoliaRecord(product);
          algoliaRecords.push(record);
        } catch (error) {
          console.error(`Error transforming product ${product.id}:`, error);
        }
      }

      if (algoliaRecords.length > 0) {
        try {
          await algoliaService.indexProducts(algoliaRecords);
          totalRecords += algoliaRecords.length;
          console.log(`Indexed ${algoliaRecords.length} records in this batch`);
        } catch (error) {
          console.error('Error indexing batch:', error);
        }
      }

      // Small delay between batches to be nice to Algolia
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    console.log(`Migration completed! Total records indexed: ${totalRecords}`);
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    prisma.$disconnect();
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  migrateProductsToAlgolia()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}
