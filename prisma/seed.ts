import { PrismaClient, CategoryType } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function seed(): Promise<void> {
  // --------------------------------------------------
  // 1️⃣ Hash passwords
  const adminPasswordHash = await bcrypt.hash('verystrongpassword', 10);
  const commonPasswordHash = await bcrypt.hash('qwerty12345', 10);

  // --------------------------------------------------
  // 2️⃣ Languages
  await prisma.language.createMany({
    data: [
      { code: 'en', flagUrl: '', name: 'English' },
      { code: 'bn', flagUrl: '', name: '<PERSON><PERSON>' },
    ],
  });

  // --------------------------------------------------
  // 3️⃣ Country
  const country = await prisma.country.create({
    data: {
      code: 'IN',
      name: 'India',
      dialCode: '91',
      flagUrl: 'https://flagsapi.com/IN/flat/64.png',
    },
  });

  // --------------------------------------------------
  // 4️⃣ Reward tiers
  await prisma.rewardTier.createMany({
    data: [
      {
        name: 'None',
        requiredRollingSpend: 0,
        earnPercentage: 0,
        redeemPercentage: 0,
        maxDiscountValue: 0,
        type: 'NONE',
      },
      {
        name: 'Bronze',
        requiredRollingSpend: 2000,
        earnPercentage: 2.2,
        redeemPercentage: 2.2,
        maxDiscountValue: 100,
        type: 'BRONZE',
      },
      {
        name: 'Silver',
        requiredRollingSpend: 3000,
        earnPercentage: 3.3,
        redeemPercentage: 3.3,
        maxDiscountValue: 200,
        type: 'SILVER',
      },
      {
        name: 'Gold',
        requiredRollingSpend: 4000,
        earnPercentage: 4.4,
        redeemPercentage: 4.4,
        maxDiscountValue: 300,
        type: 'GOLD',
      },
      {
        name: 'Platinum',
        requiredRollingSpend: 5000,
        earnPercentage: 5.5,
        redeemPercentage: 5.5,
        maxDiscountValue: 400,
        type: 'PLATINUM',
      },
      {
        name: 'Diamond',
        requiredRollingSpend: 6000,
        earnPercentage: 6.6,
        redeemPercentage: 6.6,
        maxDiscountValue: 500,
        type: 'DIAMOND',
      },
    ],
  });

  // --------------------------------------------------
  // 5️⃣ Admin user
  const adminUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: adminPasswordHash,
      name: 'admin',
      type: 'ADMIN',
      phone: '1775868353',
      countryId: country.id,
      rewardTierId: 1,
      referralCode: 'ZXC45612',
    },
  });

  // --------------------------------------------------
  // 6️⃣ Admin shipping address
  const shippingAddress = await prisma.address.create({
    data: {
      userId: adminUser.id,
      type: 'SHIPPING',
      apartment: '3A',
      block: 'Tower B',
      streetName: 'Strand Road',
      city: 'Howrah',
      state: 'West Bengal',
      countryId: country.id,
      zipCode: '711101',
    },
  });

  // --------------------------------------------------
  // 7️⃣ Initial Collections (top level)
  await prisma.category.createMany({
    data: [
      { name: 'Baby care', slug: 'baby-care', type: CategoryType.COLLECTION },
      {
        name: 'Bathroom & Cleaning',
        slug: 'bathroom-cleaning',
        type: CategoryType.COLLECTION,
      },
      {
        name: 'Cold Drinks',
        slug: 'cold-drinks',
        type: CategoryType.COLLECTION,
      },
      {
        name: 'Dals & Pulses',
        slug: 'dals-pulses',
        type: CategoryType.COLLECTION,
      },
    ],
  });

  // fetch collections
  const collections = await prisma.category.findMany({
    where: {
      slug: {
        in: ['baby-care', 'bathroom-cleaning', 'cold-drinks', 'dals-pulses'],
      },
    },
  });
  const babyCareColl = collections.find((c) => c.slug === 'baby-care')!;
  const bathroomColl = collections.find((c) => c.slug === 'bathroom-cleaning')!;
  const coldDrinksColl = collections.find((c) => c.slug === 'cold-drinks')!;
  const dalsPulsesColl = collections.find((c) => c.slug === 'dals-pulses')!;

  // --------------------------------------------------
  // 8️⃣ Under Baby care: one Category + one Segment + Products
  const babyCareCat = await prisma.category.create({
    data: {
      name: 'Baby care essentials',
      slug: 'baby-care-essentials',
      parentId: babyCareColl.id,
      type: CategoryType.CATEGORY,
    },
  });
  const babyCareSeg = await prisma.category.create({
    data: {
      name: 'Baby care products',
      slug: 'baby-care-products',
      parentId: babyCareCat.id,
      type: CategoryType.SEGMENT,
    },
  });
  await prisma.product.createMany({
    data: [
      {
        name: 'Bournvita Strength Everyday *(7 – 9 years) Super Saver Pack (2 Kg)',
        barcode: '1A1A1A',
        categoryId: babyCareSeg.id,
        description:
          'Bournvita is a brand of malted and chocolate malt drink mixes manufactured by Cadbury.',
        gstPercentage: 13,
        slug: 'bournvita-strength-everyday',
        highlights: {
          nutrition: 'Enriched with vitamins D & B12, Iron, Calcium',
          targetAge: '7-9 years',
          packSize: '2 Kg pouch',
        },
        information: {
          brand: 'Cadbury',
          weight: '2 Kg',
          flavour: 'Chocolate malt',
          vegetarian: true,
          ingredients: [
            'Malt extract',
            'Sugar',
            'Cocoa solids',
            'Liquid glucose',
            'Emulsifier',
            'Vitamins & minerals',
          ],
          allergenInfo: 'Contains gluten, milk',
          countryOfOrigin: 'India',
        },
      },
      {
        name: "Johnson's Baby Buds (15 pc)",
        barcode: '1A1A1B',
        categoryId: babyCareSeg.id,
        description:
          'Soft cotton buds specially designed for babies’ delicate ears.',
        gstPercentage: 7,
        slug: 'johnson-baby-buds',
        highlights: {
          softness: '100% pure cotton tips',
          safety: 'Ear-safe stem',
          packSize: '15-piece pack',
        },
        information: {
          brand: 'Johnson & Johnson',
          quantity: '15 buds',
          material: 'Cotton & paper',
          sterilised: true,
          ageGroup: 'Newborn+',
          countryOfOrigin: 'India',
        },
      },
    ],
  });

  // --------------------------------------------------
  // 9️⃣ Under Bathroom & Cleaning: one Category + multiple Segments
  const bathroomCat = await prisma.category.create({
    data: {
      name: 'Personal Hygiene',
      slug: 'personal-hygiene',
      parentId: bathroomColl.id,
      type: CategoryType.CATEGORY,
    },
  });
  const soapSeg = await prisma.category.create({
    data: {
      name: 'Soap',
      slug: 'soap',
      parentId: bathroomCat.id,
      type: CategoryType.SEGMENT,
    },
  });
  const shampooSeg = await prisma.category.create({
    data: {
      name: 'Shampoo',
      slug: 'shampoo',
      parentId: bathroomCat.id,
      type: CategoryType.SEGMENT,
    },
  });
  const hairColourSeg = await prisma.category.create({
    data: {
      name: 'Hair colour',
      slug: 'hair-colour',
      parentId: bathroomCat.id,
      type: CategoryType.SEGMENT,
    },
  });

  // --------------------------------------------------
  // 10️⃣ Under Cold Drinks: Category + Segments + no products here yet
  const coldDrinksCat = await prisma.category.create({
    data: {
      name: 'Beverage Drinks',
      slug: 'beverage-drinks',
      parentId: coldDrinksColl.id,
      type: CategoryType.CATEGORY,
    },
  });
  const sodaSeg2 = await prisma.category.create({
    data: {
      name: 'Soda',
      slug: 'soda',
      parentId: coldDrinksCat.id,
      type: CategoryType.SEGMENT,
    },
  });
  const juiceSeg2 = await prisma.category.create({
    data: {
      name: 'Juice',
      slug: 'juice2',
      parentId: coldDrinksCat.id,
      type: CategoryType.SEGMENT,
    },
  });

  // --------------------------------------------------
  // 11️⃣ Under Dals & Pulses: Category + Segment (no initial products)
  const dalsCat = await prisma.category.create({
    data: {
      name: 'Lentils & Pulses',
      slug: 'lentils-pulses',
      parentId: dalsPulsesColl.id,
      type: CategoryType.CATEGORY,
    },
  });
  await prisma.category.create({
    data: {
      name: 'Red Lentil',
      slug: 'red-lentil',
      parentId: dalsCat.id,
      type: CategoryType.SEGMENT,
    },
  });

  // --------------------------------------------------
  // 12️⃣ Food & Beverages hierarchy (existing extra data)
  const foodColl = await prisma.category.create({
    data: {
      name: 'Food & Beverages',
      slug: 'food-beverages',
      type: CategoryType.COLLECTION,
    },
  });
  const snacksCat = await prisma.category.create({
    data: {
      name: 'Snacks',
      slug: 'snacks',
      parentId: foodColl.id,
      type: CategoryType.CATEGORY,
    },
  });
  const bevCat = await prisma.category.create({
    data: {
      name: 'Beverages',
      slug: 'beverages-main',
      parentId: foodColl.id,
      type: CategoryType.CATEGORY,
    },
  });
  const chipsSeg = await prisma.category.create({
    data: {
      name: 'Chips',
      slug: 'chips',
      parentId: snacksCat.id,
      type: CategoryType.SEGMENT,
    },
  });
  const bisSeg = await prisma.category.create({
    data: {
      name: 'Biscuits',
      slug: 'biscuits',
      parentId: snacksCat.id,
      type: CategoryType.SEGMENT,
    },
  });
  const sodaSeg3 = await prisma.category.create({
    data: {
      name: 'Soda Beverage',
      slug: 'soda-beverate',
      parentId: bevCat.id,
      type: CategoryType.SEGMENT,
    },
  });
  const juiceSeg3 = await prisma.category.create({
    data: {
      name: 'Juice',
      slug: 'juice',
      parentId: bevCat.id,
      type: CategoryType.SEGMENT,
    },
  });

  // --------------------------------------------------
  // 13️⃣ Liquid Soap under soapSeg
  const liquidSoapSeg = await prisma.category.create({
    data: {
      name: 'Liquid Soap',
      slug: 'liquid-soap',
      parentId: soapSeg.id,
      type: CategoryType.SEGMENT,
    },
  });

  // --------------------------------------------------
  // 14️⃣ Initial products under babyCareSeg (unchanged semantics)
  // (already created above)

  // --------------------------------------------------
  // 15️⃣ Additional products under Food & Beverages segments
  await prisma.product.createMany({
    data: [
      {
        name: 'Lay’s Classic Salted Potato Chips (150 g)',
        barcode: 'CHIP001',
        categoryId: chipsSeg.id,
        description: 'Thin sliced, perfectly fried & salted.',
        gstPercentage: 12,
        slug: 'lays-classic-chips',
        highlights: {
          texture: 'Thin sliced',
          flavor: 'Salted',
          packSize: '150 g',
        },
        information: {
          brand: 'Lay’s',
          weight: '150 g',
          vegetarian: true,
          ingredients: ['Potatoes', 'Oil', 'Salt'],
          countryOfOrigin: 'India',
        },
      },
      {
        name: 'Parle‑G Biscuits Family Pack (800 g)',
        barcode: 'BISC001',
        categoryId: bisSeg.id,
        description: 'Iconic glucose biscuits.',
        gstPercentage: 5,
        slug: 'parle-g-family-pack',
        highlights: { taste: 'Sweet', packSize: '800 g' },
        information: {
          brand: 'Parle',
          weight: '800 g',
          vegetarian: true,
          ingredients: ['Wheat', 'Sugar'],
          countryOfOrigin: 'India',
        },
      },
      {
        name: 'Coca‑Cola Original Taste PET (1.25 L)',
        barcode: 'SODA001',
        categoryId: sodaSeg3.id,
        description: 'Chilled fizzy cola beverage.',
        gstPercentage: 18,
        slug: 'coca-cola-1-25l',
        highlights: { taste: 'Cola', bottle: '1.25 L' },
        information: {
          brand: 'Coca‑Cola',
          volume: '1.25 L',
          vegetarian: true,
          ingredients: ['Water', 'Sugar', 'CO2'],
          countryOfOrigin: 'India',
        },
      },
      {
        name: 'Tropicana Orange Delight (1 L Tetra)',
        barcode: 'JUICE001',
        categoryId: juiceSeg3.id,
        description: '100% orange juice.',
        gstPercentage: 12,
        slug: 'tropicana-orange-1l',
        highlights: { juiceContent: '100% orange', pack: '1 L' },
        information: {
          brand: 'Tropicana',
          volume: '1 L',
          vegetarian: true,
          ingredients: ['Orange juice'],
          countryOfOrigin: 'India',
        },
      },
      {
        name: 'Dettol Liquid Handwash Original Refill (750 ml)',
        barcode: 'LSOAP001',
        categoryId: liquidSoapSeg.id,
        description: 'Antibacterial liquid soap.',
        gstPercentage: 18,
        slug: 'dettol-handwash-refill',
        highlights: { antibacterial: 'Kills 99.9%', pack: '750 ml' },
        information: {
          brand: 'Dettol',
          volume: '750 ml',
          antibacterial: true,
          ingredients: ['Water', 'SLS'],
          countryOfOrigin: 'India',
        },
      },
    ],
  });

  // --------------------------------------------------
  // 16️⃣ Warehouses, inventory batches, and initial stock (unchanged)

  const victoriaWh = await prisma.warehouse.create({
    data: {
      name: 'Kolkata - Victoria Memorial',
      lat: '23.7560',
      long: '90.3890',
      type: 'SUPER',
    },
  });
  const howrahWh = await prisma.warehouse.create({
    data: {
      name: 'Kolkata - Howrah',
      lat: '22.3569',
      long: '88.3237',
      type: 'GENERAL',
    },
  });
  const newTownWh = await prisma.warehouse.create({
    data: {
      name: 'Kolkata - New Town',
      lat: '22.5946',
      long: '88.4790',
      type: 'GENERAL',
    },
  });
  const warehouse = await prisma.warehouse.create({
    data: {
      name: 'Tongi',
      lat: '23.875183648102684',
      long: '90.3822637814758',
      type: 'GENERAL',
    },
  });
  const batch = await prisma.inventoryBatch.create({
    data: { name: 'STOCK-001-001', warehouseId: warehouse.id },
  });
  const allProducts = await prisma.product.findMany();
  await prisma.inventory.create({
    data: {
      productId: allProducts[0].id,
      batchId: batch.id,
      buyingPrice: 830,
      sellingPrice: 875,
      inventoryTransactions: {
        create: { quantity: 100, type: 'PURCHASE', remark: 'Initial stock' },
      },
    },
  });
  await prisma.inventory.create({
    data: {
      productId: allProducts[1].id,
      batchId: batch.id,
      buyingPrice: 100,
      sellingPrice: 105,
      inventoryTransactions: {
        create: { quantity: 50, type: 'PURCHASE', remark: 'Initial stock' },
      },
    },
  });

  // --------------------------------------------------
  // 17️⃣ Order with items (unchanged)
  await prisma.order.create({
    data: {
      userId: adminUser.id,
      deliveryDate: new Date(),
      deliveryTime: '08:00 AM - 12:00 PM',
      paymentStatus: 'PENDING',
      status: 'PENDING',
      totalAmount: 4943.75 + 337.05,
      addressId: shippingAddress.id,
      note: 'random note',
      orderStatusHistory: { create: { status: 'PENDING' } },
      items: {
        createMany: {
          data: [
            {
              inventoryId: batch.id,
              price: 875,
              productId: allProducts[0].id,
              quantity: 5,
              gstAmount: 113.75,
            },
            {
              inventoryId: batch.id,
              price: 105,
              productId: allProducts[1].id,
              quantity: 3,
              gstAmount: 7.35,
            },
          ],
        },
      },
    },
  });

  // --------------------------------------------------
  // 18️⃣ Additional users
  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: commonPasswordHash,
      name: 'Alice',
      type: 'CUSTOMER',
      phone: '1700000001',
      countryId: country.id,
      rewardTierId: 1,
      referralCode: 'ALC1001',
      addresses: {
        create: {
          type: 'SHIPPING',
          streetName: '8/B Road 12',
          city: 'Howrah',
          state: 'West Bengal',
          zipCode: '1209',
          countryId: country.id,
        },
      },
    },
  });
  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: commonPasswordHash,
      name: 'Bob',
      type: 'CUSTOMER',
      phone: '1700000002',
      countryId: country.id,
      rewardTierId: 1,
      referralCode: 'BOB1002',
      addresses: {
        create: {
          type: 'SHIPPING',
          streetName: 'Park Street',
          city: 'Kolkata',
          state: 'West Bengal',
          zipCode: '700016',
          countryId: country.id,
        },
      },
    },
  });
  const staff1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: commonPasswordHash,
      name: 'Sohel',
      type: 'WAREHOUSE_STAFF',
      phone: '1700001000',
      countryId: country.id,
    },
  });
  const staff2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: commonPasswordHash,
      name: 'Raju',
      type: 'WAREHOUSE_STAFF',
      phone: '919830000100',
      countryId: country.id,
    },
  });
  await prisma.warehouseStaff.createMany({
    data: [
      { userId: staff1.id, warehouseId: warehouse.id },
      { userId: staff2.id, warehouseId: warehouse.id },
    ],
  });
  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: commonPasswordHash,
      name: 'Driver One',
      type: 'DELIVERY_PERSON',
      phone: '8801700002000',
      countryId: country.id,
    },
  });

  // --------------------------------------------------
  // 19️⃣ Home sections (unchanged)
  await prisma.homeSection.create({
    data: {
      title: 'Grocery & Food Grains',
      onlyDiscount: false,
      type: 'CATEGORY',
      displayOrder: 0,
      homeSectionCategory: {
        createMany: {
          data: collections.map((c, idx) => ({
            categoryId: c.id,
            displayOrder: idx,
          })),
        },
      },
    },
  });
  await prisma.homeSection.create({
    data: {
      title: 'Snacks & Beverages',
      onlyDiscount: false,
      type: 'CATEGORY',
      displayOrder: 1,
      homeSectionCategory: {
        createMany: {
          data: [
            { categoryId: chipsSeg.id, displayOrder: 0 },
            { categoryId: bisSeg.id, displayOrder: 1 },
            { categoryId: sodaSeg3.id, displayOrder: 2 },
            { categoryId: juiceSeg3.id, displayOrder: 3 },
          ],
        },
      },
    },
  });
  await prisma.homeSection.create({
    data: {
      title: 'Breakfast and Sausage',
      onlyDiscount: false,
      type: 'PRODUCT',
      displayOrder: 2,
      homeSectionCategory: {
        createMany: { data: [{ categoryId: chipsSeg.id, displayOrder: 0 }] },
      },
    },
  });
  await prisma.homeSection.create({
    data: {
      title: 'Makeup',
      onlyDiscount: false,
      type: 'PRODUCT',
      displayOrder: 3,
      homeSectionCategory: {
        createMany: {
          data: [{ categoryId: liquidSoapSeg.id, displayOrder: 0 }],
        },
      },
    },
  });
}

seed()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());
