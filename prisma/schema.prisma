// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserType {
  CUSTOMER
  ADMIN
  WAREHOUSE_STAFF
  DELIVERY_PERSON
}

enum MediaType {
  IMAGE
  VIDEO
}

enum AddressType {
  BILLING
  SHIPPING
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PACKING
  READY
  SHIPPED
  DELIVERED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum InventoryTransactionType {
  PURCHASE
  SALE
  ADJUSTMENT
  RETURN
}

enum WeightUnit {
  GRAM
  MILLILITER
  PIECE
  KILOGRAM
  LITER
}

enum AmountType {
  FLAT
  PERCENTAGE
}

enum WarehouseType {
  GENERAL
  SUPER
}

model User {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id                     Int                      @id @default(autoincrement())
  name                   String
  email                  String                   @unique
  phone                  String?                  @unique
  phoneCountry           Country?                 @relation(fields: [countryId], references: [id])
  password               String
  type                   UserType                 @default(CUSTOMER)
  profilePicture         String?
  addresses              Address[]
  orders                 Order[]
  cart                   Cart[]
  countryId              Int?
  InventoryTransaction   InventoryTransaction[]
  werehouseStaffs        WarehouseStaff[]
  deliverOrders          Order[]                  @relation("DeliveryDriver")
  rewardTierId           Int?
  tierExpiresAt          DateTime?
  rewardTier             RewardTier?              @relation(fields: [rewardTierId], references: [id])
  rewardPointTransaction RewardPointTransaction[]
  firebaseToken          String?
  locationHistory        DeliveryDriverLocation[]

  // Referral system fields
  referralCode                    String?                           @unique
  referredBy                      User?                             @relation("UserReferrals", fields: [referredById], references: [id])
  referredById                    Int?
  referrals                       User[]                            @relation("UserReferrals")
  ProductAvailabilitySubscription ProductAvailabilitySubscription[]

  // Role-based permissions
  userRoles UserRole[]

  // Delivery driver profile
  deliveryDriverProfile DeliveryDriverProfile?
}

model DeliveryDriverProfile {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId   Int     @unique
  isActive Boolean @default(true)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([isActive])
}

model Address {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id         Int         @id @default(autoincrement())
  user       User        @relation(fields: [userId], references: [id])
  userId     Int
  type       AddressType
  apartment  String?
  block      String?
  streetName String
  city       String
  state      String
  country    Country     @relation(fields: [countryId], references: [id])
  countryId  Int
  zipCode    String
  lat        String      @default("22.59666215410892")
  long       String      @default("88.26584374835787")
  isDeleted  Boolean     @default(false)
  orders     Order[]
}

model Country {
  id       Int        @id @default(autoincrement())
  name     String
  code     String
  dialCode String
  flagUrl  String
  Address  Address[]
  User     User[]
  Supplier Supplier[]
}

model Product {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id              Int         @id @default(autoincrement())
  barcode         String      @unique
  name            String
  description     String
  highlights      Json?
  information     Json?
  categoryId      Int
  weight          Float       @default(0)
  weightUnit      WeightUnit  @default(GRAM)
  length          Float       @default(0)
  width           Float       @default(0)
  height          Float       @default(0)
  gstPercentage   Float       @default(0)
  supplierId      Int?
  slug            String?     @unique
  discountValue   Float? // optional, can be percentage or flat
  discountType    AmountType?
  hasVariations   Boolean     @default(false)
  thumbnailId     Int?
  active          Boolean     @default(true)
  maxCartQuantity Int? // maximum quantity allowed in cart per user

  inventory          Inventory[]
  media              Media[]              @relation("ProductGallery")
  thumbnail          Media?               @relation("ProductThumbnail", fields: [thumbnailId], references: [id])
  orderItems         OrderItem[]
  category           Category             @relation("ProductCategory", fields: [categoryId], references: [id])
  cart               Cart[]
  supplier           Supplier?            @relation(fields: [supplierId], references: [id])
  productTranslation ProductTranslation[]
  variations         ProductVariation[]
  attributes         ProductAttribute[]

  // Self-relation for "sold together" products
  relatedProducts                 RelatedProduct[]                  @relation("ProductToRelated")
  relatedToProducts               RelatedProduct[]                  @relation("RelatedToProduct")
  tierDiscounts                   TierProductDiscount[]
  ProductAvailabilitySubscription ProductAvailabilitySubscription[]
  productPolicies                 ProductPolicy[]
}

model ProductPolicyType {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id              Int             @id @default(autoincrement())
  name            String
  icon            String?
  productPolicies ProductPolicy[]
}

model ProductPolicy {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id                  Int               @id @default(autoincrement())
  productId           Int
  product             Product           @relation(fields: [productId], references: [id])
  description         String?
  details             Json              @default("{}")
  productPolicyTypeId Int
  productPolicyType   ProductPolicyType @relation(fields: [productPolicyTypeId], references: [id])
}

enum CategoryType {
  COLLECTION
  CATEGORY
  SEGMENT
}

model Category {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id        Int          @id @default(autoincrement())
  name      String
  parentId  Int?
  type      CategoryType @default(SEGMENT)
  slug      String       @unique
  isActive  Boolean      @default(true)
  bannerUrl String?
  iconUrl   String?

  parent              Category?             @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children            Category[]            @relation("CategoryHierarchy")
  products            Product[]             @relation("ProductCategory")
  categoryTranslation CategoryTranslation[]
  homeSectionCategory HomeSectionCategory[]
  Banner              Banner[]

  @@index([parentId])
}

model Supplier {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id        Int     @id @default(autoincrement())
  name      String
  email     String  @unique
  phone     String  @unique
  address   String
  countryId Int
  country   Country @relation(fields: [countryId], references: [id])

  products    Product[]
  inventories Inventory[]

  @@index([countryId])
}

model InventoryBatch {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id          Int         @id @default(autoincrement())
  name        String      @unique
  inventories Inventory[]
  warehouse   Warehouse?  @relation(fields: [warehouseId], references: [id])
  warehouseId Int?
}

model Inventory {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id              Int            @id @default(autoincrement())
  productId       Int
  buyingPrice     Decimal
  sellingPrice    Decimal
  manufactureDate DateTime?
  expiryDate      DateTime?
  supplierId      Int?
  supplier        Supplier?      @relation(fields: [supplierId], references: [id])
  batch           InventoryBatch @relation(fields: [batchId], references: [id])
  batchId         Int

  product               Product                @relation(fields: [productId], references: [id])
  inventoryTransactions InventoryTransaction[]
  orderItems            OrderItem[]
  variation             ProductVariation?      @relation(fields: [variationId], references: [id])
  variationId           Int?

  @@index([productId])
  @@index([variationId])
  @@index([batchId])
}

model InventoryTransaction {
  createdAt DateTime @default(now())

  id          Int                      @id @default(autoincrement())
  inventoryId Int
  quantity    Int
  type        InventoryTransactionType
  orderId     Int?
  remark      String?
  byUserId    Int?

  inventory Inventory @relation(fields: [inventoryId], references: [id])
  order     Order?    @relation(fields: [orderId], references: [id])
  byUser    User?     @relation(fields: [byUserId], references: [id])

  @@index([inventoryId])
  @@index([orderId])
}

model Media {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id        Int       @id @default(autoincrement())
  url       String
  mediaType MediaType @default(IMAGE)
  productId Int?

  product          Product?          @relation("ProductGallery", fields: [productId], references: [id])
  productThumbnail Product[]         @relation("ProductThumbnail")
  banner           Banner?
  variation        ProductVariation? @relation(fields: [variationId], references: [id])
  variationId      Int?

  @@index([productId])
  @@index([variationId])
}

model Cart {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id        Int @id @default(autoincrement())
  userId    Int
  productId Int
  quantity  Int

  product     Product           @relation(fields: [productId], references: [id])
  user        User              @relation(fields: [userId], references: [id])
  variation   ProductVariation? @relation(fields: [variationId], references: [id])
  variationId Int?

  @@unique([userId, productId, variationId])
  @@index([userId])
  @@index([variationId])
}

model Order {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id                         Int         @id @default(autoincrement())
  userId                     Int
  status                     OrderStatus
  addressId                  Int
  subtotal                   Decimal     @default(0)
  handlingCharge             Decimal     @default(0)
  deliveryFee                Decimal     @default(0)
  discountAmount             Decimal     @default(0)
  couponDiscount             Decimal     @default(0)
  rewardDiscount             Decimal     @default(0)
  totalAmount                Decimal
  deliveryDate               DateTime
  deliveryTime               String?
  deliveryStartTime          String      @default("10:00")
  deliveryEndTime            String      @default("12:00")
  note                       String?
  cashToPay                  Decimal     @default(0)
  couponId                   Int?
  deliveryDriverId           Int?
  useRewardPoints            Boolean     @default(false)
  rewardPointsUsed           Int         @default(0)
  warehouseId                Int?
  deliveryDriverAssignedDate DateTime?

  warehouse              Warehouse?               @relation(fields: [warehouseId], references: [id])
  user                   User                     @relation(fields: [userId], references: [id])
  address                Address                  @relation(fields: [addressId], references: [id])
  paymentStatus          PaymentStatus
  inventoryTransactions  InventoryTransaction[]
  items                  OrderItem[]
  orderStatusHistory     OrderStatusHistory[]
  coupon                 Coupon?                  @relation(fields: [couponId], references: [id], onDelete: Restrict)
  deliveryDriver         User?                    @relation("DeliveryDriver", fields: [deliveryDriverId], references: [id])
  rewardPointTransaction RewardPointTransaction[]

  @@index([userId])
  @@index([status])
}

model OrderItem {
  id            Int     @id @default(autoincrement())
  orderId       Int
  productId     Int
  quantity      Int
  price         Decimal
  originalPrice Decimal @default(0)
  gstAmount     Decimal @default(0)
  inventoryId   Int

  inventory   Inventory         @relation(fields: [inventoryId], references: [id])
  order       Order             @relation(fields: [orderId], references: [id])
  product     Product           @relation(fields: [productId], references: [id])
  variation   ProductVariation? @relation(fields: [variationId], references: [id])
  variationId Int?

  @@index([orderId])
  @@index([productId])
  @@index([inventoryId])
  @@index([variationId])
}

model OrderStatusHistory {
  createdAt DateTime @default(now())

  id      Int         @id @default(autoincrement())
  orderId Int
  status  OrderStatus

  order Order @relation(fields: [orderId], references: [id])

  @@index([orderId])
}

model Coupon {
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  id Int @id @default(autoincrement())

  code          String     @unique
  discountValue Decimal
  discountType  AmountType
  minOrderValue Decimal?
  maxDiscount   Decimal?
  usageLimit    Int?
  perUserLimit  Int?
  expiresAt     DateTime?
  isActive      Boolean    @default(true)
  orders        Order[]
}

model Zone {
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  id          Int          @id @default(autoincrement())
  name        String
  active      Boolean      @default(true)
  coordinates Coordinate[]
}

model Coordinate {
  id     Int    @id @default(autoincrement())
  zone   Zone   @relation(fields: [zoneId], references: [id])
  zoneId Int
  lat    String
  long   String
}

model Warehouse {
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  id               Int              @id @default(autoincrement())
  name             String
  lat              String
  long             String
  type             WarehouseType
  inventoryBatches InventoryBatch[]
  active           Boolean          @default(true)
  warehouseStaffs  WarehouseStaff[]
  homeSection      HomeSection[]
  Order            Order[]
}

model Banner {
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  id         Int       @id @default(autoincrement())
  active     Boolean   @default(true)
  mediaId    Int       @unique
  languageId Int?
  language   Language? @relation(fields: [languageId], references: [id])
  category   Category? @relation(fields: [categoryId], references: [id])
  categoryId Int?

  media Media @relation(fields: [mediaId], references: [id])
}

model WarehouseStaff {
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  id          Int @id @default(autoincrement())
  userId      Int
  warehouseId Int

  warehouse Warehouse @relation(fields: [warehouseId], references: [id])
  user      User      @relation(fields: [userId], references: [id])

  @@unique([userId, warehouseId])
}

model Language {
  id                     Int                      @id @default(autoincrement())
  name                   String
  code                   String                   @unique
  flagUrl                String
  productTranslation     ProductTranslation[]
  categoryTranslation    CategoryTranslation[]
  Banner                 Banner[]
  HomeSectionTranslation HomeSectionTranslation[]
}

model ProductTranslation {
  id        Int     @id @default(autoincrement())
  product   Product @relation(fields: [productId], references: [id])
  productId Int

  language    Language @relation(fields: [languageId], references: [id])
  languageId  Int
  name        String
  description String

  @@unique([productId, languageId])
}

model CategoryTranslation {
  id         Int      @id @default(autoincrement())
  category   Category @relation(fields: [categoryId], references: [id])
  categoryId Int

  language   Language @relation(fields: [languageId], references: [id])
  languageId Int
  name       String

  @@unique([categoryId, languageId])
}

enum TransactionType {
  CREDIT
  DEBIT
}

enum RewardTierType {
  NONE
  BRONZE
  SILVER
  GOLD
  PLATINUM
  DIAMOND
}

model RewardTier {
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  id                     Int                   @id @default(autoincrement())
  name                   String                @unique
  requiredRollingSpend   Decimal
  earnPercentage         Float // how much reward points to earn? based on order value
  redeemPercentage       Float // how much of the reward points can you use? based on the order value
  maxDiscountValue       Decimal // after the redeemable value, put a cap on it
  minOrderAmountForBonus Decimal? // minimum amout of order, to earn points
  type                   RewardTierType
  users                  User[]
  productDiscounts       TierProductDiscount[]
}

model RewardPointTransaction {
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  id      String          @id @default(cuid())
  amount  Decimal
  type    TransactionType
  user    User            @relation(fields: [userId], references: [id])
  userId  Int
  order   Order?          @relation(fields: [orderId], references: [id])
  orderId Int?
  note    String?
  expiry  DateTime?       @default(dbgenerated("(NOW() + '60 days'::interval)"))
}

model TierProductDiscount {
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  id            Int        @id @default(autoincrement())
  tier          RewardTier @relation(fields: [tierId], references: [id], onDelete: Cascade)
  tierId        Int
  product       Product    @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId     Int
  discountValue Float
  discountType  AmountType
  active        Boolean    @default(true)

  @@unique([tierId, productId])
}

model RewardPointConfig {
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  id                     Int     @id @default(autoincrement())
  minOrderAmountForBonus Decimal
  fixedBonusPoints       Int
  referralBonusPoints    Int     @default(0) // Points awarded for referrals
  active                 Boolean @default(true)
}

model RelatedProduct {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  productId        Int
  relatedProductId Int

  product        Product @relation("ProductToRelated", fields: [productId], references: [id], onDelete: Cascade)
  relatedProduct Product @relation("RelatedToProduct", fields: [relatedProductId], references: [id], onDelete: Cascade)

  @@unique([productId, relatedProductId])
}

enum Weekday {
  SUNDAY
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
}

enum DeliverySlotType {
  REGULAR
  HOLIDAY
}

enum AccessLevel {
  VIEW
  MANAGE // includes CREATE and UPDATE
  DELETE
}

model DeliverySlot {
  id        Int              @id @default(autoincrement())
  startTime String? // "HH:mm", nullable for holidays
  endTime   String? // nullable for holidays
  date      DateTime? // exact date match
  weekday   Weekday? // recurring day match
  type      DeliverySlotType @default(REGULAR)
  reason    String? // e.g., "Eid", only used if type is HOLIDAY
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  @@index([date])
  @@index([weekday])
}

enum HomeSectionType {
  CATEGORY
  PRODUCT
}

model HomeSection {
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  id                  String                   @id @default(cuid())
  title               String
  onlyDiscount        Boolean                  @default(true)
  type                HomeSectionType
  warehouse           Warehouse?               @relation(fields: [warehouseId], references: [id])
  warehouseId         Int?
  displayOrder        Int
  homeSectionCategory HomeSectionCategory[]
  translations        HomeSectionTranslation[]
}

model HomeSectionCategory {
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  id            String      @id @default(cuid())
  homeSection   HomeSection @relation(fields: [homeSectionId], references: [id])
  category      Category    @relation(fields: [categoryId], references: [id])
  homeSectionId String
  categoryId    Int
  displayOrder  Int
}

model HomeSectionTranslation {
  id            Int         @id @default(autoincrement())
  homeSection   HomeSection @relation(fields: [homeSectionId], references: [id], onDelete: Cascade)
  homeSectionId String

  language   Language @relation(fields: [languageId], references: [id])
  languageId Int

  title String

  @@unique([homeSectionId, languageId])
}

model ProductAvailabilitySubscription {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])
  userId    Int
  product   Product  @relation(fields: [productId], references: [id])
  productId Int
  notified  Boolean  @default(false) // mark if already notified

  @@unique([userId, productId]) // prevent duplicate subscriptions
  @@index([productId])
}

// Product variation models
model ProductAttribute {
  id        Int       @id @default(autoincrement())
  name      String
  productId Int
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  product Product                  @relation(fields: [productId], references: [id], onDelete: Cascade)
  options ProductAttributeOption[]

  @@unique([productId, name])
  @@index([deletedAt])
}

model ProductAttributeOption {
  id          Int       @id @default(autoincrement())
  attributeId Int
  name        String
  colorCode   String?
  imageUrl    String?
  iconUrl     String? // Added for icon upload
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?

  attribute  ProductAttribute                @relation(fields: [attributeId], references: [id], onDelete: Cascade)
  variations ProductVariationOptionMapping[]

  @@index([attributeId])
  @@index([deletedAt])
}

model ProductVariation {
  id        Int       @id @default(autoincrement())
  productId Int
  barcode   String    @unique
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  product    Product                         @relation(fields: [productId], references: [id], onDelete: Cascade)
  options    ProductVariationOptionMapping[]
  media      Media[]
  inventory  Inventory[]
  cartItems  Cart[]
  orderItems OrderItem[]

  @@index([productId])
  @@index([deletedAt])
}

model ProductVariationOptionMapping {
  variationId Int
  optionId    Int

  variation ProductVariation       @relation(fields: [variationId], references: [id], onDelete: Cascade)
  option    ProductAttributeOption @relation(fields: [optionId], references: [id], onDelete: Cascade)

  @@id([variationId, optionId])
  @@index([variationId])
  @@index([optionId])
}

model DeliveryDriverLocation {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  lat  String
  long String

  driverId Int
  driver   User @relation(fields: [driverId], references: [id])

  @@index([driverId])
}

// Permission Features (e.g., "Order", "Product", "User Management")
model PermissionFeature {
  id          Int      @id @default(autoincrement())
  name        String   @unique // e.g., "Order", "Product", "Inventory"
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  rolePermissions RolePermission[]
}

// Custom Roles
model Role {
  id           Int      @id @default(autoincrement())
  name         String   @unique
  description  String?
  isSystemRole Boolean  @default(false) // true for roles that map to UserType
  active       Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  permissions RolePermission[]
  userRoles   UserRole[]
}

// Role permissions: Role + Feature + AccessLevel
model RolePermission {
  roleId      Int
  featureId   Int
  accessLevel AccessLevel

  role    Role              @relation(fields: [roleId], references: [id], onDelete: Cascade)
  feature PermissionFeature @relation(fields: [featureId], references: [id], onDelete: Cascade)

  @@id([roleId, featureId, accessLevel])
}

// Many-to-many: User <-> Role
model UserRole {
  userId     Int
  roleId     Int
  assignedAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@id([userId, roleId])
}
