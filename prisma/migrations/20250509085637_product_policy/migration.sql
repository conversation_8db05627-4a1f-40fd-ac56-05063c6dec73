-- CreateTable
CREATE TABLE "ProductPolicyType" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "icon" TEXT NOT NULL,

    CONSTRAINT "ProductPolicyType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductPolicy" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "id" SERIAL NOT NULL,
    "productId" INTEGER NOT NULL,
    "description" TEXT NOT NULL,
    "productPolicyTypeId" INTEGER NOT NULL,

    CONSTRAINT "ProductPolicy_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "ProductPolicy" ADD CONSTRAINT "ProductPolicy_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductPolicy" ADD CONSTRAINT "ProductPolicy_productPolicyTypeId_fkey" FOREIGN KEY ("productPolicyTypeId") REFERENCES "ProductPolicyType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
