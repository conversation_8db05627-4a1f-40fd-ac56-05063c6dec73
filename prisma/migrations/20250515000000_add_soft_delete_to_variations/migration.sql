-- Add deletedAt fields for soft deletion
ALTER TABLE "ProductAttribute" ADD COLUMN "deletedAt" TIMESTAMP(3);
ALTER TABLE "ProductAttributeOption" ADD COLUMN "deletedAt" TIMESTAMP(3);
ALTER TABLE "ProductVariation" ADD COLUMN "deletedAt" TIMESTAMP(3);

-- Add iconUrl field to ProductAttributeOption
ALTER TABLE "ProductAttributeOption" ADD COLUMN "iconUrl" TEXT;

-- Create indexes for faster queries with deletedAt filter
CREATE INDEX "ProductAttribute_deletedAt_idx" ON "ProductAttribute"("deletedAt");
CREATE INDEX "ProductAttributeOption_deletedAt_idx" ON "ProductAttributeOption"("deletedAt");
CREATE INDEX "ProductVariation_deletedAt_idx" ON "ProductVariation"("deletedAt");
