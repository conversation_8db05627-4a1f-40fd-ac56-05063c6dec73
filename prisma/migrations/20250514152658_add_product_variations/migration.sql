/*
  Warnings:

  - A unique constraint covering the columns `[userId,productId,variationId]` on the table `Cart` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "Cart_userId_productId_key";

-- AlterTable
ALTER TABLE "Cart" ADD COLUMN     "variationId" INTEGER;

-- AlterTable
ALTER TABLE "Inventory" ADD COLUMN     "variationId" INTEGER;

-- AlterTable
ALTER TABLE "Media" ADD COLUMN     "variationId" INTEGER;

-- AlterTable
ALTER TABLE "OrderItem" ADD COLUMN     "variationId" INTEGER;

-- AlterTable
ALTER TABLE "Product" ADD COLUMN     "hasVariations" BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE "ProductAttribute" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "productId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductAttribute_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductAttributeOption" (
    "id" SERIAL NOT NULL,
    "attributeId" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "colorCode" TEXT,
    "imageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductAttributeOption_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductVariation" (
    "id" SERIAL NOT NULL,
    "productId" INTEGER NOT NULL,
    "barcode" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProductVariation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProductVariationOptionMapping" (
    "variationId" INTEGER NOT NULL,
    "optionId" INTEGER NOT NULL,

    CONSTRAINT "ProductVariationOptionMapping_pkey" PRIMARY KEY ("variationId","optionId")
);

-- CreateIndex
CREATE UNIQUE INDEX "ProductAttribute_productId_name_key" ON "ProductAttribute"("productId", "name");

-- CreateIndex
CREATE INDEX "ProductAttributeOption_attributeId_idx" ON "ProductAttributeOption"("attributeId");

-- CreateIndex
CREATE UNIQUE INDEX "ProductVariation_barcode_key" ON "ProductVariation"("barcode");

-- CreateIndex
CREATE INDEX "ProductVariation_productId_idx" ON "ProductVariation"("productId");

-- CreateIndex
CREATE INDEX "ProductVariationOptionMapping_variationId_idx" ON "ProductVariationOptionMapping"("variationId");

-- CreateIndex
CREATE INDEX "ProductVariationOptionMapping_optionId_idx" ON "ProductVariationOptionMapping"("optionId");

-- CreateIndex
CREATE INDEX "Cart_variationId_idx" ON "Cart"("variationId");

-- CreateIndex
CREATE UNIQUE INDEX "Cart_userId_productId_variationId_key" ON "Cart"("userId", "productId", "variationId");

-- CreateIndex
CREATE INDEX "Inventory_variationId_idx" ON "Inventory"("variationId");

-- CreateIndex
CREATE INDEX "Media_productId_idx" ON "Media"("productId");

-- CreateIndex
CREATE INDEX "Media_variationId_idx" ON "Media"("variationId");

-- CreateIndex
CREATE INDEX "OrderItem_variationId_idx" ON "OrderItem"("variationId");

-- AddForeignKey
ALTER TABLE "Inventory" ADD CONSTRAINT "Inventory_variationId_fkey" FOREIGN KEY ("variationId") REFERENCES "ProductVariation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Media" ADD CONSTRAINT "Media_variationId_fkey" FOREIGN KEY ("variationId") REFERENCES "ProductVariation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Cart" ADD CONSTRAINT "Cart_variationId_fkey" FOREIGN KEY ("variationId") REFERENCES "ProductVariation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "OrderItem" ADD CONSTRAINT "OrderItem_variationId_fkey" FOREIGN KEY ("variationId") REFERENCES "ProductVariation"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductAttribute" ADD CONSTRAINT "ProductAttribute_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductAttributeOption" ADD CONSTRAINT "ProductAttributeOption_attributeId_fkey" FOREIGN KEY ("attributeId") REFERENCES "ProductAttribute"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductVariation" ADD CONSTRAINT "ProductVariation_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductVariationOptionMapping" ADD CONSTRAINT "ProductVariationOptionMapping_variationId_fkey" FOREIGN KEY ("variationId") REFERENCES "ProductVariation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductVariationOptionMapping" ADD CONSTRAINT "ProductVariationOptionMapping_optionId_fkey" FOREIGN KEY ("optionId") REFERENCES "ProductAttributeOption"("id") ON DELETE CASCADE ON UPDATE CASCADE;
