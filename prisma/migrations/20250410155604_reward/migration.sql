-- CreateEnum
CREATE TYPE "TransactionType" AS ENUM ('CREDIT', 'DEBIT');

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "rewardTierId" INTEGER;

-- CreateTable
CREATE TABLE "RewardTier" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "earnPercentage" DOUBLE PRECISION NOT NULL,
    "redeemPercentage" DOUBLE PRECISION NOT NULL,
    "maxDiscountValue" DECIMAL(65,30) NOT NULL,
    "minOrderAmountForBonus" DECIMAL(65,30),
    "fixedBonusPoints" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RewardTier_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RewardPointTransaction" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "amount" DECIMAL(65,30) NOT NULL,
    "type" "TransactionType" NOT NULL,
    "userId" INTEGER NOT NULL,
    "orderId" INTEGER,
    "note" TEXT,

    CONSTRAINT "RewardPointTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RewardPointConfig" (
    "id" SERIAL NOT NULL,
    "minOrderAmountForBonus" DECIMAL(65,30) NOT NULL,
    "fixedBonusPoints" INTEGER NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "RewardPointConfig_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "RewardTier_name_key" ON "RewardTier"("name");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_rewardTierId_fkey" FOREIGN KEY ("rewardTierId") REFERENCES "RewardTier"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RewardPointTransaction" ADD CONSTRAINT "RewardPointTransaction_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RewardPointTransaction" ADD CONSTRAINT "RewardPointTransaction_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "Order"("id") ON DELETE SET NULL ON UPDATE CASCADE;
