-- CreateEnum
CREATE TYPE "HomeSectionType" AS ENUM ('CATEGORY', 'PRODUCT');

-- AlterTable
ALTER TABLE "Banner" ADD COLUMN     "categoryId" INTEGER;

-- AlterTable
ALTER TABLE "Order" ADD COLUMN     "warehouseId" INTEGER;

-- CreateTable
CREATE TABLE "HomeSection" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "onlyDiscount" BOOLEAN NOT NULL DEFAULT true,
    "type" "HomeSectionType" NOT NULL,
    "warehouseId" INTEGER,

    CONSTRAINT "HomeSection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HomeSectionCategory" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "id" TEXT NOT NULL,
    "homeSectionId" TEXT NOT NULL,
    "categoryId" INTEGER NOT NULL,
    "displayOrder" INTEGER NOT NULL,

    CONSTRAINT "HomeSectionCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HomeSectionTranslation" (
    "id" SERIAL NOT NULL,
    "homeSectionId" TEXT NOT NULL,
    "languageId" INTEGER NOT NULL,
    "title" TEXT NOT NULL,

    CONSTRAINT "HomeSectionTranslation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "HomeSectionTranslation_homeSectionId_languageId_key" ON "HomeSectionTranslation"("homeSectionId", "languageId");

-- AddForeignKey
ALTER TABLE "Order" ADD CONSTRAINT "Order_warehouseId_fkey" FOREIGN KEY ("warehouseId") REFERENCES "Warehouse"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Banner" ADD CONSTRAINT "Banner_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "Category"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeSection" ADD CONSTRAINT "HomeSection_warehouseId_fkey" FOREIGN KEY ("warehouseId") REFERENCES "Warehouse"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeSectionCategory" ADD CONSTRAINT "HomeSectionCategory_homeSectionId_fkey" FOREIGN KEY ("homeSectionId") REFERENCES "HomeSection"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeSectionCategory" ADD CONSTRAINT "HomeSectionCategory_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "Category"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeSectionTranslation" ADD CONSTRAINT "HomeSectionTranslation_homeSectionId_fkey" FOREIGN KEY ("homeSectionId") REFERENCES "HomeSection"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeSectionTranslation" ADD CONSTRAINT "HomeSectionTranslation_languageId_fkey" FOREIGN KEY ("languageId") REFERENCES "Language"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
