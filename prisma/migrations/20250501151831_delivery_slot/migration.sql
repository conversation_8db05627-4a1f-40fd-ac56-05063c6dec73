-- CreateEnum
CREATE TYPE "Weekday" AS ENUM ('SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY');

-- CreateEnum
CREATE TYPE "DeliverySlotType" AS ENUM ('REGULAR', 'HOLIDAY');

-- CreateTable
CREATE TABLE "DeliverySlot" (
    "id" SERIAL NOT NULL,
    "startTime" TEXT,
    "endTime" TEXT,
    "date" TIMESTAMP(3),
    "weekday" "Weekday",
    "type" "DeliverySlotType" NOT NULL DEFAULT 'REGULAR',
    "reason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DeliverySlot_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "DeliverySlot_date_idx" ON "DeliverySlot"("date");

-- CreateIndex
CREATE INDEX "DeliverySlot_weekday_idx" ON "DeliverySlot"("weekday");
