-- CreateTable
CREATE TABLE "DeliveryDriverLocation" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lat" TEXT NOT NULL,
    "long" TEXT NOT NULL,
    "driverId" INTEGER NOT NULL,

    CONSTRAINT "DeliveryDriverLocation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "DeliveryDriverLocation_driverId_idx" ON "DeliveryDriverLocation"("driverId");

-- AddForeignKey
ALTER TABLE "DeliveryDriverLocation" ADD CONSTRAINT "DeliveryDriverLocation_driverId_fkey" FOREIGN KEY ("driverId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
