/*
  Warnings:

  - You are about to drop the `HomeConfig` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HomeConfigFeaturedCategory` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HomeConfigProduct` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `HomeConfigSection` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "CategoryType" AS ENUM ('COLLECTION', 'CATEGORY', 'SEGMENT');

-- DropForeignKey
ALTER TABLE "HomeConfig" DROP CONSTRAINT "HomeConfig_warehouseId_fkey";

-- DropForeignKey
ALTER TABLE "HomeConfigFeaturedCategory" DROP CONSTRAINT "HomeConfigFeaturedCategory_categoryId_fkey";

-- DropForeignKey
ALTER TABLE "HomeConfigFeaturedCategory" DROP CONSTRAINT "HomeConfigFeaturedCategory_homeConfigId_fkey";

-- DropForeignKey
ALTER TABLE "HomeConfigFeaturedCategory" DROP CONSTRAINT "HomeConfigFeaturedCategory_sectionId_fkey";

-- DropForeignKey
ALTER TABLE "HomeConfigProduct" DROP CONSTRAINT "HomeConfigProduct_productId_fkey";

-- DropForeignKey
ALTER TABLE "HomeConfigProduct" DROP CONSTRAINT "HomeConfigProduct_sectionId_fkey";

-- DropForeignKey
ALTER TABLE "HomeConfigSection" DROP CONSTRAINT "HomeConfigSection_categoryId_fkey";

-- DropForeignKey
ALTER TABLE "HomeConfigSection" DROP CONSTRAINT "HomeConfigSection_homeConfigId_fkey";

-- AlterTable
ALTER TABLE "Category" ADD COLUMN     "type" "CategoryType" NOT NULL DEFAULT 'SEGMENT';

-- DropTable
DROP TABLE "HomeConfig";

-- DropTable
DROP TABLE "HomeConfigFeaturedCategory";

-- DropTable
DROP TABLE "HomeConfigProduct";

-- DropTable
DROP TABLE "HomeConfigSection";
