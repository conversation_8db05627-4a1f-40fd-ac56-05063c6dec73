/*
  Warnings:

  - A unique constraint covering the columns `[referralCode]` on the table `User` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "User" ADD COLUMN     "referralCode" TEXT,
ADD COLUMN     "referredById" INTEGER;

-- CreateTable
CREATE TABLE "TierProductDiscount" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "id" SERIAL NOT NULL,
    "tierId" INTEGER NOT NULL,
    "productId" INTEGER NOT NULL,
    "discountValue" DOUBLE PRECISION NOT NULL,
    "discountType" "AmountType" NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "TierProductDiscount_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RelatedProduct" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "productId" INTEGER NOT NULL,
    "relatedProductId" INTEGER NOT NULL,

    CONSTRAINT "RelatedProduct_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HomeConfig" (
    "id" SERIAL NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "warehouseId" INTEGER,

    CONSTRAINT "HomeConfig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HomeConfigSection" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "homeConfigId" INTEGER,
    "categoryId" INTEGER,

    CONSTRAINT "HomeConfigSection_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HomeConfigProduct" (
    "id" SERIAL NOT NULL,
    "displayOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "sectionId" INTEGER NOT NULL,
    "productId" INTEGER NOT NULL,

    CONSTRAINT "HomeConfigProduct_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "HomeConfigFeaturedCategory" (
    "id" SERIAL NOT NULL,
    "displayOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "homeConfigId" INTEGER NOT NULL,
    "categoryId" INTEGER NOT NULL,
    "sectionId" INTEGER,

    CONSTRAINT "HomeConfigFeaturedCategory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "TierProductDiscount_tierId_productId_key" ON "TierProductDiscount"("tierId", "productId");

-- CreateIndex
CREATE UNIQUE INDEX "RelatedProduct_productId_relatedProductId_key" ON "RelatedProduct"("productId", "relatedProductId");

-- CreateIndex
CREATE UNIQUE INDEX "HomeConfigSection_homeConfigId_key" ON "HomeConfigSection"("homeConfigId");

-- CreateIndex
CREATE UNIQUE INDEX "HomeConfigProduct_sectionId_productId_key" ON "HomeConfigProduct"("sectionId", "productId");

-- CreateIndex
CREATE UNIQUE INDEX "HomeConfigFeaturedCategory_sectionId_key" ON "HomeConfigFeaturedCategory"("sectionId");

-- CreateIndex
CREATE UNIQUE INDEX "HomeConfigFeaturedCategory_homeConfigId_categoryId_key" ON "HomeConfigFeaturedCategory"("homeConfigId", "categoryId");

-- CreateIndex
CREATE UNIQUE INDEX "User_referralCode_key" ON "User"("referralCode");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_referredById_fkey" FOREIGN KEY ("referredById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TierProductDiscount" ADD CONSTRAINT "TierProductDiscount_tierId_fkey" FOREIGN KEY ("tierId") REFERENCES "RewardTier"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TierProductDiscount" ADD CONSTRAINT "TierProductDiscount_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RelatedProduct" ADD CONSTRAINT "RelatedProduct_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RelatedProduct" ADD CONSTRAINT "RelatedProduct_relatedProductId_fkey" FOREIGN KEY ("relatedProductId") REFERENCES "Product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeConfig" ADD CONSTRAINT "HomeConfig_warehouseId_fkey" FOREIGN KEY ("warehouseId") REFERENCES "Warehouse"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeConfigSection" ADD CONSTRAINT "HomeConfigSection_homeConfigId_fkey" FOREIGN KEY ("homeConfigId") REFERENCES "HomeConfig"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeConfigSection" ADD CONSTRAINT "HomeConfigSection_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "Category"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeConfigProduct" ADD CONSTRAINT "HomeConfigProduct_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "HomeConfigSection"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeConfigProduct" ADD CONSTRAINT "HomeConfigProduct_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeConfigFeaturedCategory" ADD CONSTRAINT "HomeConfigFeaturedCategory_homeConfigId_fkey" FOREIGN KEY ("homeConfigId") REFERENCES "HomeConfig"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeConfigFeaturedCategory" ADD CONSTRAINT "HomeConfigFeaturedCategory_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "Category"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HomeConfigFeaturedCategory" ADD CONSTRAINT "HomeConfigFeaturedCategory_sectionId_fkey" FOREIGN KEY ("sectionId") REFERENCES "HomeConfigSection"("id") ON DELETE SET NULL ON UPDATE CASCADE;
