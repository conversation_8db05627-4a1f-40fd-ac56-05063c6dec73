-- CreateTable
CREATE TABLE "ProductAvailabilitySubscription" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userId" INTEGER NOT NULL,
    "productId" INTEGER NOT NULL,
    "notified" BO<PERSON>EAN NOT NULL DEFAULT false,

    CONSTRAINT "ProductAvailabilitySubscription_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ProductAvailabilitySubscription_productId_idx" ON "ProductAvailabilitySubscription"("productId");

-- CreateIndex
CREATE UNIQUE INDEX "ProductAvailabilitySubscription_userId_productId_key" ON "ProductAvailabilitySubscription"("userId", "productId");

-- AddForeignKey
ALTER TABLE "ProductAvailabilitySubscription" ADD CONSTRAINT "ProductAvailabilitySubscription_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProductAvailabilitySubscription" ADD CONSTRAINT "ProductAvailabilitySubscription_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
