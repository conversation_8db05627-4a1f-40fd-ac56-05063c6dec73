-- CreateTable
CREATE TABLE "DeliveryDriverProfile" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" INTEGER NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "DeliveryDriverProfile_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "DeliveryDriverProfile_userId_key" ON "DeliveryDriverProfile"("userId");

-- CreateIndex
CREATE INDEX "DeliveryDriverProfile_userId_idx" ON "DeliveryDriverProfile"("userId");

-- CreateIndex
CREATE INDEX "DeliveryDriverProfile_isActive_idx" ON "DeliveryDriverProfile"("isActive");

-- AddF<PERSON>ign<PERSON>ey
ALTER TABLE "DeliveryDriverProfile" ADD CONSTRAINT "DeliveryDriverProfile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
