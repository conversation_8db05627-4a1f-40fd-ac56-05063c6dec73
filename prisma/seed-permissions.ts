import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedPermissions() {
  console.log('Seeding permission features...');

  // Create basic permission features
  const features = [
    {
      name: 'Order',
      description: 'Manage orders, view order details, update order status',
    },
    {
      name: 'Product',
      description: 'Manage products',
    },
    {
      name: 'User',
      description: 'Manage users, assign roles, view user details',
    },
    {
      name: 'Inventory',
      description: 'Manage inventory',
    },
    {
      name: 'Analytics',
      description: 'View analytics, reports, and dashboard data',
    },
    {
      name: 'Warehouse',
      description: 'Manage warehouses',
    },
    {
      name: 'Coupon',
      description: 'Manage coupons',
    },
    {
      name: 'Notification',
      description: 'Send notifications',
    },
  ];

  for (const feature of features) {
    await prisma.permissionFeature.upsert({
      where: { name: feature.name },
      update: {
        description: feature.description,
      },
      create: feature,
    });
  }

  console.log('Permission features seeded successfully!');

  // Create system roles that map to UserType
  console.log('Creating system roles...');

  const systemRoles = [
    {
      name: 'Super Admin',
      description: 'Full system access - maps to ADMIN UserType',
      isSystemRole: true,
    },
    {
      name: 'Warehouse Staff',
      description:
        'Limited access for warehouse operations - maps to WAREHOUSE_STAFF UserType',
      isSystemRole: true,
    },
    {
      name: 'Delivery Driver',
      description:
        'Access for delivery operations - maps to DELIVERY_PERSON UserType',
      isSystemRole: true,
    },
  ];

  for (const role of systemRoles) {
    await prisma.role.upsert({
      where: { name: role.name },
      update: {},
      create: role,
    });
  }

  console.log('System roles created successfully!');
}

seedPermissions()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
